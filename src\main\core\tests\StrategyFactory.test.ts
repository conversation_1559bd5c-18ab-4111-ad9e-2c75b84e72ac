/**
 * StrategyFactory Comprehensive Test Suite
 * Unit tests, integration tests, and strategy creation scenarios
 */

import { StrategyFactory } from '../StrategyFactory'
import type {
  Strategy,
  StrategyConfig,
  SingleIndicatorStrategyConfig,
  MultiIndicatorStrategyConfig,
  GeneratedSignal,
  IndicatorDataPoint
} from '../../../shared/types/signals'
import { createRSI, createSMA, createBollingerBands } from '../../indicators'

/**
 * Test constants and configuration
 */
const STRATEGY_TEST_CONFIG = {
  FLOATING_POINT_PRECISION: 0.001,
  PERFORMANCE_THRESHOLD_MS: 50,
  SIGNAL_CONFIDENCE_MIN: 0.0,
  SIGNAL_CONFIDENCE_MAX: 1.0,
  TEST_DATA_SIZE: 30
}

/**
 * Test data for strategy testing
 */
const STRATEGY_TEST_DATA = [
  { value: 100, timestamp: Date.now() },
  { value: 102, timestamp: Date.now() + 1000 },
  { value: 98, timestamp: Date.now() + 2000 },
  { value: 105, timestamp: Date.now() + 3000 },
  { value: 103, timestamp: Date.now() + 4000 },
  { value: 99, timestamp: Date.now() + 5000 },
  { value: 101, timestamp: Date.now() + 6000 },
  { value: 104, timestamp: Date.now() + 7000 },
  { value: 106, timestamp: Date.now() + 8000 },
  { value: 102, timestamp: Date.now() + 9000 }
]

/**
 * Utility functions for strategy testing
 */
const StrategyTestUtils = {
  /**
   * Reset StrategyFactory singleton for clean testing
   */
  resetStrategyFactory(): void {
    StrategyFactory.resetInstance()
  },

  /**
   * Create test market data with specified characteristics
   */
  createTestData(
    pattern: 'trending' | 'ranging' | 'volatile',
    count: number = 20
  ): IndicatorDataPoint[] {
    const baseTime = Date.now()
    const data: IndicatorDataPoint[] = []

    for (let i = 0; i < count; i++) {
      let value: number

      switch (pattern) {
        case 'trending':
          value = 100 + i * 1.5 + Math.random() * 2
          break
        case 'ranging':
          value = 100 + Math.sin(i * 0.3) * 5 + Math.random() * 2
          break
        case 'volatile':
          value = 100 + Math.sin(i * 0.8) * 15 + Math.random() * 5
          break
        default:
          value = 100
      }

      data.push({
        value,
        timestamp: baseTime + i * 1000
      })
    }

    return data
  },

  /**
   * Validate signal structure
   */
  validateSignal(signal: GeneratedSignal): boolean {
    return (
      typeof signal.signal === 'string' &&
      ['BUY', 'SELL', 'HOLD'].includes(signal.signal) &&
      typeof signal.confidence === 'number' &&
      signal.confidence >= STRATEGY_TEST_CONFIG.SIGNAL_CONFIDENCE_MIN &&
      signal.confidence <= STRATEGY_TEST_CONFIG.SIGNAL_CONFIDENCE_MAX &&
      typeof signal.timestamp === 'number' &&
      typeof signal.strategy === 'string'
    )
  }
}

/**
 * Unit Tests for StrategyFactory Core Functionality
 */
export class StrategyFactoryUnitTests {
  /**
   * Test StrategyFactory singleton pattern
   */
  static testSingletonPattern(): boolean {
    console.log('🔧 Testing StrategyFactory singleton pattern...')

    try {
      StrategyTestUtils.resetStrategyFactory()

      const factory1 = StrategyFactory.getInstance()
      const factory2 = StrategyFactory.getInstance()

      if (factory1 !== factory2) {
        console.error('❌ Singleton pattern failed - different instances returned')
        return false
      }

      console.log('✅ StrategyFactory singleton pattern test passed')
      return true
    } catch (error) {
      console.error('❌ StrategyFactory singleton pattern test error:', error)
      return false
    }
  }

  /**
   * Test single indicator strategy creation
   */
  static testSingleIndicatorStrategyCreation(): boolean {
    console.log('🔧 Testing single indicator strategy creation...')

    try {
      StrategyTestUtils.resetStrategyFactory()
      const factory = StrategyFactory.getInstance()

      // Test RSI strategy creation
      const rsiConfig: SingleIndicatorStrategyConfig = {
        name: 'test_rsi_strategy',
        description: 'Test RSI strategy',
        indicatorConfig: {
          period: 14,
          overbought: 70,
          oversold: 30
        },
        signalRules: {
          buyConditions: [
            {
              type: 'threshold',
              params: { threshold: 30, operator: 'lt' }
            }
          ],
          sellConditions: [
            {
              type: 'threshold',
              params: { threshold: 70, operator: 'gt' }
            }
          ]
        }
      }

      const rsiStrategy = factory.createSingleIndicatorStrategy('rsi', rsiConfig)

      if (!rsiStrategy || rsiStrategy.name !== 'test_rsi_strategy') {
        console.error('❌ RSI strategy creation failed')
        return false
      }

      if (!rsiStrategy.isReady) {
        console.error('❌ RSI strategy should be ready after creation')
        return false
      }

      // Test SMA strategy creation
      const smaConfig: SingleIndicatorStrategyConfig = {
        name: 'test_sma_strategy',
        description: 'Test SMA strategy',
        indicatorConfig: {
          period: 20
        },
        signalRules: {
          buyConditions: [
            {
              type: 'crossover',
              params: { reference: 'sma', direction: 'cross_above' }
            }
          ],
          sellConditions: [
            {
              type: 'crossover',
              params: { reference: 'sma', direction: 'cross_below' }
            }
          ]
        }
      }

      const smaStrategy = factory.createSingleIndicatorStrategy('sma', smaConfig)

      if (!smaStrategy || smaStrategy.name !== 'test_sma_strategy') {
        console.error('❌ SMA strategy creation failed')
        return false
      }

      console.log('✅ Single indicator strategy creation test passed')
      return true
    } catch (error) {
      console.error('❌ Single indicator strategy creation test error:', error)
      return false
    }
  }

  /**
   * Test multi-indicator strategy creation
   */
  static testMultiIndicatorStrategyCreation(): boolean {
    console.log('🔧 Testing multi-indicator strategy creation...')

    try {
      StrategyTestUtils.resetStrategyFactory()
      const factory = StrategyFactory.getInstance()

      const multiConfig: MultiIndicatorStrategyConfig = {
        name: 'test_multi_strategy',
        description: 'Test multi-indicator strategy',
        indicatorConfigs: {
          rsi: {
            period: 14,
            overbought: 70,
            oversold: 30
          },
          sma: {
            period: 20
          }
        },
        signalRules: {
          buyConditions: [
            {
              type: 'threshold',
              params: { threshold: 30, operator: 'lt' }
            }
          ],
          sellConditions: [
            {
              type: 'threshold',
              params: { threshold: 70, operator: 'gt' }
            }
          ]
        },
        combinationLogic: 'AND',
        weights: [
          { indicator: 'rsi', weight: 0.6 },
          { indicator: 'sma', weight: 0.4 }
        ]
      }

      const multiStrategy = factory.createMultiIndicatorStrategy(['rsi', 'sma'], multiConfig)

      if (!multiStrategy || multiStrategy.name !== 'test_multi_strategy') {
        console.error('❌ Multi-indicator strategy creation failed')
        return false
      }

      if (!multiStrategy.isReady) {
        console.error('❌ Multi-indicator strategy should be ready after creation')
        return false
      }

      console.log('✅ Multi-indicator strategy creation test passed')
      return true
    } catch (error) {
      console.error('❌ Multi-indicator strategy creation test error:', error)
      return false
    }
  }

  /**
   * Test strategy validation
   */
  static testStrategyValidation(): boolean {
    console.log('🔧 Testing strategy validation...')

    try {
      StrategyTestUtils.resetStrategyFactory()
      const factory = StrategyFactory.getInstance()

      // Test invalid indicator name
      try {
        const invalidConfig: SingleIndicatorStrategyConfig = {
          name: 'invalid_strategy',
          description: 'Invalid strategy',
          indicatorConfig: { period: 14 },
          signalRules: {
            buyConditions: [],
            sellConditions: []
          }
        }

        factory.createSingleIndicatorStrategy('invalid_indicator', invalidConfig)
        console.error('❌ Should have thrown error for invalid indicator')
        return false
      } catch (error) {
        // Expected error
      }

      // Test invalid configuration
      try {
        const invalidConfig: SingleIndicatorStrategyConfig = {
          name: 'invalid_config_strategy',
          description: 'Invalid config strategy',
          indicatorConfig: { period: -1 }, // Invalid period
          signalRules: {
            buyConditions: [],
            sellConditions: []
          }
        }

        factory.createSingleIndicatorStrategy('rsi', invalidConfig)
        console.error('❌ Should have thrown error for invalid configuration')
        return false
      } catch (error) {
        // Expected error
      }

      console.log('✅ Strategy validation test passed')
      return true
    } catch (error) {
      console.error('❌ Strategy validation test error:', error)
      return false
    }
  }

  /**
   * Test strategy signal generation
   */
  static testStrategySignalGeneration(): boolean {
    console.log('🔧 Testing strategy signal generation...')

    try {
      StrategyTestUtils.resetStrategyFactory()
      const factory = StrategyFactory.getInstance()

      // Create a simple RSI strategy
      const config: SingleIndicatorStrategyConfig = {
        name: 'signal_test_strategy',
        description: 'Strategy for signal testing',
        indicatorConfig: {
          period: 5, // Short period for quick testing
          oversold: 30
        },
        signalRules: {
          buyConditions: [
            {
              type: 'threshold',
              params: { threshold: 30, operator: 'lt' }
            }
          ],
          sellConditions: [
            {
              type: 'threshold',
              params: { threshold: 70, operator: 'gt' }
            }
          ]
        }
      }

      const strategy = factory.createSingleIndicatorStrategy('rsi', config)

      // Process test data
      const testData = StrategyTestUtils.createTestData('volatile', 15)
      let signalGenerated = false

      for (const dataPoint of testData) {
        const signal = strategy.addData(dataPoint)
        if (signal) {
          signalGenerated = true

          // Validate signal structure
          if (!StrategyTestUtils.validateSignal(signal)) {
            console.error('❌ Invalid signal structure:', signal)
            return false
          }

          console.log(
            `   Generated signal: ${signal.signal} (confidence: ${(signal.confidence * 100).toFixed(1)}%)`
          )
          break
        }
      }

      console.log('✅ Strategy signal generation test passed')
      return true
    } catch (error) {
      console.error('❌ Strategy signal generation test error:', error)
      return false
    }
  }
}

/**
 * Integration Tests for StrategyFactory with Complex Scenarios
 */
export class StrategyFactoryIntegrationTests {
  /**
   * Test Bollinger Bands strategy integration
   */
  static testBollingerBandsStrategy(): boolean {
    console.log('🔗 Testing Bollinger Bands strategy integration...')

    try {
      StrategyTestUtils.resetStrategyFactory()
      const factory = StrategyFactory.getInstance()

      const bbConfig: SingleIndicatorStrategyConfig = {
        name: 'bb_integration_strategy',
        description: 'Bollinger Bands integration test',
        indicatorConfig: {
          period: 10,
          standardDeviations: 2
        },
        signalRules: {
          buyConditions: [
            {
              type: 'threshold',
              params: { threshold: 'lower_band', operator: 'lt' }
            }
          ],
          sellConditions: [
            {
              type: 'threshold',
              params: { threshold: 'upper_band', operator: 'gt' }
            }
          ]
        }
      }

      const bbStrategy = factory.createSingleIndicatorStrategy('bollingerbands', bbConfig)

      if (!bbStrategy) {
        console.error('❌ Bollinger Bands strategy creation failed')
        return false
      }

      // Test with volatile data that should trigger band signals
      const volatileData = StrategyTestUtils.createTestData('volatile', 25)
      let signalCount = 0

      for (const dataPoint of volatileData) {
        const signal = bbStrategy.addData(dataPoint)
        if (signal) {
          signalCount++
          console.log(
            `   BB signal: ${signal.signal} (confidence: ${(signal.confidence * 100).toFixed(1)}%)`
          )
        }
      }

      console.log(`   Generated ${signalCount} Bollinger Bands signals`)
      console.log('✅ Bollinger Bands strategy integration test passed')
      return true
    } catch (error) {
      console.error('❌ Bollinger Bands strategy integration test error:', error)
      return false
    }
  }

  /**
   * Test weighted multi-indicator strategy
   */
  static testWeightedMultiIndicatorStrategy(): boolean {
    console.log('🔗 Testing weighted multi-indicator strategy...')

    try {
      StrategyTestUtils.resetStrategyFactory()
      const factory = StrategyFactory.getInstance()

      const weightedConfig: MultiIndicatorStrategyConfig = {
        name: 'weighted_multi_strategy',
        description: 'Weighted multi-indicator test',
        indicatorConfigs: {
          rsi: { period: 10 },
          sma: { period: 15 },
          bollingerbands: { period: 20, standardDeviations: 2 }
        },
        signalRules: {
          buyConditions: [
            {
              type: 'threshold',
              params: { threshold: 30, operator: 'lt' }
            }
          ],
          sellConditions: [
            {
              type: 'threshold',
              params: { threshold: 70, operator: 'gt' }
            }
          ]
        },
        combinationLogic: 'WEIGHTED',
        weights: [
          { indicator: 'rsi', weight: 0.5 },
          { indicator: 'sma', weight: 0.3 },
          { indicator: 'bollingerbands', weight: 0.2 }
        ]
      }

      const weightedStrategy = factory.createMultiIndicatorStrategy(
        ['rsi', 'sma', 'bollingerbands'],
        weightedConfig
      )

      if (!weightedStrategy) {
        console.error('❌ Weighted multi-indicator strategy creation failed')
        return false
      }

      // Test with trending data
      const trendingData = StrategyTestUtils.createTestData('trending', 30)
      let weightedSignalCount = 0

      for (const dataPoint of trendingData) {
        const signal = weightedStrategy.addData(dataPoint)
        if (signal) {
          weightedSignalCount++

          // Weighted signals should have confidence influenced by weights
          if (signal.confidence < 0 || signal.confidence > 1) {
            console.error('❌ Invalid weighted confidence level:', signal.confidence)
            return false
          }

          console.log(
            `   Weighted signal: ${signal.signal} (confidence: ${(signal.confidence * 100).toFixed(1)}%)`
          )
        }
      }

      console.log(`   Generated ${weightedSignalCount} weighted signals`)
      console.log('✅ Weighted multi-indicator strategy test passed')
      return true
    } catch (error) {
      console.error('❌ Weighted multi-indicator strategy test error:', error)
      return false
    }
  }

  /**
   * Test strategy performance optimization
   */
  static testStrategyPerformance(): boolean {
    console.log('🔗 Testing strategy performance optimization...')

    try {
      StrategyTestUtils.resetStrategyFactory()
      const factory = StrategyFactory.getInstance()

      // Create multiple strategies
      const strategies: Strategy[] = []

      for (let i = 0; i < 5; i++) {
        const config: SingleIndicatorStrategyConfig = {
          name: `performance_strategy_${i}`,
          description: `Performance test strategy ${i}`,
          indicatorConfig: { period: 10 + i },
          signalRules: {
            buyConditions: [
              {
                type: 'threshold',
                params: { threshold: 30, operator: 'lt' }
              }
            ],
            sellConditions: [
              {
                type: 'threshold',
                params: { threshold: 70, operator: 'gt' }
              }
            ]
          }
        }

        const strategy = factory.createSingleIndicatorStrategy('rsi', config)
        if (strategy) {
          strategies.push(strategy)
        }
      }

      // Generate large dataset
      const largeDataset = StrategyTestUtils.createTestData('volatile', 500)

      const startTime = performance.now()

      let totalSignals = 0
      for (const dataPoint of largeDataset) {
        for (const strategy of strategies) {
          const signal = strategy.addData(dataPoint)
          if (signal) {
            totalSignals++
          }
        }
      }

      const endTime = performance.now()
      const processingTime = endTime - startTime

      console.log(
        `   Processed ${largeDataset.length} data points across ${strategies.length} strategies`
      )
      console.log(`   Total processing time: ${processingTime.toFixed(2)}ms`)
      console.log(`   Generated ${totalSignals} total signals`)
      console.log(
        `   Average time per strategy per data point: ${(processingTime / (strategies.length * largeDataset.length)).toFixed(4)}ms`
      )

      if (processingTime > STRATEGY_TEST_CONFIG.PERFORMANCE_THRESHOLD_MS * largeDataset.length) {
        console.warn(`⚠️  Performance slower than expected: ${processingTime.toFixed(2)}ms`)
        return false
      }

      console.log('✅ Strategy performance optimization test passed')
      return true
    } catch (error) {
      console.error('❌ Strategy performance optimization test error:', error)
      return false
    }
  }
}

/**
 * Test Suite Configuration for StrategyFactory
 */
interface StrategyTestSuite {
  name: string
  testFunction: () => boolean | Promise<boolean>
  category: 'unit' | 'integration'
}

/**
 * All StrategyFactory test suites
 */
const STRATEGY_TEST_SUITES: readonly StrategyTestSuite[] = [
  // Unit Tests
  {
    name: 'Singleton Pattern',
    testFunction: StrategyFactoryUnitTests.testSingletonPattern,
    category: 'unit'
  },
  {
    name: 'Single Indicator Strategy Creation',
    testFunction: StrategyFactoryUnitTests.testSingleIndicatorStrategyCreation,
    category: 'unit'
  },
  {
    name: 'Multi-Indicator Strategy Creation',
    testFunction: StrategyFactoryUnitTests.testMultiIndicatorStrategyCreation,
    category: 'unit'
  },
  {
    name: 'Strategy Validation',
    testFunction: StrategyFactoryUnitTests.testStrategyValidation,
    category: 'unit'
  },
  {
    name: 'Strategy Signal Generation',
    testFunction: StrategyFactoryUnitTests.testStrategySignalGeneration,
    category: 'unit'
  },

  // Integration Tests
  {
    name: 'Bollinger Bands Strategy',
    testFunction: StrategyFactoryIntegrationTests.testBollingerBandsStrategy,
    category: 'integration'
  },
  {
    name: 'Weighted Multi-Indicator Strategy',
    testFunction: StrategyFactoryIntegrationTests.testWeightedMultiIndicatorStrategy,
    category: 'integration'
  },
  {
    name: 'Strategy Performance',
    testFunction: StrategyFactoryIntegrationTests.testStrategyPerformance,
    category: 'integration'
  }
] as const

/**
 * Main test runner for StrategyFactory comprehensive test suite
 */
export async function runStrategyFactoryTests(
  category?: 'unit' | 'integration' | 'all'
): Promise<boolean> {
  console.log('🚀 Starting StrategyFactory Comprehensive Test Suite...\n')

  const testCategory = category || 'all'
  const testsToRun =
    testCategory === 'all'
      ? STRATEGY_TEST_SUITES
      : STRATEGY_TEST_SUITES.filter((test) => test.category === testCategory)

  console.log(`Running ${testsToRun.length} tests in category: ${testCategory}\n`)

  let passedTests = 0
  const totalTests = testsToRun.length
  const results: { name: string; category: string; passed: boolean; error?: string }[] = []

  for (const testSuite of testsToRun) {
    try {
      console.log(`\n📋 Running: ${testSuite.name} (${testSuite.category})`)
      console.log('─'.repeat(60))

      const result = await testSuite.testFunction()

      if (result) {
        passedTests++
        console.log(`✅ ${testSuite.name} - PASSED`)
        results.push({ name: testSuite.name, category: testSuite.category, passed: true })
      } else {
        console.log(`❌ ${testSuite.name} - FAILED`)
        results.push({ name: testSuite.name, category: testSuite.category, passed: false })
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error(`💥 ${testSuite.name} - ERROR:`, errorMessage)
      results.push({
        name: testSuite.name,
        category: testSuite.category,
        passed: false,
        error: errorMessage
      })
    }
  }

  // Generate summary
  console.log('\n' + '='.repeat(80))
  console.log('📊 STRATEGY FACTORY TEST SUITE SUMMARY')
  console.log('='.repeat(80))
  console.log(`Total Tests: ${totalTests}`)
  console.log(`Passed: ${passedTests}`)
  console.log(`Failed: ${totalTests - passedTests}`)
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`)

  // Category breakdown
  const categories = ['unit', 'integration']
  for (const cat of categories) {
    const categoryResults = results.filter((r) => r.category === cat)
    if (categoryResults.length > 0) {
      const categoryPassed = categoryResults.filter((r) => r.passed).length
      console.log(`${cat.toUpperCase()}: ${categoryPassed}/${categoryResults.length} passed`)
    }
  }

  // Failed tests details
  const failedTests = results.filter((r) => !r.passed)
  if (failedTests.length > 0) {
    console.log('\n❌ Failed Tests:')
    failedTests.forEach((test) => {
      console.log(`   - ${test.name} (${test.category})${test.error ? ': ' + test.error : ''}`)
    })
  }

  const allPassed = passedTests === totalTests
  if (allPassed) {
    console.log('\n🎉 All tests passed! StrategyFactory implementation is working correctly.')
  } else {
    console.log('\n⚠️  Some tests failed. Please review the implementation.')
  }

  return allPassed
}

/**
 * Quick test function for StrategyFactory development
 */
export const quickStrategyFactoryTest = (): boolean => {
  console.log('⚡ Running quick StrategyFactory test...')

  try {
    StrategyTestUtils.resetStrategyFactory()
    const factory = StrategyFactory.getInstance()

    // Create a simple strategy
    const config: SingleIndicatorStrategyConfig = {
      name: 'quick_test_strategy',
      description: 'Quick test',
      indicatorConfig: { period: 5 },
      signalRules: {
        buyConditions: [
          {
            type: 'threshold',
            params: { threshold: 30, operator: 'lt' }
          }
        ],
        sellConditions: [
          {
            type: 'threshold',
            params: { threshold: 70, operator: 'gt' }
          }
        ]
      }
    }

    const strategy = factory.createSingleIndicatorStrategy('rsi', config)

    if (!strategy) {
      console.error('❌ Quick test failed - strategy creation failed')
      return false
    }

    console.log(`✅ Quick test passed - Strategy "${strategy.name}" created successfully`)
    return true
  } catch (error) {
    console.error('❌ Quick test error:', error)
    return false
  }
}

/**
 * Public API for the StrategyFactory test module
 */
export const StrategyFactoryTestSuite = {
  // Main test runner
  runTests: runStrategyFactoryTests,
  quickTest: quickStrategyFactoryTest,

  // Test classes for selective testing
  UnitTests: StrategyFactoryUnitTests,
  IntegrationTests: StrategyFactoryIntegrationTests,

  // Utility functions
  TestUtils: StrategyTestUtils,

  // Test constants
  TEST_CONFIG: STRATEGY_TEST_CONFIG
} as const

// Export default for convenience
export default StrategyFactoryTestSuite
