/**
 * Signal Engine Page
 * Comprehensive Signal Engine interface with dashboard, management, and analytics
 * Following user preferences: green color scheme, circular buttons, minimal padding, WebSocket integration
 */

import React, { useState, useCallback } from 'react'
import { SignalEngineDashboard } from '../components/SignalEngine/SignalEngineDashboard'
import { SignalEngineManager } from '../components/SignalEngine/SignalEngineManager'
import { SignalEngineAnalytics } from '../components/SignalEngine/SignalEngineAnalytics'
import { useTradingContext } from '../contexts/TradingContext'
import type { Strategy } from '../../../shared/types/signals'

/**
 * Signal Engine Page Component
 */
export const SignalEnginePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'manager' | 'analytics'>('dashboard')
  const [refreshKey, setRefreshKey] = useState(0)
  const { isConnected } = useTradingContext()

  /**
   * Handle strategy creation
   */
  const handleStrategyCreated = useCallback((strategy: Strategy) => {
    // Trigger refresh of dashboard
    setRefreshKey((prev) => prev + 1)
  }, [])

  /**
   * Handle strategy deletion
   */
  const handleStrategyDeleted = useCallback((strategyId: string) => {
    // Trigger refresh of dashboard
    setRefreshKey((prev) => prev + 1)
  }, [])

  /**
   * Tab configuration
   */
  const tabs = [
    { id: 'dashboard', label: 'Dashboard', emoji: '📊', description: 'Overview & Monitoring' },
    { id: 'manager', label: 'Manager', emoji: '⚙️', description: 'Create & Manage Strategies' },
    { id: 'analytics', label: 'Analytics', emoji: '📈', description: 'Performance & Statistics' }
  ] as const

  /**
   * Render tab button
   */
  const renderTabButton = (tab: (typeof tabs)[0]) => (
    <button
      key={tab.id}
      onClick={() => setActiveTab(tab.id)}
      className={`flex-1 p-3 rounded-lg border text-sm font-medium transition-all duration-200 ${
        activeTab === tab.id
          ? 'bg-green-200 border-green-400 text-green-900 shadow-md'
          : 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100'
      }`}
    >
      <div className="text-lg mb-1">{tab.emoji}</div>
      <div className="font-semibold">{tab.label}</div>
      <div className="text-xs opacity-75">{tab.description}</div>
    </button>
  )
  /**
   * Render active tab content
   */
  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <SignalEngineDashboard key={refreshKey} className="w-full" />

      case 'manager':
        return (
          <SignalEngineManager
            className="w-full"
            onStrategyCreated={handleStrategyCreated}
            onStrategyDeleted={handleStrategyDeleted}
          />
        )

      case 'analytics':
        return <SignalEngineAnalytics key={refreshKey} className="w-full" />

      default:
        return null
    }
  }

  return (
    <div className="signal-engine-page min-h-screen bg-gradient-to-br from-green-25 to-green-50 p-4">
      <div className="max-w-7xl mx-auto space-y-4">
        {/* Page Header */}
        <div className="bg-gradient-to-r from-green-100 to-green-200 rounded-lg border border-green-300 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-green-900 flex items-center">
                <span className="mr-2">🤖</span>
                Signal Engine
              </h1>
              <p className="text-green-700 text-sm mt-1">
                Advanced trading signal generation and strategy management
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <div
                className={`px-3 py-1 rounded-full text-xs font-medium ${
                  isConnected ? 'bg-green-200 text-green-800' : 'bg-red-200 text-red-800'
                }`}
              >
                {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
              </div>
              <button
                onClick={() => setRefreshKey((prev) => prev + 1)}
                className="w-8 h-8 rounded-full bg-green-500 hover:bg-green-600 text-white flex items-center justify-center text-sm transition-all duration-200 hover:shadow-lg"
                title="Refresh All Data"
              >
                🔄
              </button>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg border border-green-200 p-4">
          <div className="grid grid-cols-3 gap-3">{tabs.map(renderTabButton)}</div>
        </div>
        {/* Tab Content */}
        <div className="min-h-[600px]">{renderTabContent()}</div>

        {/* Quick Actions Footer */}
        <div className="bg-gradient-to-r from-green-100 to-green-200 rounded-lg border border-green-300 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-semibold text-green-900">Quick Actions</h3>
              <p className="text-xs text-green-700">Common Signal Engine operations</p>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setActiveTab('manager')}
                className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-full text-sm font-medium transition-all duration-200 hover:shadow-lg"
              >
                ➕ Create Strategy
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-full text-sm font-medium transition-all duration-200 hover:shadow-lg"
              >
                📊 View Analytics
              </button>
              <button
                onClick={() => setActiveTab('dashboard')}
                className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-full text-sm font-medium transition-all duration-200 hover:shadow-lg"
              >
                🎯 Monitor Signals
              </button>
            </div>
          </div>
        </div>

        {/* Help Section */}
        <div className="bg-gradient-to-br from-green-50 to-white rounded-lg border border-green-200 p-4">
          <div className="text-center">
            <h4 className="text-sm font-semibold text-green-900 mb-2">💡 Signal Engine Help</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-green-700">
              <div className="space-y-1">
                <div className="font-medium">📊 Dashboard</div>
                <div>Monitor active strategies and recent signals in real-time</div>
              </div>
              <div className="space-y-1">
                <div className="font-medium">⚙️ Manager</div>
                <div>Create and configure RSI, SMA, and Bollinger Bands strategies</div>
              </div>
              <div className="space-y-1">
                <div className="font-medium">📈 Analytics</div>
                <div>View performance metrics and signal distribution statistics</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SignalEnginePage
