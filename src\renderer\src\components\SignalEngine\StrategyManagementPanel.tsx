/**
 * Strategy Management Panel Component
 * Interface for viewing, editing, removing, and monitoring active strategies
 */

import React, { useState, useEffect, useCallback } from 'react'
import { SignalEngine } from '../../../../main/core/SignalEngine'
import type { Strategy, StrategyConfig } from '../../../../shared/types/signals'

/**
 * Strategy management component props
 */
interface StrategyManagementPanelProps {
  /** Custom CSS classes */
  className?: string
  /** Callback when strategy is selected for editing */
  onEditStrategy?: (strategy: Strategy) => void
  /** Callback when strategy is deleted */
  onDeleteStrategy?: (strategyId: string) => void
  /** Refresh interval in milliseconds */
  refreshInterval?: number
}

/**
 * Strategy with additional management metadata
 */
interface ManagedStrategy extends Strategy {
  isActive: boolean
  lastSignalTime?: number
  signalCount: number
  performance: {
    totalSignals: number
    avgConfidence: number
    lastUpdated: number
  }
}

/**
 * Strategy Management Panel Component
 */
export const StrategyManagementPanel: React.FC<StrategyManagementPanelProps> = ({
  className = '',
  onEditStrategy,
  onDeleteStrategy,
  refreshInterval = 5000
}) => {
  // State management
  const [strategies, setStrategies] = useState<ManagedStrategy[]>([])
  const [selectedStrategy, setSelectedStrategy] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showConfirmDelete, setShowConfirmDelete] = useState<string | null>(null)

  /**
   * Load strategies from Signal Engine
   */
  const loadStrategies = useCallback(async () => {
    try {
      setError(null)
      const signalEngine = SignalEngine.getInstance()
      
      // Get active strategies
      const activeStrategies = signalEngine.getActiveStrategies()
      
      // Get performance metrics for each strategy
      const managedStrategies: ManagedStrategy[] = await Promise.all(
        activeStrategies.map(async (strategy) => {
          const recentSignals = signalEngine.getRecentSignals(100, strategy.name)
          const avgConfidence = recentSignals.length > 0
            ? recentSignals.reduce((sum, s) => sum + s.confidence, 0) / recentSignals.length
            : 0

          return {
            ...strategy,
            isActive: true,
            lastSignalTime: recentSignals[0]?.timestamp,
            signalCount: recentSignals.length,
            performance: {
              totalSignals: recentSignals.length,
              avgConfidence,
              lastUpdated: Date.now()
            }
          }
        })
      )

      setStrategies(managedStrategies)
      setIsLoading(false)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load strategies'
      setError(errorMessage)
      setIsLoading(false)
    }
  }, [])

  /**
   * Toggle strategy active state
   */
  const toggleStrategy = useCallback(async (strategyId: string) => {
    try {
      const signalEngine = SignalEngine.getInstance()
      const strategy = strategies.find(s => s.name === strategyId)
      
      if (strategy) {
        if (strategy.isActive) {
          // Pause strategy (implementation depends on Signal Engine API)
          console.log(`Pausing strategy: ${strategyId}`)
        } else {
          // Resume strategy
          console.log(`Resuming strategy: ${strategyId}`)
        }
        
        // Refresh strategies
        await loadStrategies()
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to toggle strategy')
    }
  }, [strategies, loadStrategies])

  /**
   * Delete strategy
   */
  const deleteStrategy = useCallback(async (strategyId: string) => {
    try {
      const signalEngine = SignalEngine.getInstance()
      signalEngine.removeStrategy(strategyId)
      
      // Refresh strategies
      await loadStrategies()
      
      // Notify parent component
      onDeleteStrategy?.(strategyId)
      
      setShowConfirmDelete(null)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to delete strategy')
    }
  }, [loadStrategies, onDeleteStrategy])

  /**
   * Format timestamp for display
   */
  const formatTimestamp = useCallback((timestamp?: number): string => {
    if (!timestamp) return 'Never'
    
    const now = Date.now()
    const diff = now - timestamp
    
    if (diff < 60000) return 'Just now'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`
    return `${Math.floor(diff / 86400000)}d ago`
  }, [])

  /**
   * Get strategy status color
   */
  const getStatusColor = useCallback((strategy: ManagedStrategy): string => {
    if (!strategy.isActive) return 'text-gray-500'
    if (!strategy.lastSignalTime) return 'text-yellow-600'
    
    const timeSinceLastSignal = Date.now() - strategy.lastSignalTime
    if (timeSinceLastSignal < 300000) return 'text-green-600' // 5 minutes
    if (timeSinceLastSignal < 1800000) return 'text-yellow-600' // 30 minutes
    return 'text-red-600'
  }, [])

  // Load strategies on component mount and set up refresh interval
  useEffect(() => {
    loadStrategies()
    
    const interval = setInterval(loadStrategies, refreshInterval)
    return () => clearInterval(interval)
  }, [loadStrategies, refreshInterval])

  if (isLoading) {
    return (
      <div className={`strategy-management-panel ${className}`}>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading strategies...</span>
        </div>
      </div>
    )
  }

  return (
    <div className={`strategy-management-panel bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-semibold text-gray-900">⚙️ Strategy Management</h3>
            <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
              {strategies.length} Active
            </span>
          </div>
          <button
            onClick={loadStrategies}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
            title="Refresh strategies"
          >
            🔄
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mx-6 mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <span className="text-red-600 text-xl mr-2">⚠️</span>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      )}

      {/* Strategy List */}
      <div className="p-6">
        {strategies.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📊</div>
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Active Strategies</h4>
            <p className="text-gray-500">Create your first strategy to get started with signal generation</p>
          </div>
        ) : (
          <div className="space-y-4">
            {strategies.map((strategy) => (
              <div
                key={strategy.name}
                className={`strategy-card p-4 border rounded-lg transition-all duration-200 ${
                  selectedStrategy === strategy.name
                    ? 'border-green-300 bg-green-50'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                }`}
              >
                <div className="flex items-center justify-between">
                  {/* Strategy Info */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="font-medium text-gray-900">{strategy.name}</h4>
                      <span className={`text-sm font-medium ${getStatusColor(strategy)}`}>
                        {strategy.isActive ? '🟢 Active' : '🔴 Paused'}
                      </span>
                    </div>
                    
                    {strategy.description && (
                      <p className="text-sm text-gray-600 mb-3">{strategy.description}</p>
                    )}

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Signals:</span>
                        <span className="ml-1 font-medium">{strategy.performance.totalSignals}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Avg Confidence:</span>
                        <span className="ml-1 font-medium">
                          {(strategy.performance.avgConfidence * 100).toFixed(1)}%
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">Last Signal:</span>
                        <span className="ml-1 font-medium">{formatTimestamp(strategy.lastSignalTime)}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Type:</span>
                        <span className="ml-1 font-medium">
                          {Array.isArray(strategy.indicators) ? 'Multi' : 'Single'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => setSelectedStrategy(
                        selectedStrategy === strategy.name ? null : strategy.name
                      )}
                      className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
                      title="View details"
                    >
                      {selectedStrategy === strategy.name ? '🔼' : '🔽'}
                    </button>
                    
                    <button
                      onClick={() => toggleStrategy(strategy.name)}
                      className={`p-2 rounded-lg transition-colors ${
                        strategy.isActive
                          ? 'text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50'
                          : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                      }`}
                      title={strategy.isActive ? 'Pause strategy' : 'Resume strategy'}
                    >
                      {strategy.isActive ? '⏸️' : '▶️'}
                    </button>

                    {onEditStrategy && (
                      <button
                        onClick={() => onEditStrategy(strategy)}
                        className="p-2 text-blue-600 hover:text-blue-700 rounded-lg hover:bg-blue-50 transition-colors"
                        title="Edit strategy"
                      >
                        ✏️
                      </button>
                    )}

                    <button
                      onClick={() => setShowConfirmDelete(strategy.name)}
                      className="p-2 text-red-600 hover:text-red-700 rounded-lg hover:bg-red-50 transition-colors"
                      title="Delete strategy"
                    >
                      🗑️
                    </button>
                  </div>
                </div>

                {/* Expanded Details */}
                {selectedStrategy === strategy.name && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <h6 className="font-medium text-gray-700 mb-2">Configuration</h6>
                        <div className="space-y-1">
                          <div>
                            <span className="text-gray-500">Indicators:</span>
                            <span className="ml-1">
                              {Array.isArray(strategy.indicators) 
                                ? strategy.indicators.join(', ') 
                                : strategy.indicators || 'Unknown'}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">Created:</span>
                            <span className="ml-1">{formatTimestamp(strategy.createdAt)}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h6 className="font-medium text-gray-700 mb-2">Performance</h6>
                        <div className="space-y-1">
                          <div>
                            <span className="text-gray-500">Ready:</span>
                            <span className="ml-1">{strategy.isReady ? '✅ Yes' : '❌ No'}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Last Updated:</span>
                            <span className="ml-1">{formatTimestamp(strategy.performance.lastUpdated)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showConfirmDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Confirm Delete</h4>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete the strategy "{showConfirmDelete}"? This action cannot be undone.
            </p>
            <div className="flex items-center justify-end space-x-3">
              <button
                onClick={() => setShowConfirmDelete(null)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => deleteStrategy(showConfirmDelete)}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default StrategyManagementPanel
