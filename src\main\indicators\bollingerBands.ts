/**
 * Bollinger Bands Indicator
 * High-performance implementation with configurable standard deviations, moving average types,
 * and comprehensive band analysis capabilities
 */

import { BaseIndicatorClass } from './BaseIndicator'
import { logger } from '../../shared/utils/logger'
import {
  INDICATOR_CONSTANTS,
  INDICATOR_LOG_CATEGORIES,
  DEFAULT_BOLLINGER_BANDS_CONFIG
} from '../../shared/constants/indicators'
import type {
  BollingerBandsIndicator,
  BollingerBandsConfig,
  BollingerBandsOutputPoint,
  IndicatorDataPoint,
  OHLCDataPoint,
  IndicatorOutputPoint,
  IndicatorStreamUpdate
} from '../../shared/types/indicators'

/**
 * Bollinger Bands indicator implementation
 * Calculates upper band, middle band (moving average), and lower band
 * Bands = Middle Band ± (Standard Deviation × Multiplier)
 */
export class BollingerBands extends BaseIndicatorClass implements BollingerBandsIndicator {
  private _windowValues: number[] = []
  private _currentSum: number = 0
  private _streamingEnabled: boolean = false
  private _streamUpdateCallbacks: ((update: IndicatorStreamUpdate) => void)[] = []

  /**
   * Create a new Bollinger Bands indicator
   * @param config - Configuration for the indicator
   */
  constructor(config: BollingerBandsConfig) {
    super('BollingerBands', { ...DEFAULT_BOLLINGER_BANDS_CONFIG, ...config })
    this._validateBollingerBandsConfig()

    logger.debug(
      INDICATOR_LOG_CATEGORIES.CALCULATION,
      `Bollinger Bands indicator initialized with period ${this._config.period}, std dev multiplier ${this.config.standardDeviations}`
    )
  }

  /**
   * Get Bollinger Bands-specific configuration
   */
  public get config(): BollingerBandsConfig {
    return this._config as BollingerBandsConfig
  }

  /**
   * Calculate Bollinger Bands for a specific index
   * @param index - Index in the data points array
   * @returns Calculated Bollinger Bands output point or null
   */
  protected _calculateSingleValue(index: number): BollingerBandsOutputPoint | null {
    if (index < 0 || index >= this._dataPoints.length) {
      return null
    }

    // Check if we have enough data points for this index
    if (index < this._config.period - 1) {
      return null
    }

    const dataPoint = this._dataPoints[index]

    // Calculate moving average (middle band)
    let sum = 0
    const values: number[] = []

    for (let i = index - this._config.period + 1; i <= index; i++) {
      const value = this._extractPriceValue(this._dataPoints[i])
      values.push(value)
      sum += value
    }

    const middleBand = this.config.useEMA
      ? this._calculateEMAFromValues(values)
      : sum / this._config.period

    // Calculate standard deviation
    const variance =
      values.reduce((acc, value) => {
        const diff = value - middleBand
        return acc + diff * diff
      }, 0) / this._config.period

    const standardDeviation = Math.sqrt(variance)
    const multiplier = this.config.standardDeviations || 2

    // Calculate bands
    const upperBand = middleBand + standardDeviation * multiplier
    const lowerBand = middleBand - standardDeviation * multiplier

    return {
      value: middleBand, // Main value is the middle band
      upperBand,
      middleBand,
      lowerBand,
      standardDeviation,
      timestamp: dataPoint.timestamp || Date.now(),
      index
    }
  }

  /**
   * Override addData to implement optimized rolling calculation
   * @param dataPoint - The data point to add
   * @returns The calculated Bollinger Bands values or null if not enough data
   */
  public addData(dataPoint: IndicatorDataPoint): BollingerBandsOutputPoint | null {
    const startTime = performance.now()

    try {
      // Validate input if enabled
      if (this._config.validateInputs) {
        const validation = this.validateInput([dataPoint])
        if (!validation.isValid) {
          const error = `Invalid data point: ${validation.errors.join(', ')}`
          this._emitEvent('error-occurred', { error, dataPoint })
          throw new Error(error)
        }
      }

      // Process the data point (add timestamp if missing)
      const processedDataPoint: IndicatorDataPoint = {
        ...dataPoint,
        timestamp: dataPoint.timestamp || Date.now()
      }

      const currentValue = this._extractPriceValue(processedDataPoint)

      // Add to data points
      this._dataPoints.push(processedDataPoint)
      this._operationCount++

      // Update rolling window for optimized calculation
      this._windowValues.push(currentValue)
      this._currentSum += currentValue

      // Maintain rolling window
      if (this._windowValues.length > this._config.period) {
        const removedValue = this._windowValues.shift()!
        this._currentSum -= removedValue
      }

      // Manage memory
      this._manageMemory()

      // Calculate new value if we have enough data
      let outputValue: BollingerBandsOutputPoint | null = null
      if (this._windowValues.length >= this._config.period) {
        outputValue = this._calculateOptimizedBollingerBands(processedDataPoint)
        if (outputValue) {
          this._outputValues.push(outputValue)
          this._isReady = true
          this._emitEvent('value-calculated', { outputValue, dataPoint: processedDataPoint })

          // Handle streaming updates
          if (this._streamingEnabled) {
            this._emitStreamUpdate(processedDataPoint, outputValue, true)
          }
        }
      }

      // Update performance metrics
      this._lastCalculationTime = performance.now() - startTime

      return outputValue
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error(
        INDICATOR_LOG_CATEGORIES.ERROR,
        `Bollinger Bands calculation failed: ${errorMessage}`
      )
      this._emitEvent('error-occurred', { error: errorMessage, dataPoint })
      throw error
    }
  }

  /**
   * Calculate optimized Bollinger Bands using rolling window
   * @param dataPoint - Current data point
   * @returns Calculated Bollinger Bands output point
   */
  private _calculateOptimizedBollingerBands(
    dataPoint: IndicatorDataPoint
  ): BollingerBandsOutputPoint {
    // Calculate middle band (moving average)
    const middleBand = this.config.useEMA
      ? this._calculateEMAFromWindow()
      : this._currentSum / this._config.period

    // Calculate standard deviation
    const variance =
      this._windowValues.reduce((acc, value) => {
        const diff = value - middleBand
        return acc + diff * diff
      }, 0) / this._config.period

    const standardDeviation = Math.sqrt(variance)
    const multiplier = this.config.standardDeviations || 2

    // Calculate bands
    const upperBand = middleBand + standardDeviation * multiplier
    const lowerBand = middleBand - standardDeviation * multiplier

    return {
      value: middleBand, // Main value is the middle band
      upperBand,
      middleBand,
      lowerBand,
      standardDeviation,
      timestamp: dataPoint.timestamp || Date.now(),
      index: this._dataPoints.length - 1
    }
  }

  /**
   * Calculate Exponential Moving Average from current window
   * @returns EMA value
   */
  private _calculateEMAFromWindow(): number {
    if (this._windowValues.length === 0) return 0

    const smoothingFactor = this.config.smoothingFactor || 0.1
    let ema = this._windowValues[0]

    for (let i = 1; i < this._windowValues.length; i++) {
      ema = smoothingFactor * this._windowValues[i] + (1 - smoothingFactor) * ema
    }

    return ema
  }

  /**
   * Extract price value based on configured price type
   * @param dataPoint - Input data point
   * @returns Extracted price value
   */
  private _extractPriceValue(dataPoint: IndicatorDataPoint): number {
    const priceType = this.config.priceType || 'close'
    const ohlcData = dataPoint as OHLCDataPoint

    switch (priceType) {
      case 'close': {
        return ohlcData.close !== undefined ? ohlcData.close : dataPoint.value
      }
      case 'open': {
        return ohlcData.open !== undefined ? ohlcData.open : dataPoint.value
      }
      case 'high': {
        return ohlcData.high !== undefined ? ohlcData.high : dataPoint.value
      }
      case 'low': {
        return ohlcData.low !== undefined ? ohlcData.low : dataPoint.value
      }
      case 'typical': {
        if (
          ohlcData.high !== undefined &&
          ohlcData.low !== undefined &&
          ohlcData.close !== undefined
        ) {
          return (ohlcData.high + ohlcData.low + ohlcData.close) / 3
        }
        return dataPoint.value
      }
      case 'weighted': {
        if (
          ohlcData.high !== undefined &&
          ohlcData.low !== undefined &&
          ohlcData.close !== undefined
        ) {
          return (ohlcData.high + ohlcData.low + 2 * ohlcData.close) / 4
        }
        return dataPoint.value
      }
      default: {
        return dataPoint.value
      }
    }
  }

  /**
   * Get the current Bollinger Bands values without adding new data
   * @returns Current Bollinger Bands values or null if not enough data
   */
  public getCurrentBands(): BollingerBandsOutputPoint | null {
    if (!this._isReady || this._outputValues.length === 0) {
      return null
    }
    return this._outputValues[this._outputValues.length - 1] as BollingerBandsOutputPoint
  }

  /**
   * Get the current middle band (moving average) value
   * @returns Current middle band value
   */
  public getCurrentMiddleBand(): number | null {
    const currentBands = this.getCurrentBands()
    return currentBands ? currentBands.middleBand : null
  }

  /**
   * Get the current upper band value
   * @returns Current upper band value
   */
  public getCurrentUpperBand(): number | null {
    const currentBands = this.getCurrentBands()
    return currentBands ? currentBands.upperBand : null
  }

  /**
   * Get the current lower band value
   * @returns Current lower band value
   */
  public getCurrentLowerBand(): number | null {
    const currentBands = this.getCurrentBands()
    return currentBands ? currentBands.lowerBand : null
  }

  /**
   * Get the current standard deviation
   * @returns Current standard deviation value
   */
  public getCurrentStandardDeviation(): number | null {
    const currentBands = this.getCurrentBands()
    return currentBands ? currentBands.standardDeviation : null
  }

  /**
   * Check if current price is above upper band (potential sell signal)
   * @param currentPrice - Current price to check
   * @returns True if price is above upper band
   */
  public isPriceAboveUpperBand(currentPrice: number): boolean {
    const upperBand = this.getCurrentUpperBand()
    return upperBand !== null && currentPrice > upperBand
  }

  /**
   * Check if current price is below lower band (potential buy signal)
   * @param currentPrice - Current price to check
   * @returns True if price is below lower band
   */
  public isPriceBelowLowerBand(currentPrice: number): boolean {
    const lowerBand = this.getCurrentLowerBand()
    return lowerBand !== null && currentPrice < lowerBand
  }

  /**
   * Get the band width (upper band - lower band)
   * @returns Current band width or null if not enough data
   */
  public getBandWidth(): number | null {
    const currentBands = this.getCurrentBands()
    if (!currentBands) return null
    return currentBands.upperBand - currentBands.lowerBand
  }

  /**
   * Get the %B indicator (position of price within bands)
   * @param currentPrice - Current price
   * @returns %B value (0-1, where 0.5 is middle band)
   */
  public getPercentB(currentPrice: number): number | null {
    const currentBands = this.getCurrentBands()
    if (!currentBands) return null

    const bandWidth = currentBands.upperBand - currentBands.lowerBand
    if (bandWidth === 0) return 0.5 // If bands are flat, return middle

    return (currentPrice - currentBands.lowerBand) / bandWidth
  }

  /**
   * Enable real-time streaming updates
   * @param callback - Callback function for stream updates
   */
  public enableStreaming(callback?: (update: IndicatorStreamUpdate) => void): void {
    this._streamingEnabled = true
    if (callback) {
      this._streamUpdateCallbacks.push(callback)
    }
    this._emitEvent('stream-started', { timestamp: Date.now() })

    logger.debug(
      INDICATOR_LOG_CATEGORIES.CALCULATION,
      `Bollinger Bands streaming enabled with ${this._streamUpdateCallbacks.length} callbacks`
    )
  }

  /**
   * Disable real-time streaming updates
   */
  public disableStreaming(): void {
    this._streamingEnabled = false
    this._streamUpdateCallbacks.length = 0
    this._emitEvent('stream-stopped', { timestamp: Date.now() })

    logger.debug(INDICATOR_LOG_CATEGORIES.CALCULATION, 'Bollinger Bands streaming disabled')
  }

  /**
   * Emit stream update to all registered callbacks
   * @param dataPoint - New data point
   * @param indicatorValue - Updated indicator value
   * @param isNewValue - Whether this is a new value or update
   */
  private _emitStreamUpdate(
    dataPoint: IndicatorDataPoint,
    indicatorValue: IndicatorOutputPoint,
    isNewValue: boolean
  ): void {
    const update: IndicatorStreamUpdate = {
      dataPoint,
      indicatorValue,
      isNewValue,
      updateTimestamp: Date.now()
    }

    this._streamUpdateCallbacks.forEach((callback) => {
      try {
        callback(update)
      } catch (error) {
        logger.error(
          INDICATOR_LOG_CATEGORIES.ERROR,
          `Bollinger Bands stream callback error: ${error instanceof Error ? error.message : String(error)}`
        )
      }
    })
  }

  /**
   * Validate Bollinger Bands-specific configuration
   */
  private _validateBollingerBandsConfig(): void {
    const config = this.config

    // Validate standard deviations multiplier
    if (config.standardDeviations !== undefined) {
      const constraints = INDICATOR_CONSTANTS.VALIDATION_CONSTRAINTS.standardDeviations
      if (
        config.standardDeviations < constraints.min ||
        config.standardDeviations > constraints.max
      ) {
        throw new Error(
          `Standard deviations multiplier must be between ${constraints.min} and ${constraints.max}, got ${config.standardDeviations}`
        )
      }
    }

    // Validate smoothing factor if EMA is enabled
    if (config.useEMA && config.smoothingFactor !== undefined) {
      const constraints = INDICATOR_CONSTANTS.VALIDATION_CONSTRAINTS.smoothingFactor
      if (config.smoothingFactor < constraints.min || config.smoothingFactor > constraints.max) {
        throw new Error(
          `Smoothing factor must be between ${constraints.min} and ${constraints.max}, got ${config.smoothingFactor}`
        )
      }
    }
  }

  /**
   * Override base validation to include Bollinger Bands-specific checks
   */
  protected _validateConfig(): void {
    super._validateConfig()
    this._validateBollingerBandsConfig()
  }

  /**
   * Reset the indicator to initial state
   */
  public reset(): void {
    super.reset()
    this._windowValues = []
    this._currentSum = 0
    this._streamingEnabled = false
    this._streamUpdateCallbacks.length = 0

    logger.debug(INDICATOR_LOG_CATEGORIES.CALCULATION, 'Bollinger Bands indicator reset')
  }
}

/**
 * Factory function to create a new Bollinger Bands indicator
 * @param config - Configuration for the indicator
 * @returns New Bollinger Bands indicator instance
 */
export const createBollingerBands = (
  config: Partial<BollingerBandsConfig> = {}
): BollingerBands => {
  return new BollingerBands({ ...DEFAULT_BOLLINGER_BANDS_CONFIG, ...config })
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use BollingerBands class or createBollingerBands factory function instead
 * @param prices - Array of price values
 * @param period - Period for calculation
 * @param standardDeviations - Standard deviation multiplier
 * @returns Array of Bollinger Bands values
 */
export const calculateBollingerBands = (
  prices: number[],
  period: number = 20,
  standardDeviations: number = 2
): BollingerBandsOutputPoint[] => {
  logger.warn(
    INDICATOR_LOG_CATEGORIES.CALCULATION,
    'Using deprecated calculateBollingerBands function. Consider using BollingerBands class for better performance.'
  )

  if (prices.length < period) return []

  const bb = createBollingerBands({ period, standardDeviations })
  const dataPoints: IndicatorDataPoint[] = prices.map((price, index) => ({
    value: price,
    timestamp: Date.now() + index
  }))

  bb.addDataBatch(dataPoints)
  return bb.getValues() as BollingerBandsOutputPoint[]
}
