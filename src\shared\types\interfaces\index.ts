/**
 * Centralized interface exports for the Signal Engine system
 * This file provides a single point of import for all interfaces
 */

// Core Signal Engine interfaces
export type {
  Strategy,
  StrategyConfig,
  SingleIndicatorStrategyConfig,
  MultiIndicatorStrategyConfig,
  GeneratedSignal,
  SignalEnginePerformanceMetrics,
  SignalRule,
  SignalCondition,
  IndicatorWeight
} from '../signals'

// Indicator interfaces
export type {
  IndicatorDataPoint,
  IndicatorConfig,
  BaseIndicatorConfig,
  RSIConfig,
  SMAConfig,
  BollingerBandsConfig,
  IndicatorResult,
  IndicatorMetadata
} from '../indicators'

// Backtesting interfaces
export type {
  BacktestConfig,
  BacktestResult,
  BacktestMetrics,
  TradeRecord,
  PositionSizerConfig,
  RiskManagerConfig,
  MarketSimulatorConfig,
  ExecutionConfig
} from '../backtesting'

// Persistence interfaces
export type {
  PersistenceConfig,
  PersistenceResult,
  SignalQuery,
  PerformanceSnapshot,
  StorageMetrics
} from '../persistence'

// Trading interfaces
export type {
  TradingConfig,
  Position,
  Order,
  OrderType,
  OrderStatus,
  MarketData,
  Portfolio,
  TradingMetrics
} from '../trading'

// Broker interfaces
export type {
  BrokerConfig,
  BrokerConnection,
  BrokerMetrics,
  HeartbeatConfig,
  ConnectionStatus,
  BrokerEvent
} from '../broker'

// UI Component interfaces
export interface ComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface SignalEngineComponentProps extends ComponentProps {
  refreshInterval?: number
  autoRefresh?: boolean
}

export interface ChartComponentProps extends ComponentProps {
  data: any[]
  width?: number
  height?: number
  showLegend?: boolean
}

export interface ModalComponentProps extends ComponentProps {
  isOpen: boolean
  onClose: () => void
  title?: string
}

// Form interfaces
export interface FormFieldProps {
  label: string
  name: string
  type?: 'text' | 'number' | 'select' | 'checkbox' | 'range'
  value: any
  onChange: (value: any) => void
  error?: string
  required?: boolean
  disabled?: boolean
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  min?: number
  max?: number
  step?: number
}

export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  custom?: (value: any) => string | null
}

export interface FormValidation {
  [fieldName: string]: ValidationRule[]
}

// Event interfaces
export interface SignalEngineEvent {
  type: string
  timestamp: number
  data: any
  source: string
}

export interface WebSocketEvent extends SignalEngineEvent {
  connectionId: string
  channel: string
}

// State management interfaces
export interface AppState {
  signalEngine: SignalEngineState
  ui: UIState
  settings: SettingsState
}

export interface SignalEngineState {
  isInitialized: boolean
  strategies: Strategy[]
  recentSignals: GeneratedSignal[]
  metrics: SignalEnginePerformanceMetrics | null
  isProcessing: boolean
  error: string | null
}

export interface UIState {
  theme: 'light' | 'dark'
  sidebarCollapsed: boolean
  activeTab: string
  notifications: Notification[]
  modals: { [key: string]: boolean }
}

export interface SettingsState {
  autoSave: boolean
  refreshInterval: number
  maxSignalsDisplay: number
  enableNotifications: boolean
  debugMode: boolean
}

export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: number
  autoClose?: boolean
  duration?: number
}

// API interfaces
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  metadata?: {
    timestamp: number
    duration: number
    version: string
  }
}

export interface APIRequest {
  endpoint: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  body?: any
  timeout?: number
}

// Utility interfaces
export interface TimeRange {
  start: number
  end: number
}

export interface Pagination {
  page: number
  limit: number
  total: number
  hasNext: boolean
  hasPrev: boolean
}

export interface SortConfig {
  field: string
  direction: 'asc' | 'desc'
}

export interface FilterConfig {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'in'
  value: any
}

// Performance monitoring interfaces
export interface PerformanceMetric {
  name: string
  value: number
  unit: string
  timestamp: number
  tags?: Record<string, string>
}

export interface SystemHealth {
  cpu: number
  memory: number
  disk: number
  network: number
  timestamp: number
}

// Error handling interfaces
export interface ErrorInfo {
  code: string
  message: string
  stack?: string
  context?: Record<string, any>
  timestamp: number
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

// Configuration interfaces
export interface AppConfig {
  environment: 'development' | 'production' | 'test'
  version: string
  features: {
    [featureName: string]: boolean
  }
  limits: {
    maxStrategies: number
    maxSignalsHistory: number
    maxBacktestDuration: number
  }
  endpoints: {
    api: string
    websocket: string
  }
}

// Export all as a namespace for convenience
export namespace SignalEngineInterfaces {
  export type {
    Strategy,
    GeneratedSignal,
    IndicatorDataPoint,
    BacktestResult,
    PersistenceResult,
    ComponentProps,
    FormFieldProps,
    SignalEngineEvent,
    AppState,
    APIResponse,
    PerformanceMetric,
    ErrorInfo,
    AppConfig
  }
}
