/**
 * Technical Indicators Usage Examples
 * Comprehensive examples demonstrating the indicator system capabilities
 */

import { SimpleMovingAverage, createSMA, IndicatorUtils, indicatorRegistry } from './index'
import type { IndicatorDataPoint, OHLCDataPoint } from '../../shared/types/indicators'
import { logger } from '../../shared/utils/logger'

/**
 * Example 1: Basic SMA Usage
 * Demonstrates simple moving average calculation with basic configuration
 */
export function basicSMAExample(): void {
  console.log('\n=== Basic SMA Example ===')

  // Create SMA with 5-period
  const sma = createSMA({ period: 5 })

  // Sample price data
  const prices = [100, 102, 98, 105, 103, 99, 101, 104, 97, 106]

  console.log('Adding price data:')
  prices.forEach((price, index) => {
    const dataPoint: IndicatorDataPoint = {
      value: price,
      timestamp: Date.now() + index * 1000
    }

    const result = sma.addData(dataPoint)
    if (result) {
      console.log(`Price: ${price}, SMA(5): ${result.value.toFixed(2)}`)
    } else {
      console.log(`Price: ${price}, SMA(5): Not enough data`)
    }
  })

  console.log(
    `\nFinal SMA values: ${IndicatorUtils.extractValues(sma.getValues())
      .map((v) => v.toFixed(2))
      .join(', ')}`
  )
}

/**
 * Example 2: Advanced SMA Configuration
 * Shows advanced features like different price types and smoothing
 */
export function advancedSMAExample(): void {
  console.log('\n=== Advanced SMA Example ===')

  // Create SMA with advanced configuration
  const sma = new SimpleMovingAverage({
    period: 10,
    priceType: 'typical', // (high + low + close) / 3
    useSmoothing: true,
    smoothingFactor: 0.2,
    enableStreaming: true,
    maxHistorySize: 100
  })

  // Sample OHLC data
  const ohlcData: OHLCDataPoint[] = [
    { value: 100, open: 99, high: 102, low: 98, timestamp: Date.now() },
    { value: 102, open: 100, high: 104, low: 99, timestamp: Date.now() + 1000 },
    { value: 98, open: 102, high: 103, low: 96, timestamp: Date.now() + 2000 },
    { value: 105, open: 98, high: 107, low: 97, timestamp: Date.now() + 3000 },
    { value: 103, open: 105, high: 106, low: 101, timestamp: Date.now() + 4000 }
  ]

  console.log('Processing OHLC data with typical price:')
  ohlcData.forEach((data, index) => {
    const typicalPrice = (data.high! + data.low! + data.value) / 3
    console.log(
      `OHLC: O=${data.open} H=${data.high} L=${data.low} C=${data.value}, Typical: ${typicalPrice.toFixed(2)}`
    )

    const result = sma.addData(data)
    if (result) {
      console.log(`  SMA(10) with smoothing: ${result.value.toFixed(2)}`)
    }
  })
}

/**
 * Example 3: Real-time Streaming
 * Demonstrates streaming capabilities with event handling
 */
export function streamingExample(): void {
  console.log('\n=== Streaming Example ===')

  const sma = createSMA({
    period: 3,
    enableStreaming: true
  })

  // Set up event listeners
  sma.on('data-added', (event) => {
    console.log(`📊 Data added: ${(event.payload as any).dataPoint.value}`)
  })

  sma.on('value-calculated', (event) => {
    const output = (event.payload as any).outputValue
    console.log(`📈 SMA calculated: ${output.value.toFixed(2)} at index ${output.index}`)
  })

  sma.on('error-occurred', (event) => {
    console.error(`❌ Error: ${(event.payload as any).error}`)
  })

  // Set up streaming updates
  sma.onStreamUpdate((update) => {
    console.log(
      `🔄 Stream update: ${update.indicatorValue.value.toFixed(2)} (${update.isNewValue ? 'NEW' : 'UPDATE'})`
    )
  })

  // Simulate real-time data
  const prices = [100, 105, 95, 110, 102]
  console.log('Simulating real-time price updates:')

  prices.forEach((price, index) => {
    setTimeout(() => {
      sma.addData({ value: price, timestamp: Date.now() })
    }, index * 500) // 500ms intervals
  })
}

/**
 * Example 4: Batch Processing
 * Shows efficient batch processing for historical data
 */
export function batchProcessingExample(): void {
  console.log('\n=== Batch Processing Example ===')

  const sma = createSMA({ period: 20 })

  // Generate sample historical data
  const historicalPrices = Array.from({ length: 100 }, (_, i) => {
    return 100 + Math.sin(i * 0.1) * 10 + Math.random() * 5
  })

  console.log(`Processing ${historicalPrices.length} historical data points...`)

  const startTime = performance.now()

  // Convert to data points
  const dataPoints = IndicatorUtils.pricesToDataPoints(
    historicalPrices,
    Date.now() - historicalPrices.length * 1000
  )

  // Process in batch
  const results = sma.addDataBatch(dataPoints)

  const endTime = performance.now()

  console.log(`✅ Processed ${results.length} SMA values in ${(endTime - startTime).toFixed(2)}ms`)
  console.log(
    `📊 Latest SMA values: ${results
      .slice(-5)
      .map((r) => r.value.toFixed(2))
      .join(', ')}`
  )

  // Get calculation metadata
  const calculation = sma.calculate()
  if (calculation.metadata.performance) {
    console.log(
      `⚡ Performance: ${calculation.metadata.performance.calculationTime.toFixed(2)}ms for ${calculation.metadata.inputCount} inputs`
    )
  }
}

/**
 * Example 5: Error Handling and Validation
 * Demonstrates comprehensive error handling
 */
export function errorHandlingExample(): void {
  console.log('\n=== Error Handling Example ===')

  try {
    // Invalid configuration
    console.log('Testing invalid configuration...')
    const invalidSMA = createSMA({ period: -1 })
  } catch (error) {
    console.log(`❌ Configuration error caught: ${error instanceof Error ? error.message : error}`)
  }

  const sma = createSMA({ period: 5 })

  // Test input validation
  console.log('\nTesting input validation...')
  const invalidData: IndicatorDataPoint[] = [
    { value: NaN, timestamp: Date.now() },
    { value: Infinity, timestamp: Date.now() + 1000 },
    { value: 100, timestamp: -1 } // Invalid timestamp
  ]

  const validation = sma.validateInput(invalidData)
  if (!validation.isValid) {
    console.log(`❌ Validation errors: ${validation.errors.join(', ')}`)
  }
  if (validation.warnings.length > 0) {
    console.log(`⚠️  Validation warnings: ${validation.warnings.join(', ')}`)
  }

  // Test error events
  sma.on('error-occurred', (event) => {
    console.log(`❌ Error event: ${(event.payload as any).error}`)
  })

  // Try to add invalid data
  try {
    sma.addData({ value: NaN, timestamp: Date.now() })
  } catch (error) {
    console.log(`❌ Runtime error caught: ${error instanceof Error ? error.message : error}`)
  }
}

/**
 * Example 6: Indicator Registry Usage
 * Shows how to use the indicator registry system
 */
export function registryExample(): void {
  console.log('\n=== Registry Example ===')

  // List available indicators
  console.log('Available indicators:', indicatorRegistry.getAvailableIndicators())

  // Create indicator using registry
  const sma1 = indicatorRegistry.create('sma', { period: 10 })
  const sma2 = indicatorRegistry.create('simplemovingaverage', { period: 20 })

  console.log(
    `Created SMA indicators: ${sma1.name} (period: ${sma1.config.period}), ${sma2.name} (period: ${sma2.config.period})`
  )

  // Add some data
  const testData = [100, 102, 98, 105, 103]
  testData.forEach((price) => {
    const dataPoint = { value: price, timestamp: Date.now() }
    sma1.addData(dataPoint)
    sma2.addData(dataPoint)
  })

  console.log(`SMA(10) latest: ${sma1.latestValue?.value.toFixed(2) || 'N/A'}`)
  console.log(`SMA(20) latest: ${sma2.latestValue?.value.toFixed(2) || 'N/A'}`)
}

/**
 * Example 7: Trading Strategy Integration
 * Shows how to integrate indicators in a trading strategy
 */
export function tradingStrategyExample(): void {
  console.log('\n=== Trading Strategy Example ===')

  class SimpleMovingAverageCrossover {
    private shortSMA: SimpleMovingAverage
    private longSMA: SimpleMovingAverage
    private signals: Array<{ type: 'BUY' | 'SELL'; price: number; timestamp: number }> = []

    constructor(shortPeriod: number = 10, longPeriod: number = 20) {
      this.shortSMA = createSMA({ period: shortPeriod, enableStreaming: true })
      this.longSMA = createSMA({ period: longPeriod, enableStreaming: true })

      // Set up crossover detection
      this.shortSMA.on('value-calculated', () => this.checkCrossover())
    }

    private checkCrossover(): void {
      const shortValue = this.shortSMA.getCurrentAverage()
      const longValue = this.longSMA.getCurrentAverage()

      if (shortValue && longValue) {
        const shortValues = this.shortSMA.getLastValues(2)
        const longValues = this.longSMA.getLastValues(2)

        if (shortValues.length >= 2 && longValues.length >= 2) {
          const prevShort = shortValues[0].value
          const prevLong = longValues[0].value

          // Golden cross (bullish signal)
          if (prevShort <= prevLong && shortValue > longValue) {
            this.signals.push({
              type: 'BUY',
              price: shortValue,
              timestamp: Date.now()
            })
            console.log(`🟢 GOLDEN CROSS - BUY signal at ${shortValue.toFixed(2)}`)
          }

          // Death cross (bearish signal)
          if (prevShort >= prevLong && shortValue < longValue) {
            this.signals.push({
              type: 'SELL',
              price: shortValue,
              timestamp: Date.now()
            })
            console.log(`🔴 DEATH CROSS - SELL signal at ${shortValue.toFixed(2)}`)
          }
        }
      }
    }

    public addPrice(price: number): void {
      const dataPoint = { value: price, timestamp: Date.now() }
      this.shortSMA.addData(dataPoint)
      this.longSMA.addData(dataPoint)
    }

    public getSignals() {
      return this.signals
    }

    public getStatus() {
      return {
        shortSMA: this.shortSMA.getCurrentAverage(),
        longSMA: this.longSMA.getCurrentAverage(),
        signalCount: this.signals.length,
        isReady: this.shortSMA.isReady && this.longSMA.isReady
      }
    }
  }

  // Test the strategy
  const strategy = new SimpleMovingAverageCrossover(5, 10)

  // Simulate price data that will generate crossovers
  const priceData = [
    100,
    102,
    98,
    105,
    103,
    99,
    101,
    104,
    97,
    106, // Initial data
    108,
    110,
    112,
    115,
    118,
    120,
    122,
    125,
    128,
    130, // Uptrend
    128,
    125,
    122,
    120,
    118,
    115,
    112,
    110,
    108,
    105 // Downtrend
  ]

  console.log('Running SMA crossover strategy...')
  priceData.forEach((price, index) => {
    strategy.addPrice(price)

    if (index % 5 === 0) {
      // Log status every 5 prices
      const status = strategy.getStatus()
      if (status.isReady) {
        console.log(
          `Price: ${price}, Short SMA: ${status.shortSMA?.toFixed(2)}, Long SMA: ${status.longSMA?.toFixed(2)}`
        )
      }
    }
  })

  const finalStatus = strategy.getStatus()
  console.log(`\n📊 Strategy Results:`)
  console.log(`   Signals generated: ${finalStatus.signalCount}`)
  console.log(`   Final Short SMA: ${finalStatus.shortSMA?.toFixed(2)}`)
  console.log(`   Final Long SMA: ${finalStatus.longSMA?.toFixed(2)}`)

  const signals = strategy.getSignals()
  if (signals.length > 0) {
    console.log(`   Signals: ${signals.map((s) => `${s.type}@${s.price.toFixed(2)}`).join(', ')}`)
  }
}

/**
 * Run all examples
 */
export function runAllExamples(): void {
  console.log('🚀 Running Technical Indicators Examples...\n')

  try {
    basicSMAExample()
    advancedSMAExample()
    batchProcessingExample()
    errorHandlingExample()
    registryExample()
    tradingStrategyExample()

    // Note: Streaming example uses setTimeout, so it runs asynchronously
    console.log('\n⏱️  Running streaming example (asynchronous)...')
    streamingExample()

    console.log('\n✅ All examples completed successfully!')
  } catch (error) {
    console.error('❌ Example execution failed:', error)
    logger.error('Examples', 'Failed to run examples', error)
  }
}

// Export for use in other modules
export default {
  basicSMAExample,
  advancedSMAExample,
  streamingExample,
  batchProcessingExample,
  errorHandlingExample,
  registryExample,
  tradingStrategyExample,
  runAllExamples
}
