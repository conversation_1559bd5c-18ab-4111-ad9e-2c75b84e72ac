/**
 * Signal Generation Integration Tests
 * Comprehensive test suite for the signal generation system
 */

import { logger } from '../../shared/utils/logger'
import { MarketDataProcessor } from '../services/MarketDataProcessor'
import { SignalGenerationService } from '../services/SignalGenerationService'
import { BacktestingEngine } from '../backtesting/BacktestingEngine'
import { SystemMonitor, ErrorHandler, PerformanceTracker } from '../monitoring/SystemMonitor'
import { SignalBroadcaster } from '../events/SignalBroadcaster'
import {
  transformStreamData,
  transformHistoryData,
  transformPeriodData
} from '../../shared/utils/marketDataTransformers'
import type { GeneratedSignal, MarketDataPoint, OHLCMarketData } from '../../shared/types/signals'

/**
 * Test configuration
 */
interface TestConfig {
  enableLogging: boolean
  testDataSize: number
  timeoutMs: number
  expectedMinSignals: number
  expectedMinWinRate: number
}

/**
 * Test results
 */
interface TestResults {
  testName: string
  passed: boolean
  duration: number
  details: Record<string, any>
  errors: string[]
}

/**
 * Mock market data generator
 */
class MockMarketDataGenerator {
  private currentPrice: number = 1.2
  private volatility: number = 0.001
  private trend: number = 0.0001

  /**
   * Generate realistic market data for testing
   */
  public generateStreamData(symbol: string, count: number): StreamData[] {
    const data: StreamData[] = []
    const startTime = Date.now() - count * 1000 // 1 second intervals

    for (let i = 0; i < count; i++) {
      // Simulate realistic price movement
      const randomChange = (Math.random() - 0.5) * this.volatility
      const trendChange = this.trend * (Math.random() > 0.5 ? 1 : -1)
      this.currentPrice += randomChange + trendChange

      // Ensure price stays positive
      this.currentPrice = Math.max(0.0001, this.currentPrice)

      data.push([
        symbol,
        Math.floor((startTime + i * 1000) / 1000), // Convert to seconds
        this.currentPrice
      ])
    }

    return data
  }

  /**
   * Generate OHLC candle data
   */
  public generateCandleData(symbol: string, count: number): OHLCMarketData[] {
    const data: OHLCMarketData[] = []
    const startTime = Date.now() - count * 60000 // 1 minute intervals

    for (let i = 0; i < count; i++) {
      const open = this.currentPrice
      const high = open + Math.random() * this.volatility * 2
      const low = open - Math.random() * this.volatility * 2
      const close = low + Math.random() * (high - low)

      this.currentPrice = close

      data.push({
        symbol,
        timestamp: startTime + i * 60000,
        open,
        high,
        low,
        close,
        source: 'candle'
      })
    }

    return data
  }

  /**
   * Reset price to initial value
   */
  public reset(): void {
    this.currentPrice = 1.2
  }
}

/**
 * Signal Generation Integration Test Suite
 */
export class SignalGenerationIntegrationTest {
  private config: TestConfig
  private mockDataGenerator: MockMarketDataGenerator
  private testResults: TestResults[] = []

  constructor(config: Partial<TestConfig> = {}) {
    this.config = {
      enableLogging: false,
      testDataSize: 100,
      timeoutMs: 30000,
      expectedMinSignals: 5,
      expectedMinWinRate: 0.6,
      ...config
    }
    this.mockDataGenerator = new MockMarketDataGenerator()
  }

  /**
   * Run all integration tests
   */
  public async runAllTests(): Promise<TestResults[]> {
    logger.info('SignalGenerationTest', 'Starting signal generation integration tests')

    try {
      // Test 1: Market Data Transformation
      await this.testMarketDataTransformation()

      // Test 2: Signal Generation Service
      await this.testSignalGenerationService()

      // Test 3: Market Data Processor
      await this.testMarketDataProcessor()

      // Test 4: Backtesting Engine
      await this.testBacktestingEngine()

      // Test 5: System Monitoring
      await this.testSystemMonitoring()

      // Test 6: WebSocket Broadcasting
      await this.testWebSocketBroadcasting()

      // Test 7: End-to-End Signal Pipeline
      await this.testEndToEndPipeline()

      // Test 8: Error Handling
      await this.testErrorHandling()

      // Test 9: Performance Under Load
      await this.testPerformanceUnderLoad()

      // Test 10: Signal Quality Validation
      await this.testSignalQualityValidation()
    } catch (error) {
      logger.error('SignalGenerationTest', 'Test suite failed:', error)
    }

    this.logTestSummary()
    return this.testResults
  }

  /**
   * Test market data transformation utilities
   */
  private async testMarketDataTransformation(): Promise<void> {
    const testName = 'Market Data Transformation'
    const startTime = Date.now()
    const errors: string[] = []

    try {
      // Test stream data transformation
      const streamData = this.mockDataGenerator.generateStreamData('EURUSD', 10)
      const transformedStream = streamData
        .map((data) => transformStreamData(data))
        .filter((d) => d !== null)

      if (transformedStream.length !== streamData.length) {
        errors.push('Stream data transformation failed')
      }

      // Test OHLC data transformation
      const candleData = this.mockDataGenerator.generateCandleData('EURUSD', 10)
      const validCandles = candleData.filter(
        (candle) =>
          candle.high >= Math.max(candle.open, candle.close) &&
          candle.low <= Math.min(candle.open, candle.close)
      )

      if (validCandles.length !== candleData.length) {
        errors.push('OHLC data validation failed')
      }

      this.testResults.push({
        testName,
        passed: errors.length === 0,
        duration: Date.now() - startTime,
        details: {
          streamDataPoints: transformedStream.length,
          candleDataPoints: validCandles.length
        },
        errors
      })
    } catch (error) {
      errors.push(`Test execution failed: ${error}`)
      this.testResults.push({
        testName,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        errors
      })
    }
  }

  /**
   * Test signal generation service
   */
  private async testSignalGenerationService(): Promise<void> {
    const testName = 'Signal Generation Service'
    const startTime = Date.now()
    const errors: string[] = []

    try {
      const service = SignalGenerationService.getInstance()

      // Initialize service
      await service.initialize({
        minConfidenceThreshold: 0.6,
        enableAdvancedStrategy: true,
        enableSignalLogging: this.config.enableLogging,
        maxSignalsPerSymbolPerHour: 100,
        watchedSymbols: ['EURUSD']
      })

      await service.start()

      // Generate test data and process
      const testData = this.mockDataGenerator.generateStreamData('EURUSD', this.config.testDataSize)
      const signals: GeneratedSignal[] = []

      for (const streamData of testData) {
        const marketData = transformStreamData(streamData)
        if (marketData) {
          const generatedSignals = await service.processMarketData(marketData)
          signals.push(...generatedSignals)
        }
      }

      // Validate results
      if (signals.length < this.config.expectedMinSignals) {
        errors.push(
          `Insufficient signals generated: ${signals.length} < ${this.config.expectedMinSignals}`
        )
      }

      const avgConfidence =
        signals.length > 0 ? signals.reduce((sum, s) => sum + s.confidence, 0) / signals.length : 0

      if (avgConfidence < 0.6) {
        errors.push(`Low average confidence: ${avgConfidence.toFixed(2)}`)
      }

      await service.stop()

      this.testResults.push({
        testName,
        passed: errors.length === 0,
        duration: Date.now() - startTime,
        details: {
          signalsGenerated: signals.length,
          averageConfidence: avgConfidence,
          serviceStatus: service.getStatus()
        },
        errors
      })
    } catch (error) {
      errors.push(`Test execution failed: ${error}`)
      this.testResults.push({
        testName,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        errors
      })
    }
  }

  /**
   * Test system monitoring
   */
  private async testSystemMonitoring(): Promise<void> {
    const testName = 'System Monitoring'
    const startTime = Date.now()
    const errors: string[] = []

    try {
      const monitor = SystemMonitor.getInstance()
      monitor.setEnabled(true)

      // Record some test data
      monitor.recordProcessingTime(50)
      monitor.recordProcessingTime(75)
      monitor.recordProcessingTime(100)

      const mockSignal: GeneratedSignal = {
        signal: 'BUY',
        confidence: 0.8,
        timestamp: Date.now(),
        strategy: 'test_strategy',
        indicators: []
      }
      monitor.recordSignal(mockSignal)

      // Test error recording
      monitor.recordError('TEST_ERROR', 'Test error message', 'TestComponent', 'MEDIUM', {
        testData: 'test'
      })

      // Get metrics and health
      const metrics = monitor.getPerformanceMetrics()
      const health = monitor.getSystemHealth()

      // Validate metrics
      if (metrics.averageProcessingTime === 0) {
        errors.push('Processing time not recorded properly')
      }

      if (metrics.totalErrors === 0) {
        errors.push('Error recording not working')
      }

      this.testResults.push({
        testName,
        passed: errors.length === 0,
        duration: Date.now() - startTime,
        details: {
          metrics: {
            avgProcessingTime: metrics.averageProcessingTime,
            totalErrors: metrics.totalErrors,
            signalCount: metrics.totalSignalsProcessed
          },
          health: {
            status: health.status,
            score: health.score,
            issueCount: health.issues.length
          }
        },
        errors
      })
    } catch (error) {
      errors.push(`Test execution failed: ${error}`)
      this.testResults.push({
        testName,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        errors
      })
    }
  }

  /**
   * Test WebSocket broadcasting
   */
  private async testWebSocketBroadcasting(): Promise<void> {
    const testName = 'WebSocket Broadcasting'
    const startTime = Date.now()
    const errors: string[] = []

    try {
      const broadcaster = SignalBroadcaster.getInstance()
      broadcaster.setEnabled(true)

      // Test signal broadcasting
      const mockSignal: GeneratedSignal = {
        signal: 'SELL',
        confidence: 0.75,
        timestamp: Date.now(),
        strategy: 'test_strategy',
        indicators: []
      }

      const mockMarketData: MarketDataPoint = {
        symbol: 'EURUSD',
        timestamp: Date.now(),
        price: 1.2,
        source: 'stream'
      }

      broadcaster.broadcastSignalGenerated(mockSignal, mockMarketData, 50)
      broadcaster.broadcastMarketDataUpdate('EURUSD', 1.2, 'stream')

      // Test statistics
      const stats = broadcaster.getStatistics()

      if (!stats.isEnabled) {
        errors.push('Broadcaster not enabled')
      }

      this.testResults.push({
        testName,
        passed: errors.length === 0,
        duration: Date.now() - startTime,
        details: {
          broadcasterStats: stats
        },
        errors
      })
    } catch (error) {
      errors.push(`Test execution failed: ${error}`)
      this.testResults.push({
        testName,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        errors
      })
    }
  }

  /**
   * Test end-to-end signal pipeline
   */
  private async testEndToEndPipeline(): Promise<void> {
    const testName = 'End-to-End Signal Pipeline'
    const startTime = Date.now()
    const errors: string[] = []

    try {
      // Initialize all components
      const processor = MarketDataProcessor.getInstance()
      const service = SignalGenerationService.getInstance()
      const monitor = SystemMonitor.getInstance()
      const broadcaster = SignalBroadcaster.getInstance()

      // Configure components
      await processor.initialize({
        minConfidenceThreshold: 0.65,
        enableRealTimeSignals: true,
        watchedSymbols: ['EURUSD']
      })

      await service.initialize({
        minConfidenceThreshold: 0.65,
        enableAdvancedStrategy: true,
        enableSignalLogging: this.config.enableLogging
      })

      await service.start()
      monitor.setEnabled(true)
      broadcaster.setEnabled(true)

      // Process test data through the entire pipeline
      const testData = this.mockDataGenerator.generateStreamData('EURUSD', 50)
      let totalSignals = 0

      for (const streamData of testData) {
        const results = await processor.processStreamData(streamData)
        totalSignals += results.length
      }

      // Validate pipeline results
      const serviceStats = service.getStatistics()
      const monitorMetrics = monitor.getPerformanceMetrics()

      if (totalSignals === 0 && serviceStats.totalSignalsGenerated === 0) {
        errors.push('No signals generated through pipeline')
      }

      await service.stop()

      this.testResults.push({
        testName,
        passed: errors.length === 0,
        duration: Date.now() - startTime,
        details: {
          pipelineSignals: totalSignals,
          serviceSignals: serviceStats.totalSignalsGenerated,
          processingMetrics: {
            avgProcessingTime: monitorMetrics.averageProcessingTime,
            totalProcessed: monitorMetrics.totalSignalsProcessed
          }
        },
        errors
      })
    } catch (error) {
      errors.push(`Test execution failed: ${error}`)
      this.testResults.push({
        testName,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        errors
      })
    }
  }

  /**
   * Test error handling
   */
  private async testErrorHandling(): Promise<void> {
    const testName = 'Error Handling'
    const startTime = Date.now()
    const errors: string[] = []

    try {
      // Test ErrorHandler utility
      const testError = new Error('Test error for validation')
      ErrorHandler.handleError(testError, 'TestComponent', { testData: 'test' }, 'HIGH')

      // Test async error wrapping
      const asyncResult = await ErrorHandler.wrapAsync(async () => {
        throw new Error('Async test error')
      }, 'AsyncTestComponent')

      if (asyncResult !== null) {
        errors.push('Async error handling failed - should return null')
      }

      // Test sync error wrapping
      const syncResult = ErrorHandler.wrapSync(() => {
        throw new Error('Sync test error')
      }, 'SyncTestComponent')

      if (syncResult !== null) {
        errors.push('Sync error handling failed - should return null')
      }

      // Test performance tracking with errors
      const { result, duration } = await PerformanceTracker.timeAsync(async () => {
        await new Promise((resolve) => setTimeout(resolve, 10))
        return 'success'
      }, 'TestOperation')

      if (result !== 'success' || duration < 10) {
        errors.push('Performance tracking failed')
      }

      this.testResults.push({
        testName,
        passed: errors.length === 0,
        duration: Date.now() - startTime,
        details: {
          asyncErrorHandled: asyncResult === null,
          syncErrorHandled: syncResult === null,
          performanceTracked: duration >= 10
        },
        errors
      })
    } catch (error) {
      errors.push(`Test execution failed: ${error}`)
      this.testResults.push({
        testName,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        errors
      })
    }
  }

  /**
   * Test performance under load
   */
  private async testPerformanceUnderLoad(): Promise<void> {
    const testName = 'Performance Under Load'
    const startTime = Date.now()
    const errors: string[] = []

    try {
      const service = SignalGenerationService.getInstance()

      // Initialize for load testing
      await service.initialize({
        minConfidenceThreshold: 0.6,
        enableAdvancedStrategy: true,
        enableSignalLogging: false,
        maxSignalsPerSymbolPerHour: 1000
      })

      await service.start()

      // Generate large dataset
      const largeDataset = this.mockDataGenerator.generateStreamData('EURUSD', 500)
      const processingTimes: number[] = []
      let totalSignals = 0

      // Process data and measure performance
      for (const streamData of largeDataset) {
        const processStart = Date.now()
        const marketData = transformStreamData(streamData)

        if (marketData) {
          const signals = await service.processMarketData(marketData)
          totalSignals += signals.length
        }

        processingTimes.push(Date.now() - processStart)
      }

      // Analyze performance
      const avgProcessingTime =
        processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length
      const maxProcessingTime = Math.max(...processingTimes)

      // Performance thresholds
      if (avgProcessingTime > 100) {
        // 100ms average
        errors.push(`High average processing time: ${avgProcessingTime.toFixed(2)}ms`)
      }

      if (maxProcessingTime > 500) {
        // 500ms max
        errors.push(`High maximum processing time: ${maxProcessingTime.toFixed(2)}ms`)
      }

      await service.stop()

      this.testResults.push({
        testName,
        passed: errors.length === 0,
        duration: Date.now() - startTime,
        details: {
          dataPointsProcessed: largeDataset.length,
          totalSignalsGenerated: totalSignals,
          averageProcessingTime: avgProcessingTime,
          maxProcessingTime: maxProcessingTime,
          signalGenerationRate: totalSignals / (largeDataset.length / 60) // signals per minute
        },
        errors
      })
    } catch (error) {
      errors.push(`Test execution failed: ${error}`)
      this.testResults.push({
        testName,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        errors
      })
    }
  }

  /**
   * Test signal quality validation
   */
  private async testSignalQualityValidation(): Promise<void> {
    const testName = 'Signal Quality Validation'
    const startTime = Date.now()
    const errors: string[] = []

    try {
      const service = SignalGenerationService.getInstance()

      await service.initialize({
        minConfidenceThreshold: 0.7,
        enableAdvancedStrategy: true,
        enableSignalLogging: false
      })

      await service.start()

      // Generate test data with known patterns
      const testData = this.mockDataGenerator.generateStreamData('EURUSD', 100)
      const signals: GeneratedSignal[] = []

      for (const streamData of testData) {
        const marketData = transformStreamData(streamData)
        if (marketData) {
          const generatedSignals = await service.processMarketData(marketData)
          signals.push(...generatedSignals)
        }
      }

      // Validate signal quality
      if (signals.length > 0) {
        const avgConfidence = signals.reduce((sum, s) => sum + s.confidence, 0) / signals.length
        const highConfidenceSignals = signals.filter((s) => s.confidence >= 0.8).length
        const lowConfidenceSignals = signals.filter((s) => s.confidence < 0.6).length

        if (avgConfidence < 0.7) {
          errors.push(`Low average confidence: ${avgConfidence.toFixed(2)}`)
        }

        if (lowConfidenceSignals > 0) {
          errors.push(`${lowConfidenceSignals} signals below minimum confidence threshold`)
        }

        // Check signal distribution
        const buySignals = signals.filter((s) => s.signal === 'BUY').length
        const sellSignals = signals.filter((s) => s.signal === 'SELL').length
        const holdSignals = signals.filter((s) => s.signal === 'HOLD').length

        if (buySignals === 0 && sellSignals === 0) {
          errors.push('No actionable signals generated (only HOLD signals)')
        }
      }

      await service.stop()

      this.testResults.push({
        testName,
        passed: errors.length === 0,
        duration: Date.now() - startTime,
        details: {
          totalSignals: signals.length,
          averageConfidence:
            signals.length > 0
              ? signals.reduce((sum, s) => sum + s.confidence, 0) / signals.length
              : 0,
          signalDistribution: {
            BUY: signals.filter((s) => s.signal === 'BUY').length,
            SELL: signals.filter((s) => s.signal === 'SELL').length,
            HOLD: signals.filter((s) => s.signal === 'HOLD').length
          },
          highConfidenceCount: signals.filter((s) => s.confidence >= 0.8).length
        },
        errors
      })
    } catch (error) {
      errors.push(`Test execution failed: ${error}`)
      this.testResults.push({
        testName,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        errors
      })
    }
  }

  /**
   * Log test summary
   */
  private logTestSummary(): void {
    const totalTests = this.testResults.length
    const passedTests = this.testResults.filter((t) => t.passed).length
    const failedTests = totalTests - passedTests
    const totalDuration = this.testResults.reduce((sum, t) => sum + t.duration, 0)

    logger.info('SignalGenerationTest', '=== TEST SUMMARY ===')
    logger.info('SignalGenerationTest', `Total Tests: ${totalTests}`)
    logger.info('SignalGenerationTest', `Passed: ${passedTests}`)
    logger.info('SignalGenerationTest', `Failed: ${failedTests}`)
    logger.info(
      'SignalGenerationTest',
      `Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`
    )
    logger.info('SignalGenerationTest', `Total Duration: ${totalDuration}ms`)

    // Log failed tests
    const failedTestResults = this.testResults.filter((t) => !t.passed)
    if (failedTestResults.length > 0) {
      logger.error('SignalGenerationTest', '=== FAILED TESTS ===')
      failedTestResults.forEach((test) => {
        logger.error('SignalGenerationTest', `${test.testName}: ${test.errors.join(', ')}`)
      })
    }

    // Log performance summary
    logger.info('SignalGenerationTest', '=== PERFORMANCE SUMMARY ===')
    this.testResults.forEach((test) => {
      logger.info('SignalGenerationTest', `${test.testName}: ${test.duration}ms`)
    })
  }

  /**
   * Test market data processor
   */
  private async testMarketDataProcessor(): Promise<void> {
    const testName = 'Market Data Processor'
    const startTime = Date.now()
    const errors: string[] = []

    try {
      const processor = MarketDataProcessor.getInstance()

      // Initialize processor
      await processor.initialize({
        minConfidenceThreshold: 0.65,
        maxHistorySize: 500,
        enableRealTimeSignals: true,
        watchedSymbols: ['EURUSD'],
        signalGenerationInterval: 1000
      })

      // Test stream data processing
      const streamData = this.mockDataGenerator.generateStreamData('EURUSD', 20)
      const streamResults: any[] = []

      for (const data of streamData) {
        const results = await processor.processStreamData(data)
        streamResults.push(...results)
      }

      // Test status
      const status = processor.getStatus()
      if (!status.isInitialized) {
        errors.push('Processor not properly initialized')
      }

      this.testResults.push({
        testName,
        passed: errors.length === 0,
        duration: Date.now() - startTime,
        details: {
          streamResultsCount: streamResults.length,
          processorStatus: status
        },
        errors
      })
    } catch (error) {
      errors.push(`Test execution failed: ${error}`)
      this.testResults.push({
        testName,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        errors
      })
    }
  }

  /**
   * Test backtesting engine
   */
  private async testBacktestingEngine(): Promise<void> {
    const testName = 'Backtesting Engine'
    const startTime = Date.now()
    const errors: string[] = []

    try {
      const engine = new BacktestingEngine({
        initialCapital: 10000,
        positionSizePercent: 0.02,
        maxRiskPerTrade: 0.01,
        minConfidenceThreshold: 0.7
      })

      // Generate test data
      const testData = this.mockDataGenerator.generateCandleData('EURUSD', 200)

      // Run backtest
      const results = await engine.runBacktest(testData)

      // Validate results
      if (results.totalTrades === 0) {
        errors.push('No trades executed during backtest')
      }

      if (results.winRate < this.config.expectedMinWinRate) {
        errors.push(
          `Low win rate: ${(results.winRate * 100).toFixed(1)}% < ${(this.config.expectedMinWinRate * 100).toFixed(1)}%`
        )
      }

      this.testResults.push({
        testName,
        passed: errors.length === 0,
        duration: Date.now() - startTime,
        details: {
          totalTrades: results.totalTrades,
          winRate: results.winRate,
          totalReturn: results.totalReturnPercent,
          sharpeRatio: results.sharpeRatio
        },
        errors
      })
    } catch (error) {
      errors.push(`Test execution failed: ${error}`)
      this.testResults.push({
        testName,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        errors
      })
    }
  }
}
