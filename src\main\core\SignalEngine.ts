/**
 * Signal Engine Class
 * Main orchestrator class that uses StrategyFactory to create and execute trading strategies,
 * producing BUY/SELL signals with comprehensive analysis and performance tracking
 *
 * @example
 * ```typescript
 * // Get singleton instance
 * const signalEngine = SignalEngine.getInstance()
 *
 * // Create single indicator strategy
 * const strategy1 = signalEngine.createStrategy('rsi', {
 *   period: 14,
 *   overbought: 70,
 *   oversold: 30
 * })
 *
 * // Create multi-indicator combined strategy
 * const strategy2 = signalEngine.createStrategy(['rsi', 'sma'], {
 *   rsi: { period: 14, overbought: 70, oversold: 30 },
 *   sma: { period: 20 }
 * })
 *
 * // Process market data
 * const signals = signalEngine.processMarketData({ value: 45.67, timestamp: Date.now() })
 * console.log('Generated signals:', signals)
 * ```
 */

import { logger } from '../../shared/utils/logger'
import { SignalEngineEvents } from '../events/SignalEngineEventEmitter'
import { StrategyFactory } from './StrategyFactory'
import type {
  SignalEngine as ISignalEngine,
  SignalEngineConfig,
  SignalEnginePerformanceMetrics,
  Strategy,
  GeneratedSignal,
  TradingSignal
} from '../../shared/types/signals'
import type { IndicatorDataPoint } from '../../shared/types/indicators'
import {
  DEFAULT_SIGNAL_ENGINE_CONFIG,
  DEFAULT_STRATEGY_CONFIG,
  SIGNAL_ENGINE_ERROR_MESSAGES,
  SIGNAL_ENGINE_LOG_CATEGORIES,
  SIGNAL_ENGINE_EVENTS,
  PERFORMANCE_CONSTANTS
} from '../../shared/constants/signals'
import {
  SignalValidationError,
  sanitizeSignalError,
  validateGeneratedSignal
} from '../../shared/utils/signalValidation'

/**
 * Signal Engine Class
 * Singleton orchestrator for creating and managing trading strategies with comprehensive signal generation
 *
 * Features:
 * - Flexible API for single and multi-indicator strategy creation
 * - Real-time signal generation and processing
 * - Performance monitoring and metrics tracking
 * - Comprehensive error handling and validation
 * - Event-driven architecture for signal broadcasting
 * - Memory management and cleanup utilities
 */
export class SignalEngine implements ISignalEngine {
  private static instance: SignalEngine | null = null
  private readonly config: SignalEngineConfig
  private readonly strategyFactory: StrategyFactory
  private readonly activeStrategies: Map<string, Strategy> = new Map()
  private readonly signalHistory: GeneratedSignal[] = []
  private performanceMetrics: SignalEnginePerformanceMetrics
  private lastCleanupTime: number = Date.now()

  /**
   * Private constructor for Singleton pattern
   */
  private constructor(config?: Partial<SignalEngineConfig>) {
    this.config = {
      ...DEFAULT_SIGNAL_ENGINE_CONFIG,
      ...config
    }

    this.strategyFactory = StrategyFactory.getInstance()

    this.performanceMetrics = {
      totalStrategies: 0,
      activeStrategies: 0,
      totalSignals: 0,
      signalsPerStrategy: {},
      averageProcessingTime: 0,
      lastProcessingTime: 0,
      memoryUsage: {
        signalHistory: 0,
        strategies: 0,
        indicators: 0
      }
    }

    // Start performance monitoring if enabled
    if (this.config.performanceMonitoring) {
      this.startPerformanceMonitoring()
    }

    logger.info(
      `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} SignalEngine initialized with config:`,
      this.config
    )
  }

  /**
   * Get singleton instance
   * @param config - Optional configuration override
   * @returns SignalEngine instance
   */
  public static getInstance(config?: Partial<SignalEngineConfig>): SignalEngine {
    if (!SignalEngine.instance) {
      SignalEngine.instance = new SignalEngine(config)
    }
    return SignalEngine.instance
  }

  /**
   * Reset singleton instance (useful for testing)
   */
  public static resetInstance(): void {
    if (SignalEngine.instance) {
      SignalEngine.instance.dispose()
      SignalEngine.instance = null
    }
  }

  /**
   * Create a strategy using flexible API
   * Supports both single indicator and multi-indicator strategies
   */
  public createStrategy(indicators: string | string[], config: Record<string, unknown>): Strategy {
    const methodName = 'createStrategy'
    const startTime = Date.now()

    try {
      logger.info(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} ${methodName} called with indicators:`,
        indicators
      )

      // Check strategy limit
      if (this.activeStrategies.size >= this.config.maxActiveStrategies) {
        throw new SignalValidationError(
          SIGNAL_ENGINE_ERROR_MESSAGES.MAX_STRATEGIES_REACHED,
          'strategy_limit'
        )
      }

      let strategy: Strategy

      if (typeof indicators === 'string') {
        // Single indicator strategy
        strategy = this.createSingleIndicatorStrategy(indicators, config)
      } else if (Array.isArray(indicators)) {
        // Multi-indicator strategy
        strategy = this.createMultiIndicatorStrategy(indicators, config)
      } else {
        throw new SignalValidationError(
          'Indicators must be a string or array of strings',
          'invalid_indicators_type'
        )
      }

      // Check for duplicate strategy names
      if (this.activeStrategies.has(strategy.name)) {
        throw new SignalValidationError(
          SIGNAL_ENGINE_ERROR_MESSAGES.STRATEGY_ALREADY_EXISTS,
          'duplicate_strategy'
        )
      }

      // Add to active strategies
      this.activeStrategies.set(strategy.name, strategy)

      // Update metrics
      this.updateStrategyMetrics(strategy, 'created')
      this.updateProcessingTime(Date.now() - startTime)

      // Emit strategy created event
      SignalEngineEvents.strategyCreated(strategy)

      logger.info(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} ${methodName} completed successfully for strategy: ${strategy.name}`
      )

      return strategy
    } catch (error) {
      const errorMessage = sanitizeSignalError(error)
      logger.error(`${SIGNAL_ENGINE_LOG_CATEGORIES.ERROR} ${methodName} failed: ${errorMessage}`)
      throw error instanceof SignalValidationError
        ? error
        : new SignalValidationError(errorMessage, 'strategy_creation')
    }
  }

  /**
   * Get active strategies
   */
  public getActiveStrategies(): Strategy[] {
    return Array.from(this.activeStrategies.values())
  }

  /**
   * Remove a strategy
   */
  public removeStrategy(strategyName: string): boolean {
    const strategy = this.activeStrategies.get(strategyName)
    if (!strategy) {
      logger.warn(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} Strategy not found for removal: ${strategyName}`
      )
      return false
    }

    try {
      // Dispose of strategy resources
      strategy.dispose()

      // Remove from active strategies
      this.activeStrategies.delete(strategyName)

      // Update metrics
      this.updateStrategyMetrics(strategy, 'removed')

      // Clean up signal history for this strategy
      this.cleanupSignalHistory(strategyName)

      // Emit strategy deleted event
      SignalEngineEvents.strategyDeleted(strategyName)

      logger.info(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} Strategy removed successfully: ${strategyName}`
      )

      return true
    } catch (error) {
      logger.error(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.ERROR} Failed to remove strategy ${strategyName}: ${sanitizeSignalError(error)}`
      )
      return false
    }
  }

  /**
   * Process market data through all active strategies
   */
  public processMarketData(data: IndicatorDataPoint): GeneratedSignal[] {
    const methodName = 'processMarketData'
    const startTime = Date.now()

    try {
      if (this.activeStrategies.size === 0) {
        logger.debug(
          `${SIGNAL_ENGINE_LOG_CATEGORIES.SIGNAL_GENERATION} No active strategies to process data`
        )
        return []
      }

      const generatedSignals: GeneratedSignal[] = []

      // Process data through each active strategy
      for (const [strategyName, strategy] of this.activeStrategies) {
        try {
          const signal = strategy.addData(data)
          if (signal) {
            // Validate generated signal
            const validation = validateGeneratedSignal(signal)
            if (validation.isValid) {
              generatedSignals.push(signal)
              this.addToSignalHistory(signal)
              this.updateSignalMetrics(signal)

              // Emit signal generated event
              SignalEngineEvents.signalGenerated(signal, {
                strategy: strategyName,
                processingTime: Date.now() - startTime,
                dataPoint: data
              })

              logger.debug(
                `${SIGNAL_ENGINE_LOG_CATEGORIES.SIGNAL_GENERATION} Signal generated by ${strategyName}: ${signal.signal} (${(signal.confidence * 100).toFixed(1)}%)`
              )
            } else {
              logger.warn(
                `${SIGNAL_ENGINE_LOG_CATEGORIES.VALIDATION} Invalid signal from strategy ${strategyName}: ${validation.errors.join(', ')}`
              )
            }
          }
        } catch (error) {
          logger.error(
            `${SIGNAL_ENGINE_LOG_CATEGORIES.ERROR} Error processing data in strategy ${strategyName}: ${sanitizeSignalError(error)}`
          )
        }
      }

      // Update processing metrics
      this.updateProcessingTime(Date.now() - startTime)
      this.performanceMetrics.lastProcessingTime = Date.now()

      // Perform cleanup if needed
      this.performPeriodicCleanup()

      return generatedSignals
    } catch (error) {
      const errorMessage = sanitizeSignalError(error)
      logger.error(`${SIGNAL_ENGINE_LOG_CATEGORIES.ERROR} ${methodName} failed: ${errorMessage}`)
      return []
    }
  }

  /**
   * Get signal history
   */
  public getSignalHistory(strategyName?: string): GeneratedSignal[] {
    if (strategyName) {
      return this.signalHistory.filter((signal) => signal.strategy === strategyName)
    }
    return [...this.signalHistory]
  }

  /**
   * Clear signal history
   */
  public clearSignalHistory(strategyName?: string): void {
    if (strategyName) {
      const initialLength = this.signalHistory.length
      for (let i = this.signalHistory.length - 1; i >= 0; i--) {
        if (this.signalHistory[i].strategy === strategyName) {
          this.signalHistory.splice(i, 1)
        }
      }
      const removedCount = initialLength - this.signalHistory.length
      logger.info(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.PERFORMANCE} Cleared ${removedCount} signals for strategy: ${strategyName}`
      )
    } else {
      const clearedCount = this.signalHistory.length
      this.signalHistory.length = 0
      logger.info(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.PERFORMANCE} Cleared all ${clearedCount} signals from history`
      )
    }

    this.updateMemoryUsage()
  }

  /**
   * Get engine performance metrics
   */
  public getPerformanceMetrics(): SignalEnginePerformanceMetrics {
    this.updateMemoryUsage()
    return { ...this.performanceMetrics }
  }

  /**
   * Emit performance update event
   */
  public emitPerformanceUpdate(): void {
    const metrics = this.getPerformanceMetrics()
    SignalEngineEvents.performanceUpdate(metrics)
  }

  /**
   * Create single indicator strategy
   */
  private createSingleIndicatorStrategy(
    indicator: string,
    config: Record<string, unknown>
  ): Strategy {
    // Extract indicator-specific config
    const indicatorConfig = config[indicator] || config

    return this.strategyFactory.createSingleIndicatorStrategy(indicator, {
      name: config.name as string,
      description: config.description as string,
      indicatorConfig: indicatorConfig as any,
      signalRules: config.signalRules as any,
      enableRealTime: this.config.enableRealTimeProcessing,
      maxSignalHistory:
        (config.maxSignalHistory as number) || DEFAULT_STRATEGY_CONFIG.maxSignalHistory,
      validateInputs: true
    })
  }

  /**
   * Create multi-indicator strategy
   */
  private createMultiIndicatorStrategy(
    indicators: string[],
    config: Record<string, unknown>
  ): Strategy {
    // Build indicator configs
    const indicatorConfigs: Record<string, any> = {}
    const signalRules: Record<string, any> = {}

    for (const indicator of indicators) {
      indicatorConfigs[indicator] = config[indicator] || { period: 14 }
      signalRules[indicator] = config.signalRules?.[indicator] || undefined
    }

    return this.strategyFactory.createMultiIndicatorStrategy(indicators, {
      name: config.name as string,
      description: config.description as string,
      indicatorConfigs,
      signalRules,
      combinationLogic: (config.combinationLogic as any) || 'MAJORITY',
      weights: config.weights as any,
      enableRealTime: this.config.enableRealTimeProcessing,
      maxSignalHistory:
        (config.maxSignalHistory as number) || DEFAULT_STRATEGY_CONFIG.maxSignalHistory,
      validateInputs: true
    })
  }

  /**
   * Update strategy metrics
   */
  private updateStrategyMetrics(strategy: Strategy, action: 'created' | 'removed'): void {
    if (action === 'created') {
      this.performanceMetrics.totalStrategies++
      this.performanceMetrics.signalsPerStrategy[strategy.name] = 0
    } else if (action === 'removed') {
      delete this.performanceMetrics.signalsPerStrategy[strategy.name]
    }

    this.performanceMetrics.activeStrategies = this.activeStrategies.size
  }

  /**
   * Add signal to history
   */
  private addToSignalHistory(signal: GeneratedSignal): void {
    this.signalHistory.push(signal)

    // Limit history size based on retention period
    const retentionTime = Date.now() - this.config.signalHistoryRetention
    while (this.signalHistory.length > 0 && this.signalHistory[0].timestamp < retentionTime) {
      this.signalHistory.shift()
    }
  }

  /**
   * Update signal metrics
   */
  private updateSignalMetrics(signal: GeneratedSignal): void {
    this.performanceMetrics.totalSignals++

    if (this.performanceMetrics.signalsPerStrategy[signal.strategy] !== undefined) {
      this.performanceMetrics.signalsPerStrategy[signal.strategy]++
    }
  }

  /**
   * Update processing time metrics
   */
  private updateProcessingTime(processingTime: number): void {
    const currentAvg = this.performanceMetrics.averageProcessingTime
    const totalSignals = this.performanceMetrics.totalSignals

    if (totalSignals === 0) {
      this.performanceMetrics.averageProcessingTime = processingTime
    } else {
      this.performanceMetrics.averageProcessingTime =
        (currentAvg * (totalSignals - 1) + processingTime) / totalSignals
    }
  }

  /**
   * Update memory usage metrics
   */
  private updateMemoryUsage(): void {
    this.performanceMetrics.memoryUsage = {
      signalHistory: this.signalHistory.length * 1024, // Rough estimate
      strategies: this.activeStrategies.size * 512, // Rough estimate
      indicators:
        Array.from(this.activeStrategies.values()).reduce(
          (total, strategy) => total + strategy.indicators.size,
          0
        ) * 256
    }
  }

  /**
   * Cleanup signal history for specific strategy
   */
  private cleanupSignalHistory(strategyName: string): void {
    const initialLength = this.signalHistory.length
    for (let i = this.signalHistory.length - 1; i >= 0; i--) {
      if (this.signalHistory[i].strategy === strategyName) {
        this.signalHistory.splice(i, 1)
      }
    }
    const removedCount = initialLength - this.signalHistory.length
    if (removedCount > 0) {
      logger.debug(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.PERFORMANCE} Cleaned up ${removedCount} signals for strategy: ${strategyName}`
      )
    }
  }

  /**
   * Perform periodic cleanup
   */
  private performPeriodicCleanup(): void {
    const now = Date.now()
    if (now - this.lastCleanupTime > PERFORMANCE_CONSTANTS.HISTORY_CLEANUP_INTERVAL) {
      this.cleanupExpiredSignals()
      this.lastCleanupTime = now
    }
  }

  /**
   * Clean up expired signals
   */
  private cleanupExpiredSignals(): void {
    const retentionTime = Date.now() - this.config.signalHistoryRetention
    const initialLength = this.signalHistory.length

    while (this.signalHistory.length > 0 && this.signalHistory[0].timestamp < retentionTime) {
      this.signalHistory.shift()
    }

    const removedCount = initialLength - this.signalHistory.length
    if (removedCount > 0) {
      logger.debug(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.PERFORMANCE} Cleaned up ${removedCount} expired signals`
      )
    }
  }

  /**
   * Start performance monitoring
   */
  private startPerformanceMonitoring(): void {
    setInterval(() => {
      this.updateMemoryUsage()

      // Log performance warnings if needed
      if (
        this.performanceMetrics.averageProcessingTime >
        PERFORMANCE_CONSTANTS.MAX_PROCESSING_TIME_WARNING
      ) {
        logger.warn(
          `${SIGNAL_ENGINE_LOG_CATEGORIES.PERFORMANCE} High average processing time: ${this.performanceMetrics.averageProcessingTime.toFixed(2)}ms`
        )
      }

      const totalMemory = Object.values(this.performanceMetrics.memoryUsage).reduce(
        (sum, val) => sum + val,
        0
      )
      if (totalMemory > PERFORMANCE_CONSTANTS.MEMORY_WARNING_THRESHOLD * 1024 * 1024) {
        logger.warn(
          `${SIGNAL_ENGINE_LOG_CATEGORIES.PERFORMANCE} High memory usage: ${(totalMemory / 1024 / 1024).toFixed(2)}MB`
        )
      }
    }, PERFORMANCE_CONSTANTS.METRICS_UPDATE_INTERVAL)
  }

  /**
   * Dispose of engine resources
   */
  private dispose(): void {
    // Dispose all strategies
    for (const strategy of this.activeStrategies.values()) {
      strategy.dispose()
    }

    // Clear collections
    this.activeStrategies.clear()
    this.signalHistory.length = 0

    logger.info(
      `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} SignalEngine disposed successfully`
    )
  }
}
