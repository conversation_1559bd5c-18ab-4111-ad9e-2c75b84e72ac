/**
 * Signal Persistence System Type Definitions
 * Comprehensive types for saving and loading signal history, strategy configurations, and performance data
 */

import type { GeneratedSignal, Strategy, StrategyConfig } from './signals'
import type { BacktestResult, TradeRecord, PortfolioSnapshot } from './backtesting'

/**
 * Persistence configuration options
 */
export interface PersistenceConfig {
  /** Storage backend type */
  backend: 'file' | 'database' | 'memory' | 'cloud'
  /** Storage location/connection string */
  location: string
  /** Compression settings */
  compression: CompressionConfig
  /** Encryption settings */
  encryption?: EncryptionConfig
  /** Retention policy */
  retention: RetentionConfig
  /** Backup settings */
  backup?: BackupConfig
}

/**
 * Compression configuration
 */
export interface CompressionConfig {
  /** Enable compression */
  enabled: boolean
  /** Compression algorithm */
  algorithm: 'gzip' | 'lz4' | 'zstd'
  /** Compression level (1-9) */
  level: number
}

/**
 * Encryption configuration
 */
export interface EncryptionConfig {
  /** Enable encryption */
  enabled: boolean
  /** Encryption algorithm */
  algorithm: 'aes-256-gcm' | 'chacha20-poly1305'
  /** Encryption key */
  key: string
  /** Key derivation settings */
  keyDerivation: {
    algorithm: 'pbkdf2' | 'scrypt'
    iterations: number
    salt: string
  }
}

/**
 * Retention policy configuration
 */
export interface RetentionConfig {
  /** Maximum age of data in days */
  maxAge: number
  /** Maximum number of records */
  maxRecords: number
  /** Cleanup interval in hours */
  cleanupInterval: number
  /** Archive old data instead of deleting */
  archiveOldData: boolean
}

/**
 * Backup configuration
 */
export interface BackupConfig {
  /** Enable automatic backups */
  enabled: boolean
  /** Backup interval in hours */
  interval: number
  /** Maximum number of backups to keep */
  maxBackups: number
  /** Backup location */
  location: string
}

/**
 * Stored signal record with metadata
 */
export interface StoredSignal extends GeneratedSignal {
  /** Unique storage ID */
  id: string
  /** Storage timestamp */
  storedAt: number
  /** Signal metadata */
  metadata: {
    /** Strategy version */
    strategyVersion?: string
    /** Market conditions */
    marketConditions?: string
    /** Signal source */
    source: string
    /** Additional tags */
    tags?: string[]
  }
}

/**
 * Stored strategy configuration
 */
export interface StoredStrategy {
  /** Unique storage ID */
  id: string
  /** Strategy configuration */
  config: StrategyConfig
  /** Strategy metadata */
  metadata: {
    /** Creation timestamp */
    createdAt: number
    /** Last modified timestamp */
    modifiedAt: number
    /** Strategy version */
    version: string
    /** Description */
    description?: string
    /** Author */
    author?: string
    /** Tags */
    tags?: string[]
  }
  /** Performance history */
  performance?: StrategyPerformanceHistory
}

/**
 * Strategy performance history
 */
export interface StrategyPerformanceHistory {
  /** Performance snapshots over time */
  snapshots: PerformanceSnapshot[]
  /** Backtest results */
  backtests: BacktestResult[]
  /** Live trading results */
  liveResults?: LiveTradingResult[]
}

/**
 * Performance snapshot at a point in time
 */
export interface PerformanceSnapshot {
  /** Snapshot timestamp */
  timestamp: number
  /** Total signals generated */
  totalSignals: number
  /** Win rate percentage */
  winRate: number
  /** Average confidence */
  averageConfidence: number
  /** Sharpe ratio */
  sharpeRatio?: number
  /** Maximum drawdown */
  maxDrawdown?: number
  /** Total return */
  totalReturn?: number
}

/**
 * Live trading result record
 */
export interface LiveTradingResult {
  /** Trading session ID */
  sessionId: string
  /** Start timestamp */
  startTime: number
  /** End timestamp */
  endTime: number
  /** Trades executed */
  trades: TradeRecord[]
  /** Portfolio snapshots */
  portfolioHistory: PortfolioSnapshot[]
  /** Final performance metrics */
  finalMetrics: {
    totalReturn: number
    winRate: number
    sharpeRatio: number
    maxDrawdown: number
  }
}

/**
 * Signal query parameters
 */
export interface SignalQuery {
  /** Strategy name filter */
  strategy?: string
  /** Signal type filter */
  signalType?: 'BUY' | 'SELL' | 'HOLD'
  /** Date range filter */
  dateRange?: {
    start: number
    end: number
  }
  /** Confidence range filter */
  confidenceRange?: {
    min: number
    max: number
  }
  /** Tags filter */
  tags?: string[]
  /** Limit number of results */
  limit?: number
  /** Offset for pagination */
  offset?: number
  /** Sort order */
  sortBy?: 'timestamp' | 'confidence' | 'strategy'
  /** Sort direction */
  sortOrder?: 'asc' | 'desc'
}

/**
 * Strategy query parameters
 */
export interface StrategyQuery {
  /** Strategy name filter */
  name?: string
  /** Author filter */
  author?: string
  /** Tags filter */
  tags?: string[]
  /** Date range filter */
  dateRange?: {
    start: number
    end: number
  }
  /** Performance criteria */
  performance?: {
    minWinRate?: number
    minSharpeRatio?: number
    maxDrawdown?: number
  }
  /** Limit number of results */
  limit?: number
  /** Offset for pagination */
  offset?: number
}

/**
 * Persistence operation result
 */
export interface PersistenceResult<T = unknown> {
  /** Operation success status */
  success: boolean
  /** Result data */
  data?: T
  /** Error message if failed */
  error?: string
  /** Operation metadata */
  metadata?: {
    /** Operation duration */
    duration: number
    /** Records affected */
    recordsAffected?: number
    /** Storage size */
    storageSize?: number
  }
}

/**
 * Batch operation configuration
 */
export interface BatchConfig {
  /** Batch size */
  batchSize: number
  /** Maximum concurrent operations */
  maxConcurrency: number
  /** Retry configuration */
  retry: {
    /** Maximum retry attempts */
    maxAttempts: number
    /** Retry delay in milliseconds */
    delay: number
    /** Exponential backoff multiplier */
    backoffMultiplier: number
  }
}

/**
 * Storage statistics
 */
export interface StorageStats {
  /** Total signals stored */
  totalSignals: number
  /** Total strategies stored */
  totalStrategies: number
  /** Total storage size in bytes */
  totalSize: number
  /** Storage utilization percentage */
  utilization: number
  /** Last cleanup timestamp */
  lastCleanup: number
  /** Performance metrics */
  performance: {
    /** Average read time */
    avgReadTime: number
    /** Average write time */
    avgWriteTime: number
    /** Cache hit rate */
    cacheHitRate: number
  }
}

/**
 * Signal persistence interface
 */
export interface SignalPersistence {
  /** Save a signal */
  saveSignal(signal: GeneratedSignal, metadata?: Record<string, unknown>): Promise<PersistenceResult<string>>

  /** Save multiple signals */
  saveSignals(signals: GeneratedSignal[], metadata?: Record<string, unknown>[]): Promise<PersistenceResult<string[]>>

  /** Load signals by query */
  loadSignals(query: SignalQuery): Promise<PersistenceResult<StoredSignal[]>>

  /** Load signal by ID */
  loadSignal(id: string): Promise<PersistenceResult<StoredSignal>>

  /** Delete signal by ID */
  deleteSignal(id: string): Promise<PersistenceResult<boolean>>

  /** Delete signals by query */
  deleteSignals(query: SignalQuery): Promise<PersistenceResult<number>>
}

/**
 * Strategy persistence interface
 */
export interface StrategyPersistence {
  /** Save a strategy */
  saveStrategy(strategy: StrategyConfig, metadata?: Record<string, unknown>): Promise<PersistenceResult<string>>

  /** Load strategies by query */
  loadStrategies(query: StrategyQuery): Promise<PersistenceResult<StoredStrategy[]>>

  /** Load strategy by ID */
  loadStrategy(id: string): Promise<PersistenceResult<StoredStrategy>>

  /** Update strategy */
  updateStrategy(id: string, updates: Partial<StoredStrategy>): Promise<PersistenceResult<boolean>>

  /** Delete strategy by ID */
  deleteStrategy(id: string): Promise<PersistenceResult<boolean>>

  /** Save strategy performance */
  savePerformance(strategyId: string, performance: PerformanceSnapshot): Promise<PersistenceResult<boolean>>
}

/**
 * Main persistence manager interface
 */
export interface PersistenceManager extends SignalPersistence, StrategyPersistence {
  /** Initialize persistence system */
  initialize(config: PersistenceConfig): Promise<PersistenceResult<boolean>>

  /** Close persistence system */
  close(): Promise<PersistenceResult<boolean>>

  /** Get storage statistics */
  getStats(): Promise<PersistenceResult<StorageStats>>

  /** Perform cleanup */
  cleanup(): Promise<PersistenceResult<number>>

  /** Create backup */
  backup(): Promise<PersistenceResult<string>>

  /** Restore from backup */
  restore(backupPath: string): Promise<PersistenceResult<boolean>>

  /** Export data */
  export(format: 'json' | 'csv' | 'parquet', query?: SignalQuery | StrategyQuery): Promise<PersistenceResult<string>>

  /** Import data */
  import(filePath: string, format: 'json' | 'csv' | 'parquet'): Promise<PersistenceResult<number>>
}

/**
 * Persistence event types
 */
export type PersistenceEventType = 
  | 'signal-saved'
  | 'signal-loaded'
  | 'signal-deleted'
  | 'strategy-saved'
  | 'strategy-loaded'
  | 'strategy-updated'
  | 'strategy-deleted'
  | 'cleanup-completed'
  | 'backup-created'
  | 'error-occurred'

/**
 * Persistence event data
 */
export interface PersistenceEvent {
  type: PersistenceEventType
  timestamp: number
  data: Record<string, unknown>
}

/**
 * Persistence event callback
 */
export type PersistenceEventCallback = (event: PersistenceEvent) => void

/**
 * Export all types for external use
 */
export type {
  PersistenceConfig,
  CompressionConfig,
  EncryptionConfig,
  RetentionConfig,
  BackupConfig,
  StoredSignal,
  StoredStrategy,
  StrategyPerformanceHistory,
  PerformanceSnapshot,
  LiveTradingResult,
  SignalQuery,
  StrategyQuery,
  PersistenceResult,
  BatchConfig,
  StorageStats,
  SignalPersistence,
  StrategyPersistence,
  PersistenceManager,
  PersistenceEvent,
  PersistenceEventCallback,
  PersistenceEventType
}
