/**
 * Signal Broadcaster
 * Enhanced WebSocket event broadcaster for real-time signal distribution
 * Extends the existing SignalEngineEventEmitter with additional signal-specific events
 */

import { <PERSON>rowserWindow } from 'electron'
import { logger } from '../../shared/utils/logger'
import { SignalEngineEventEmitter } from './SignalEngineEventEmitter'
import { WEBSOCKET_CONSTANTS } from '../../shared/constants'
import type {
  GeneratedSignal,
  MarketDataPoint,
  OHLCMarketData,
  SignalGenerationResult
} from '../../shared/types/signals'
import type { BacktestResults } from '../backtesting/BacktestingEngine'

/**
 * Signal event data types
 */
export interface SignalEventData {
  'signal-generated': {
    signal: GeneratedSignal
    marketData: MarketDataPoint | OHLCMarketData
    processingTime: number
    timestamp: number
    metadata?: Record<string, any>
  }
  'signal-batch-update': {
    signals: GeneratedSignal[]
    batchSize: number
    totalProcessed: number
    timestamp: number
  }
  'signal-statistics': {
    totalSignals: number
    signalsByType: Record<'BUY' | 'SELL' | 'HOLD', number>
    signalsBySymbol: Record<string, number>
    averageConfidence: number
    lastSignalTime: number
    processingTimeMs: number
    timestamp: number
  }
  'strategy-performance': {
    strategyName: string
    performance: {
      totalSignals: number
      winRate: number
      averageConfidence: number
      profitFactor: number
    }
    timestamp: number
  }
  'market-data-update': {
    symbol: string
    price: number
    timestamp: number
    volume?: number
    source: 'stream' | 'history' | 'period'
  }
  'indicator-update': {
    symbol: string
    indicator: string
    value: number
    timestamp: number
    metadata?: Record<string, any>
  }
  'backtest-started': {
    config: any
    dataPoints: number
    startTime: number
    timestamp: number
  }
  'backtest-progress': {
    progress: number
    currentDataPoint: number
    totalDataPoints: number
    currentEquity: number
    tradesExecuted: number
    timestamp: number
  }
  'backtest-completed': {
    results: BacktestResults
    duration: number
    timestamp: number
  }
}

/**
 * Signal Broadcaster - Singleton
 */
export class SignalBroadcaster {
  private static instance: SignalBroadcaster | null = null
  private signalEngineEmitter: SignalEngineEventEmitter
  private isEnabled: boolean = true
  private eventQueue: Array<{ event: string; data: any; timestamp: number }> = []
  private batchSize: number = 10
  private batchTimeout: number = 1000 // 1 second
  private batchTimer: NodeJS.Timeout | null = null
  private eventHistory: Array<{ event: string; timestamp: number; dataSize: number }> = []
  private maxHistorySize: number = 1000

  /**
   * Get singleton instance
   */
  public static getInstance(): SignalBroadcaster {
    if (!SignalBroadcaster.instance) {
      SignalBroadcaster.instance = new SignalBroadcaster()
    }
    return SignalBroadcaster.instance
  }

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    this.signalEngineEmitter = SignalEngineEventEmitter.getInstance()
    this.startBatchTimer()
    logger.debug('SignalBroadcaster', 'SignalBroadcaster instance created')
  }

  /**
   * Enable/disable event broadcasting
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
    logger.info('SignalBroadcaster', `Event broadcasting ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Register a browser window for event broadcasting
   */
  public registerWindow(window: BrowserWindow): void {
    this.signalEngineEmitter.registerWindow(window)
    logger.debug('SignalBroadcaster', 'Window registered for signal broadcasting')
  }

  /**
   * Unregister a browser window
   */
  public unregisterWindow(window: BrowserWindow): void {
    this.signalEngineEmitter.unregisterWindow(window)
    logger.debug('SignalBroadcaster', 'Window unregistered from signal broadcasting')
  }

  /**
   * Broadcast signal generated event
   */
  public broadcastSignalGenerated(
    signal: GeneratedSignal,
    marketData: MarketDataPoint | OHLCMarketData,
    processingTime: number,
    metadata?: Record<string, any>
  ): void {
    const eventData: SignalEventData['signal-generated'] = {
      signal,
      marketData,
      processingTime,
      timestamp: Date.now(),
      metadata
    }

    this.emitEvent(WEBSOCKET_CONSTANTS.EVENTS.SIGNAL_GENERATED, eventData)
  }

  /**
   * Broadcast signal batch update
   */
  public broadcastSignalBatch(signals: GeneratedSignal[], totalProcessed: number): void {
    const eventData: SignalEventData['signal-batch-update'] = {
      signals,
      batchSize: signals.length,
      totalProcessed,
      timestamp: Date.now()
    }

    this.emitEvent(WEBSOCKET_CONSTANTS.EVENTS.SIGNAL_BATCH_UPDATE, eventData)
  }

  /**
   * Broadcast signal statistics
   */
  public broadcastSignalStatistics(stats: {
    totalSignals: number
    signalsByType: Record<'BUY' | 'SELL' | 'HOLD', number>
    signalsBySymbol: Record<string, number>
    averageConfidence: number
    lastSignalTime: number
    processingTimeMs: number
  }): void {
    const eventData: SignalEventData['signal-statistics'] = {
      ...stats,
      timestamp: Date.now()
    }

    this.emitEvent(WEBSOCKET_CONSTANTS.EVENTS.SIGNAL_STATISTICS, eventData)
  }

  /**
   * Broadcast strategy performance update
   */
  public broadcastStrategyPerformance(
    strategyName: string,
    performance: {
      totalSignals: number
      winRate: number
      averageConfidence: number
      profitFactor: number
    }
  ): void {
    const eventData: SignalEventData['strategy-performance'] = {
      strategyName,
      performance,
      timestamp: Date.now()
    }

    this.emitEvent(WEBSOCKET_CONSTANTS.EVENTS.STRATEGY_PERFORMANCE, eventData)
  }

  /**
   * Broadcast market data update
   */
  public broadcastMarketDataUpdate(
    symbol: string,
    price: number,
    source: 'stream' | 'history' | 'period',
    volume?: number
  ): void {
    const eventData: SignalEventData['market-data-update'] = {
      symbol,
      price,
      timestamp: Date.now(),
      volume,
      source
    }

    this.emitEvent(WEBSOCKET_CONSTANTS.EVENTS.MARKET_DATA_UPDATE, eventData)
  }

  /**
   * Broadcast indicator update
   */
  public broadcastIndicatorUpdate(
    symbol: string,
    indicator: string,
    value: number,
    metadata?: Record<string, any>
  ): void {
    const eventData: SignalEventData['indicator-update'] = {
      symbol,
      indicator,
      value,
      timestamp: Date.now(),
      metadata
    }

    this.emitEvent(WEBSOCKET_CONSTANTS.EVENTS.INDICATOR_UPDATE, eventData)
  }

  /**
   * Broadcast backtest started
   */
  public broadcastBacktestStarted(config: any, dataPoints: number): void {
    const eventData: SignalEventData['backtest-started'] = {
      config,
      dataPoints,
      startTime: Date.now(),
      timestamp: Date.now()
    }

    this.emitEvent(WEBSOCKET_CONSTANTS.EVENTS.BACKTEST_STARTED, eventData)
  }

  /**
   * Broadcast backtest progress
   */
  public broadcastBacktestProgress(
    progress: number,
    currentDataPoint: number,
    totalDataPoints: number,
    currentEquity: number,
    tradesExecuted: number
  ): void {
    const eventData: SignalEventData['backtest-progress'] = {
      progress,
      currentDataPoint,
      totalDataPoints,
      currentEquity,
      tradesExecuted,
      timestamp: Date.now()
    }

    this.emitEvent(WEBSOCKET_CONSTANTS.EVENTS.BACKTEST_PROGRESS, eventData)
  }

  /**
   * Broadcast backtest completed
   */
  public broadcastBacktestCompleted(results: BacktestResults, duration: number): void {
    const eventData: SignalEventData['backtest-completed'] = {
      results,
      duration,
      timestamp: Date.now()
    }

    this.emitEvent(WEBSOCKET_CONSTANTS.EVENTS.BACKTEST_COMPLETED, eventData)
  }

  /**
   * Queue event for batch processing
   */
  public queueEvent(event: string, data: any): void {
    this.eventQueue.push({
      event,
      data,
      timestamp: Date.now()
    })

    // Process immediately if queue is full
    if (this.eventQueue.length >= this.batchSize) {
      this.processBatchedEvents()
    }
  }

  /**
   * Get broadcasting statistics
   */
  public getStatistics(): {
    isEnabled: boolean
    registeredWindows: number
    eventHistorySize: number
    queuedEvents: number
    recentEvents: Array<{ event: string; timestamp: number; dataSize: number }>
  } {
    return {
      isEnabled: this.isEnabled,
      registeredWindows: this.signalEngineEmitter.getStatus().registeredWindows,
      eventHistorySize: this.eventHistory.length,
      queuedEvents: this.eventQueue.length,
      recentEvents: this.eventHistory.slice(-10) // Last 10 events
    }
  }

  /**
   * Clear event history and queue
   */
  public clearHistory(): void {
    this.eventHistory = []
    this.eventQueue = []
    logger.debug('SignalBroadcaster', 'Event history and queue cleared')
  }

  /**
   * Shutdown the broadcaster
   */
  public shutdown(): void {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer)
      this.batchTimer = null
    }

    this.processBatchedEvents() // Process any remaining events
    this.clearHistory()
    this.isEnabled = false

    logger.info('SignalBroadcaster', 'SignalBroadcaster shutdown completed')
  }

  /**
   * Emit event through SignalEngineEventEmitter
   */
  private emitEvent(event: string, data: any): void {
    if (!this.isEnabled) {
      return
    }

    try {
      // Add to event history
      this.addToEventHistory(event, data)

      // Emit through SignalEngineEventEmitter
      this.signalEngineEmitter.emitSignalEngineEvent(event as any, data)

      logger.debug('SignalBroadcaster', `Broadcasted event: ${event}`, {
        dataSize: JSON.stringify(data).length
      })
    } catch (error) {
      logger.error('SignalBroadcaster', `Failed to emit event ${event}:`, error)
    }
  }

  /**
   * Start batch timer for processing queued events
   */
  private startBatchTimer(): void {
    this.batchTimer = setInterval(() => {
      this.processBatchedEvents()
    }, this.batchTimeout)
  }

  /**
   * Process batched events
   */
  private processBatchedEvents(): void {
    if (this.eventQueue.length === 0) {
      return
    }

    try {
      // Group events by type
      const eventGroups = new Map<string, any[]>()

      for (const queuedEvent of this.eventQueue) {
        if (!eventGroups.has(queuedEvent.event)) {
          eventGroups.set(queuedEvent.event, [])
        }
        eventGroups.get(queuedEvent.event)!.push(queuedEvent.data)
      }

      // Emit grouped events
      for (const [eventType, eventDataArray] of eventGroups) {
        if (eventDataArray.length === 1) {
          // Single event
          this.emitEvent(eventType, eventDataArray[0])
        } else {
          // Batch event
          this.emitEvent(`${eventType}-batch`, {
            events: eventDataArray,
            count: eventDataArray.length,
            timestamp: Date.now()
          })
        }
      }

      // Clear the queue
      this.eventQueue = []

      logger.debug('SignalBroadcaster', `Processed ${eventGroups.size} batched event types`)
    } catch (error) {
      logger.error('SignalBroadcaster', 'Error processing batched events:', error)
    }
  }

  /**
   * Add event to history for tracking
   */
  private addToEventHistory(event: string, data: any): void {
    this.eventHistory.push({
      event,
      timestamp: Date.now(),
      dataSize: JSON.stringify(data).length
    })

    // Maintain history size limit
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.splice(0, this.eventHistory.length - this.maxHistorySize)
    }
  }
}

/**
 * Convenience functions for common signal broadcasting operations
 */
export const SignalBroadcastUtils = {
  /**
   * Broadcast signal with market data
   */
  broadcastSignal: (
    signal: GeneratedSignal,
    marketData: MarketDataPoint | OHLCMarketData,
    processingTime: number = 0,
    metadata?: Record<string, any>
  ) => {
    SignalBroadcaster.getInstance().broadcastSignalGenerated(
      signal,
      marketData,
      processingTime,
      metadata
    )
  },

  /**
   * Broadcast multiple signals as batch
   */
  broadcastSignalBatch: (signals: GeneratedSignal[], totalProcessed: number) => {
    SignalBroadcaster.getInstance().broadcastSignalBatch(signals, totalProcessed)
  },

  /**
   * Broadcast market data update
   */
  broadcastMarketData: (
    symbol: string,
    price: number,
    source: 'stream' | 'history' | 'period',
    volume?: number
  ) => {
    SignalBroadcaster.getInstance().broadcastMarketDataUpdate(symbol, price, source, volume)
  },

  /**
   * Broadcast indicator value update
   */
  broadcastIndicator: (
    symbol: string,
    indicator: string,
    value: number,
    metadata?: Record<string, any>
  ) => {
    SignalBroadcaster.getInstance().broadcastIndicatorUpdate(symbol, indicator, value, metadata)
  },

  /**
   * Get broadcaster statistics
   */
  getStats: () => {
    return SignalBroadcaster.getInstance().getStatistics()
  }
}
