/**
 * Validation Utilities for Broker Service
 * Provides comprehensive validation functions and type guards
 */

import type {
  IPCResponse,
  BrokerStatusData,
  BrokerEventHandlers,
  HeartbeatConfig,
  BrokerServiceConfig
} from '../interfaces/broker'
import type { TradingBotConfig } from '../../../shared/types/trading'
import { VALIDATION, ERROR_MESSAGES } from '../constants/brokerService'

/**
 * Custom error class for validation errors
 */
export class ValidationError extends Error {
  constructor(
    message: string,
    public field?: string
  ) {
    super(message)
    this.name = 'ValidationError'
  }
}

/**
 * Type guard to check if a value is a valid IPCResponse
 * @param value - Value to check
 * @returns True if value is a valid IPCResponse
 */
export function isIPCResponse<T = unknown>(value: unknown): value is IPCResponse<T> {
  if (typeof value !== 'object' || value === null) {
    return false
  }

  const response = value as IPCResponse<T>

  return (
    typeof response.success === 'boolean' &&
    (response.message === undefined || typeof response.message === 'string') &&
    (response.error === undefined || typeof response.error === 'string') &&
    (response.data === undefined || response.data !== null) &&
    (response.config === undefined || isValidTradingBotConfig(response.config))
  )
}

/**
 * Type guard to check if a value is valid BrokerStatusData
 * @param value - Value to check
 * @returns True if value is valid BrokerStatusData
 */
export function isBrokerStatusData(value: unknown): value is BrokerStatusData {
  if (typeof value !== 'object' || value === null) {
    return false
  }

  const data = value as BrokerStatusData

  return (
    typeof data.isConnected === 'boolean' &&
    typeof data.connectionState === 'string' &&
    typeof data.heartbeatHealth === 'string' &&
    typeof data.isBotActive === 'boolean' &&
    isValidHeartbeatStats(data.heartbeatStats)
  )
}

/**
 * Validates heartbeat statistics object
 * @param stats - Heartbeat stats to validate
 * @returns True if stats are valid
 */
function isValidHeartbeatStats(stats: unknown): boolean {
  if (typeof stats !== 'object' || stats === null) {
    return false
  }

  const heartbeatStats = stats as any

  return (
    typeof heartbeatStats.health === 'string' &&
    typeof heartbeatStats.missedBeats === 'number' &&
    typeof heartbeatStats.lastSent === 'number' &&
    typeof heartbeatStats.lastReceived === 'number' &&
    typeof heartbeatStats.isActive === 'boolean' &&
    heartbeatStats.missedBeats >= 0 &&
    heartbeatStats.lastSent >= 0 &&
    heartbeatStats.lastReceived >= 0
  )
}

/**
 * Validates trading bot configuration
 * @param config - Configuration to validate
 * @returns True if configuration is valid
 * @throws ValidationError if configuration is invalid
 */
export function validateTradingBotConfig(config: unknown): asserts config is TradingBotConfig {
  if (!isValidTradingBotConfig(config)) {
    throw new ValidationError(ERROR_MESSAGES.INVALID_CONFIG)
  }
}

/**
 * Type guard for trading bot configuration
 * @param config - Configuration to check
 * @returns True if configuration is valid
 */
function isValidTradingBotConfig(config: unknown): config is TradingBotConfig {
  if (typeof config !== 'object' || config === null) {
    return false
  }

  const botConfig = config as TradingBotConfig

  return (
    typeof botConfig.tradeCapital === 'number' &&
    typeof botConfig.targetProfit === 'number' &&
    typeof botConfig.tradeAmount === 'number' &&
    typeof botConfig.tradeExpiryDuration === 'string' &&
    botConfig.tradeCapital > 0 &&
    botConfig.targetProfit > 0 &&
    botConfig.tradeAmount > 0 &&
    botConfig.tradeAmount <= botConfig.tradeCapital &&
    isValidTradeExpiryDuration(botConfig.tradeExpiryDuration)
  )
}

/**
 * Validates trade expiry duration
 * @param duration - Duration to validate
 * @returns True if duration is valid
 */
function isValidTradeExpiryDuration(duration: string): boolean {
  const validDurations = ['S5', 'S15', 'S30', 'M1', 'M3', 'M5', 'H1', 'H4', 'H5']
  return validDurations.includes(duration)
}

/**
 * Validates broker event handlers object
 * @param handlers - Event handlers to validate
 * @returns True if handlers are valid
 * @throws ValidationError if handlers are invalid
 */
export function validateEventHandlers(handlers: unknown): asserts handlers is BrokerEventHandlers {
  if (typeof handlers !== 'object' || handlers === null) {
    throw new ValidationError('Event handlers must be an object')
  }

  const eventHandlers = handlers as Record<string, unknown>

  for (const [key, handler] of Object.entries(eventHandlers)) {
    if (handler !== undefined && typeof handler !== 'function') {
      throw new ValidationError(`Event handler '${key}' must be a function`, key)
    }
  }
}

/**
 * Validates balance value
 * @param balance - Balance to validate
 * @returns True if balance is valid
 * @throws ValidationError if balance is invalid
 */
export function validateBalance(balance: unknown): asserts balance is number {
  if (typeof balance !== 'number' || isNaN(balance)) {
    throw new ValidationError('Balance must be a valid number')
  }

  if (balance < VALIDATION.MIN_BALANCE || balance > VALIDATION.MAX_BALANCE) {
    throw new ValidationError(
      `Balance must be between ${VALIDATION.MIN_BALANCE} and ${VALIDATION.MAX_BALANCE}`
    )
  }
}

/**
 * Validates heartbeat configuration
 * @param config - Heartbeat config to validate
 * @returns True if config is valid
 * @throws ValidationError if config is invalid
 */
export function validateHeartbeatConfig(config: unknown): asserts config is HeartbeatConfig {
  if (typeof config !== 'object' || config === null) {
    throw new ValidationError('Heartbeat config must be an object')
  }

  const heartbeatConfig = config as HeartbeatConfig

  if (typeof heartbeatConfig.interval !== 'number' || heartbeatConfig.interval <= 0) {
    throw new ValidationError('Heartbeat interval must be a positive number')
  }

  if (typeof heartbeatConfig.timeout !== 'number' || heartbeatConfig.timeout <= 0) {
    throw new ValidationError('Heartbeat timeout must be a positive number')
  }

  if (typeof heartbeatConfig.maxMissedBeats !== 'number' || heartbeatConfig.maxMissedBeats <= 0) {
    throw new ValidationError('Max missed beats must be a positive number')
  }

  if (typeof heartbeatConfig.enabled !== 'boolean') {
    throw new ValidationError('Heartbeat enabled flag must be a boolean')
  }

  if (heartbeatConfig.timeout >= heartbeatConfig.interval) {
    throw new ValidationError('Heartbeat timeout must be less than interval')
  }
}

/**
 * Validates broker service configuration
 * @param config - Service config to validate
 * @returns True if config is valid
 * @throws ValidationError if config is invalid
 */
export function validateBrokerServiceConfig(
  config: unknown
): asserts config is BrokerServiceConfig {
  if (typeof config !== 'object' || config === null) {
    throw new ValidationError('Broker service config must be an object')
  }

  const serviceConfig = config as BrokerServiceConfig

  validateHeartbeatConfig(serviceConfig.heartbeat)

  if (typeof serviceConfig.autoReconnect !== 'boolean') {
    throw new ValidationError('Auto reconnect flag must be a boolean')
  }

  if (
    typeof serviceConfig.maxReconnectAttempts !== 'number' ||
    serviceConfig.maxReconnectAttempts < 0
  ) {
    throw new ValidationError('Max reconnect attempts must be a non-negative number')
  }

  if (typeof serviceConfig.reconnectDelay !== 'number' || serviceConfig.reconnectDelay <= 0) {
    throw new ValidationError('Reconnect delay must be a positive number')
  }
}

/**
 * Sanitizes error message for safe display
 * @param error - Error to sanitize
 * @returns Sanitized error message
 */
export function sanitizeError(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }

  if (typeof error === 'string') {
    return error
  }

  return ERROR_MESSAGES.UNKNOWN_ERROR
}

/**
 * Validates event data size
 * @param data - Data to validate
 * @throws ValidationError if data is too large
 */
export function validateEventDataSize(data: unknown): void {
  try {
    const serialized = JSON.stringify(data)
    const sizeInBytes = new Blob([serialized]).size

    if (sizeInBytes > VALIDATION.MAX_EVENT_SIZE) {
      throw new ValidationError(
        `Event data size (${sizeInBytes} bytes) exceeds maximum allowed size (${VALIDATION.MAX_EVENT_SIZE} bytes)`
      )
    }
  } catch (error) {
    if (error instanceof ValidationError) {
      throw error
    }
    throw new ValidationError('Failed to validate event data size')
  }
}
