/**
 * Header Balance Component
 * Displays the current account balance in the application header
 */

import React from 'react'
import { useFormattedBalance } from '../hooks/useTradingContext'

/**
 * Props for HeaderBalance component
 */
interface HeaderBalanceProps {
  /** Currency symbol to display (default: '$') */
  currencySymbol?: string
  /** Additional CSS classes */
  className?: string
  /** ARIA label for accessibility */
  ariaLabel?: string
}

/**
 * HeaderBalance Component
 * Renders the current account balance with proper formatting and accessibility
 */
export const HeaderBalance: React.FC<HeaderBalanceProps> = ({
  currencySymbol = '$',
  className = '',
  ariaLabel
}) => {
  const formattedBalance = useFormattedBalance(currencySymbol)

  return (
    <div
      className={`flex flex-col items-end ${className}`}
      role="status"
      aria-label={ariaLabel || 'Current account balance'}
      aria-live="polite"
    >
      <span className="text-xs text-gray-400 font-medium uppercase tracking-wide">Balance</span>
      <span className="text-lg font-bold text-white mt-0.5">{formattedBalance}</span>
    </div>
  )
}

export default HeaderBalance
