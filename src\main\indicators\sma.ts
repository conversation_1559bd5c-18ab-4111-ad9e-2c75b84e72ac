/**
 * Simple Moving Average (SMA) Indicator
 * High-performance implementation with O(n) complexity, streaming support, and comprehensive error handling
 */

import { BaseIndicatorClass } from './BaseIndicator'
import { logger } from '../../shared/utils/logger'
import {
  INDICATOR_CONSTANTS,
  INDICATOR_LOG_CATEGORIES,
  DEFAULT_SMA_CONFIG
} from '../../shared/constants/indicators'
import type {
  SMAIndicator,
  SMAConfig,
  IndicatorDataPoint,
  OHLCDataPoint,
  IndicatorOutputPoint,
  IndicatorStreamUpdate
} from '../../shared/types/indicators'

/**
 * Simple Moving Average indicator implementation
 * Uses rolling window optimization for O(n) performance and supports real-time streaming
 */
export class SimpleMovingAverage extends BaseIndicatorClass implements SMAIndicator {
  private _currentSum: number = 0
  private _windowValues: number[] = []
  private _streamingEnabled: boolean = false
  private _streamUpdateCallbacks: ((update: IndicatorStreamUpdate) => void)[] = []

  /**
   * Constructor for Simple Moving Average
   * @param config - SMA configuration
   */
  constructor(config: Partial<SMAConfig> = {}) {
    const fullConfig: SMAConfig = { ...DEFAULT_SMA_CONFIG, ...config }
    super(INDICATOR_CONSTANTS.NAMES.SMA, fullConfig)

    this._streamingEnabled = fullConfig.enableStreaming || false

    logger.debug(
      INDICATOR_LOG_CATEGORIES.CALCULATION,
      `SMA indicator created with period ${this._config.period}`,
      {
        config: fullConfig,
        streamingEnabled: this._streamingEnabled
      }
    )
  }

  /**
   * Get SMA-specific configuration
   */
  public get config(): SMAConfig {
    return super.config as SMAConfig
  }

  /**
   * Get the current sum of values in the window
   * @returns Current sum
   */
  public getCurrentSum(): number {
    return this._currentSum
  }

  /**
   * Get the current average without adding new data
   * @returns Current average value or null if not enough data
   */
  public getCurrentAverage(): number | null {
    if (this._windowValues.length < this._config.period) {
      return null
    }
    return this._currentSum / this._config.period
  }

  /**
   * Add a streaming update callback
   * @param callback - Function to call on streaming updates
   */
  public onStreamUpdate(callback: (update: IndicatorStreamUpdate) => void): void {
    this._streamUpdateCallbacks.push(callback)
  }

  /**
   * Remove a streaming update callback
   * @param callback - Function to remove
   */
  public offStreamUpdate(callback: (update: IndicatorStreamUpdate) => void): void {
    const index = this._streamUpdateCallbacks.indexOf(callback)
    if (index > -1) {
      this._streamUpdateCallbacks.splice(index, 1)
    }
  }

  /**
   * Start streaming mode
   */
  public startStreaming(): void {
    if (!this._streamingEnabled) {
      this._streamingEnabled = true
      this._emitEvent('stream-started', { timestamp: Date.now() })
      logger.debug(INDICATOR_LOG_CATEGORIES.STREAMING, 'SMA streaming started')
    }
  }

  /**
   * Stop streaming mode
   */
  public stopStreaming(): void {
    if (this._streamingEnabled) {
      this._streamingEnabled = false
      this._emitEvent('stream-stopped', { timestamp: Date.now() })
      logger.debug(INDICATOR_LOG_CATEGORIES.STREAMING, 'SMA streaming stopped')
    }
  }

  /**
   * Add a single data point and calculate indicator value using optimized rolling window
   * @param dataPoint - The data point to add
   * @returns The calculated indicator value or null if not enough data
   */
  public addData(dataPoint: IndicatorDataPoint): IndicatorOutputPoint | null {
    const startTime = performance.now()

    try {
      // Validate input
      const validation = this.validateInput([dataPoint])
      if (!validation.isValid) {
        const error = `Invalid data point: ${validation.errors.join(', ')}`
        this._emitEvent('error-occurred', { error, dataPoint })
        throw new Error(error)
      }

      // Add timestamp if not provided
      const processedDataPoint: IndicatorDataPoint = {
        ...dataPoint,
        timestamp: dataPoint.timestamp || Date.now()
      }

      // Add to data points
      this._dataPoints.push(processedDataPoint)
      this._operationCount++

      // Update rolling window for O(1) incremental calculation
      const value = this._extractPriceValue(processedDataPoint)
      this._windowValues.push(value)
      this._currentSum += value

      // Remove old value if window is full
      if (this._windowValues.length > this._config.period) {
        const removedValue = this._windowValues.shift()!
        this._currentSum -= removedValue
      }

      // Manage memory
      this._manageMemory()

      // Calculate new value if we have enough data
      let outputValue: IndicatorOutputPoint | null = null
      if (this._windowValues.length >= this._config.period) {
        const smaValue = this._currentSum / this._config.period
        outputValue = {
          value: smaValue,
          timestamp: processedDataPoint.timestamp || Date.now(),
          index: this._dataPoints.length - 1
        }

        // Apply smoothing if enabled
        if (this.config.useSmoothing && this._outputValues.length > 0) {
          const lastValue = this._outputValues[this._outputValues.length - 1].value
          const smoothingFactor = this.config.smoothingFactor || 0.1
          outputValue.value = lastValue + smoothingFactor * (smaValue - lastValue)
        }

        this._outputValues.push(outputValue)
        this._isReady = true
        this._emitEvent('value-calculated', { outputValue, dataPoint: processedDataPoint })

        // Emit streaming update if enabled
        if (this._streamingEnabled) {
          this._emitStreamUpdate(processedDataPoint, outputValue, true)
        }
      }

      this._emitEvent('data-added', { dataPoint: processedDataPoint, outputValue })

      // Log performance
      const calculationTime = performance.now() - startTime
      this._lastCalculationTime = calculationTime

      if (calculationTime > INDICATOR_CONSTANTS.PERFORMANCE_CONFIG.MAX_CALCULATION_TIME) {
        logger.warn(
          INDICATOR_LOG_CATEGORIES.PERFORMANCE,
          `${this._name} calculation time exceeded threshold`,
          {
            calculationTime,
            threshold: INDICATOR_CONSTANTS.PERFORMANCE_CONFIG.MAX_CALCULATION_TIME
          }
        )
      }

      return outputValue
    } catch (error) {
      logger.error(INDICATOR_LOG_CATEGORIES.ERROR, `${this._name} addData failed`, error)
      this._emitEvent('error-occurred', {
        error: error instanceof Error ? error.message : String(error)
      })
      return null
    }
  }

  /**
   * Reset the indicator to initial state
   */
  public reset(): void {
    super.reset()
    this._currentSum = 0
    this._windowValues = []
    logger.debug(INDICATOR_LOG_CATEGORIES.CALCULATION, 'SMA indicator reset')
  }

  /**
   * Calculate SMA value for a specific index using stateless calculation
   * @param index - Index in the data points array
   * @returns Calculated SMA output point or null
   */
  protected _calculateSingleValue(index: number): IndicatorOutputPoint | null {
    if (index < 0 || index >= this._dataPoints.length) {
      return null
    }

    // Check if we have enough data points for this index
    if (index < this._config.period - 1) {
      return null
    }

    const dataPoint = this._dataPoints[index]

    // Calculate SMA for the window ending at this index
    let sum = 0
    for (let i = index - this._config.period + 1; i <= index; i++) {
      const value = this._extractPriceValue(this._dataPoints[i])
      sum += value
    }

    const smaValue = sum / this._config.period
    const outputPoint: IndicatorOutputPoint = {
      value: smaValue,
      timestamp: dataPoint.timestamp || Date.now(),
      index
    }

    // Apply smoothing if enabled (only for incremental updates, not full recalculation)
    if (
      this.config.useSmoothing &&
      this._outputValues.length > 0 &&
      index === this._dataPoints.length - 1
    ) {
      const lastValue = this._outputValues[this._outputValues.length - 1].value
      const smoothingFactor = this.config.smoothingFactor || 0.1
      outputPoint.value = lastValue + smoothingFactor * (smaValue - lastValue)
    }

    return outputPoint
  }

  /**
   * Extract price value based on configured price type
   * @param dataPoint - Input data point
   * @returns Extracted price value
   */
  private _extractPriceValue(dataPoint: IndicatorDataPoint): number {
    const priceType = this.config.priceType || 'close'
    const ohlcData = dataPoint as OHLCDataPoint

    switch (priceType) {
      case 'close': {
        return dataPoint.value
      }
      case 'open': {
        return ohlcData.open || dataPoint.value
      }
      case 'high': {
        return ohlcData.high || dataPoint.value
      }
      case 'low': {
        return ohlcData.low || dataPoint.value
      }
      case 'typical': {
        const high = ohlcData.high || dataPoint.value
        const low = ohlcData.low || dataPoint.value
        const close = dataPoint.value
        return (high + low + close) / 3
      }
      case 'weighted': {
        const wHigh = ohlcData.high || dataPoint.value
        const wLow = ohlcData.low || dataPoint.value
        const wClose = dataPoint.value
        return (wHigh + wLow + 2 * wClose) / 4
      }
      default: {
        return dataPoint.value
      }
    }
  }

  /**
   * Emit streaming update to registered callbacks
   * @param dataPoint - Input data point
   * @param outputPoint - Calculated output point
   * @param isNewValue - Whether this is a new value or update
   */
  private _emitStreamUpdate(
    dataPoint: IndicatorDataPoint,
    outputPoint: IndicatorOutputPoint,
    isNewValue: boolean
  ): void {
    const streamUpdate: IndicatorStreamUpdate = {
      dataPoint,
      indicatorValue: outputPoint,
      isNewValue,
      updateTimestamp: Date.now()
    }

    this._streamUpdateCallbacks.forEach((callback) => {
      try {
        callback(streamUpdate)
      } catch (error) {
        logger.error(INDICATOR_LOG_CATEGORIES.ERROR, 'SMA stream update callback failed', {
          error,
          streamUpdate
        })
      }
    })
  }

  /**
   * Override memory management to handle rolling window state
   */
  protected _manageMemory(): void {
    if (!INDICATOR_CONSTANTS.MEMORY_MANAGEMENT.AUTO_CLEANUP) return

    const maxHistory =
      this._config.maxHistorySize || INDICATOR_CONSTANTS.MEMORY_MANAGEMENT.MAX_RETAINED_HISTORY

    // Trim data points if exceeding max history
    if (this._dataPoints.length > maxHistory) {
      const excessCount = this._dataPoints.length - maxHistory
      this._dataPoints.splice(0, excessCount)

      // Also trim output values proportionally
      if (this._outputValues.length > maxHistory - this._config.period + 1) {
        const outputExcess = this._outputValues.length - (maxHistory - this._config.period + 1)
        this._outputValues.splice(0, outputExcess)
      }

      // Reset rolling window state when trimming data
      this._windowValues = []
      this._currentSum = 0

      // Rebuild rolling window from the last period's worth of data
      const startIndex = Math.max(0, this._dataPoints.length - this._config.period)
      for (let i = startIndex; i < this._dataPoints.length; i++) {
        const value = this._extractPriceValue(this._dataPoints[i])
        this._windowValues.push(value)
        this._currentSum += value
      }

      logger.debug(INDICATOR_LOG_CATEGORIES.PERFORMANCE, `${this._name} memory cleanup performed`, {
        removedDataPoints: excessCount,
        remainingDataPoints: this._dataPoints.length,
        rebuiltWindow: this._windowValues.length
      })
    }
  }

  /**
   * Validate SMA-specific configuration
   */
  protected _validateConfig(): void {
    super._validateConfig()

    const smaConfig = this.config

    if (smaConfig.smoothingFactor !== undefined) {
      if (
        smaConfig.smoothingFactor <
          INDICATOR_CONSTANTS.VALIDATION_CONSTRAINTS.smoothingFactor.min ||
        smaConfig.smoothingFactor > INDICATOR_CONSTANTS.VALIDATION_CONSTRAINTS.smoothingFactor.max
      ) {
        throw new Error(
          `Smoothing factor must be between ${INDICATOR_CONSTANTS.VALIDATION_CONSTRAINTS.smoothingFactor.min} and ${INDICATOR_CONSTANTS.VALIDATION_CONSTRAINTS.smoothingFactor.max}`
        )
      }
    }
  }
}

/**
 * Factory function to create SMA indicator
 * @param config - SMA configuration
 * @returns SMA indicator instance
 */
export function createSMA(config: Partial<SMAConfig> = {}): SimpleMovingAverage {
  return new SimpleMovingAverage(config)
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use SimpleMovingAverage class or createSMA factory function instead
 * @param prices - Array of price values
 * @param period - Period for SMA calculation
 * @returns Array of SMA values
 */
export const calculateSMA = (prices: number[], period: number): number[] => {
  logger.warn(
    INDICATOR_LOG_CATEGORIES.CALCULATION,
    'Using deprecated calculateSMA function. Consider using SimpleMovingAverage class for better performance.'
  )

  if (prices.length < period) return []

  const sma = createSMA({ period })
  const dataPoints: IndicatorDataPoint[] = prices.map((price, index) => ({
    value: price,
    timestamp: Date.now() + index
  }))

  sma.addDataBatch(dataPoints)
  return sma.getValues().map((point) => point.value)
}

// Export the main class as default
export default SimpleMovingAverage
