/**
 * Market Data Transformation Utilities
 * Converts PocketOption broker data formats to standardized formats for signal generation
 */

import { logger } from './logger'
import type { 
  MarketDataPoint, 
  OHLCMarketData 
} from '../types/signals'
import type { IndicatorDataPoint } from '../types/indicators'

/**
 * Transform StreamData to MarketDataPoint
 * StreamData format: [symbol: string, timestamp: number, price: number | null]
 */
export function transformStreamData(
  streamData: StreamData,
  source: 'stream' = 'stream'
): MarketDataPoint | null {
  try {
    const [symbol, timestamp, price] = streamData

    // Validate data
    if (!symbol || typeof symbol !== 'string') {
      logger.warn('MarketDataTransformer', 'Invalid symbol in stream data:', symbol)
      return null
    }

    if (!timestamp || typeof timestamp !== 'number' || timestamp <= 0) {
      logger.warn('MarketDataTransformer', 'Invalid timestamp in stream data:', timestamp)
      return null
    }

    if (price === null || typeof price !== 'number' || price <= 0) {
      logger.warn('MarketDataTransformer', 'Invalid price in stream data:', price)
      return null
    }

    return {
      symbol,
      timestamp: timestamp * 1000, // Convert to milliseconds if needed
      price,
      source
    }
  } catch (error) {
    logger.error('MarketDataTransformer', 'Error transforming stream data:', error)
    return null
  }
}

/**
 * Transform History data to MarketDataPoint array
 * History format: [time: number, price: number][]
 */
export function transformHistoryData(
  historyData: HistoryNewFastData,
  source: 'history' = 'history'
): MarketDataPoint[] {
  try {
    const { asset, history } = historyData
    const transformedData: MarketDataPoint[] = []

    if (!asset || !Array.isArray(history)) {
      logger.warn('MarketDataTransformer', 'Invalid history data structure')
      return []
    }

    for (const [time, price] of history) {
      if (typeof time === 'number' && typeof price === 'number' && time > 0 && price > 0) {
        transformedData.push({
          symbol: asset,
          timestamp: time * 1000, // Convert to milliseconds if needed
          price,
          source
        })
      }
    }

    logger.debug('MarketDataTransformer', `Transformed ${transformedData.length} history points for ${asset}`)
    return transformedData
  } catch (error) {
    logger.error('MarketDataTransformer', 'Error transforming history data:', error)
    return []
  }
}

/**
 * Transform PeriodData to OHLCMarketData array
 * PeriodData format: { symbol_id, time, open, close, high, low }[]
 */
export function transformPeriodData(
  periodData: HistoryPeriodData,
  source: 'period' = 'period'
): OHLCMarketData[] {
  try {
    const { asset, data } = periodData
    const transformedData: OHLCMarketData[] = []

    if (!asset || !Array.isArray(data)) {
      logger.warn('MarketDataTransformer', 'Invalid period data structure')
      return []
    }

    for (const candle of data) {
      const { time, open, high, low, close } = candle

      // Validate OHLC data
      if (
        typeof time === 'number' && time > 0 &&
        typeof open === 'number' && open > 0 &&
        typeof high === 'number' && high > 0 &&
        typeof low === 'number' && low > 0 &&
        typeof close === 'number' && close > 0 &&
        high >= Math.max(open, close) &&
        low <= Math.min(open, close)
      ) {
        transformedData.push({
          symbol: asset,
          timestamp: time * 1000, // Convert to milliseconds if needed
          open,
          high,
          low,
          close,
          source
        })
      } else {
        logger.warn('MarketDataTransformer', 'Invalid OHLC data:', candle)
      }
    }

    logger.debug('MarketDataTransformer', `Transformed ${transformedData.length} period candles for ${asset}`)
    return transformedData
  } catch (error) {
    logger.error('MarketDataTransformer', 'Error transforming period data:', error)
    return []
  }
}

/**
 * Transform Candle data to OHLCMarketData array
 * Candle format: [time: number, open: number, high: number, low: number, close: number][]
 */
export function transformCandleData(
  candleData: Candle[],
  symbol: string,
  source: 'candle' = 'candle'
): OHLCMarketData[] {
  try {
    const transformedData: OHLCMarketData[] = []

    if (!Array.isArray(candleData) || !symbol) {
      logger.warn('MarketDataTransformer', 'Invalid candle data or symbol')
      return []
    }

    for (const [time, open, high, low, close] of candleData) {
      // Validate OHLC data
      if (
        typeof time === 'number' && time > 0 &&
        typeof open === 'number' && open > 0 &&
        typeof high === 'number' && high > 0 &&
        typeof low === 'number' && low > 0 &&
        typeof close === 'number' && close > 0 &&
        high >= Math.max(open, close) &&
        low <= Math.min(open, close)
      ) {
        transformedData.push({
          symbol,
          timestamp: time * 1000, // Convert to milliseconds if needed
          open,
          high,
          low,
          close,
          source
        })
      } else {
        logger.warn('MarketDataTransformer', 'Invalid candle data:', [time, open, high, low, close])
      }
    }

    logger.debug('MarketDataTransformer', `Transformed ${transformedData.length} candles for ${symbol}`)
    return transformedData
  } catch (error) {
    logger.error('MarketDataTransformer', 'Error transforming candle data:', error)
    return []
  }
}

/**
 * Convert MarketDataPoint to IndicatorDataPoint for indicator processing
 */
export function marketDataToIndicatorData(marketData: MarketDataPoint): IndicatorDataPoint {
  return {
    value: marketData.price,
    timestamp: marketData.timestamp,
    volume: marketData.volume
  }
}

/**
 * Convert OHLCMarketData to IndicatorDataPoint using close price
 */
export function ohlcDataToIndicatorData(ohlcData: OHLCMarketData, priceType: 'open' | 'high' | 'low' | 'close' = 'close'): IndicatorDataPoint {
  let value: number
  
  switch (priceType) {
    case 'open':
      value = ohlcData.open
      break
    case 'high':
      value = ohlcData.high
      break
    case 'low':
      value = ohlcData.low
      break
    case 'close':
    default:
      value = ohlcData.close
      break
  }

  return {
    value,
    timestamp: ohlcData.timestamp,
    volume: ohlcData.volume
  }
}

/**
 * Validate market data point
 */
export function validateMarketDataPoint(data: MarketDataPoint): boolean {
  return (
    typeof data.symbol === 'string' && data.symbol.length > 0 &&
    typeof data.timestamp === 'number' && data.timestamp > 0 &&
    typeof data.price === 'number' && data.price > 0 &&
    ['stream', 'history', 'period'].includes(data.source)
  )
}

/**
 * Validate OHLC market data
 */
export function validateOHLCMarketData(data: OHLCMarketData): boolean {
  return (
    typeof data.symbol === 'string' && data.symbol.length > 0 &&
    typeof data.timestamp === 'number' && data.timestamp > 0 &&
    typeof data.open === 'number' && data.open > 0 &&
    typeof data.high === 'number' && data.high > 0 &&
    typeof data.low === 'number' && data.low > 0 &&
    typeof data.close === 'number' && data.close > 0 &&
    data.high >= Math.max(data.open, data.close) &&
    data.low <= Math.min(data.open, data.close) &&
    ['candle', 'period'].includes(data.source)
  )
}

/**
 * Utility to get the latest data point from an array
 */
export function getLatestDataPoint<T extends { timestamp: number }>(dataPoints: T[]): T | null {
  if (!Array.isArray(dataPoints) || dataPoints.length === 0) {
    return null
  }

  return dataPoints.reduce((latest, current) => 
    current.timestamp > latest.timestamp ? current : latest
  )
}

/**
 * Utility to filter data points by time range
 */
export function filterDataByTimeRange<T extends { timestamp: number }>(
  dataPoints: T[],
  startTime: number,
  endTime: number
): T[] {
  return dataPoints.filter(point => 
    point.timestamp >= startTime && point.timestamp <= endTime
  )
}

/**
 * Utility to sort data points by timestamp
 */
export function sortDataByTimestamp<T extends { timestamp: number }>(dataPoints: T[]): T[] {
  return [...dataPoints].sort((a, b) => a.timestamp - b.timestamp)
}
