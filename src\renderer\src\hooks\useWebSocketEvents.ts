/**
 * WebSocket Events Hook
 * Custom hook for handling WebSocket events from the main process with comprehensive
 * error handling, type safety, and validation following established patterns
 */

import { useEffect, useCallback, useRef } from 'react'
import { logger } from '../../../shared/utils/logger'
import { BROKER_EVENT_CHANNEL, WEBSOCKET_CONFIG } from '../constants/websocket'
import {
  handleWebSocketError,
  logEventReception,
  validateEventDataSize,
  sanitizeEventData,
  withTimeout,
  withRetry
} from '../utils/websocket'
import type { WebSocketEventConfig, BrokerEventData } from '../types/websocket'

/**
 * Logger category for WebSocket hooks
 */
const LOG_CATEGORY = 'WebSocketHooks' as const

/**
 * Hook for listening to WebSocket events from the main process
 * Provides comprehensive error handling, validation, and logging
 *
 * @template T - The type of data expected by the event handler
 * @param config - Event listener configuration with type safety
 * @returns Cleanup function to remove the listener
 *
 * @example
 * ```typescript
 * const cleanup = useWebSocketEvent({
 *   eventName: WEBSOCKET_EVENTS.BALANCE_UPDATE,
 *   handler: (data: BalanceUpdateData) => {
 *     console.log('Balance updated:', data.balance)
 *   },
 *   enabled: true
 * })
 * ```
 */
export const useWebSocketEvent = <T = BrokerEventData>(
  config: WebSocketEventConfig<T>
): (() => void) => {
  const { eventName, handler, enabled = WEBSOCKET_CONFIG.DEFAULT_ENABLED } = config
  const handlerRef = useRef(handler)
  const cleanupRef = useRef<(() => void) | null>(null)

  // Update handler ref when handler changes
  useEffect(() => {
    handlerRef.current = handler
  }, [handler])

  /**
   * Enhanced broker event handler with error handling and validation
   */
  const handleBrokerEvent = useCallback(
    (...args: unknown[]) => {
      try {
        // Extract event name and data from args
        const [event, data] = args

        // Validate that we have the expected arguments
        if (typeof event !== 'string') {
          logger.warn(LOG_CATEGORY, 'Invalid broker event: event name is not a string', args)
          return
        }

        // Only process events that match our target event name
        if (event !== eventName) {
          return
        }

        // Validate event data size to prevent memory issues
        if (!validateEventDataSize(data)) {
          logger.warn(LOG_CATEGORY, `Event '${eventName}' data size validation failed`)
          return
        }

        // Log event reception for debugging
        logEventReception(eventName, sanitizeEventData(data), true)

        // Execute the handler with timeout and retry protection
        const protectedHandler = withTimeout(
          withRetry(handlerRef.current),
          WEBSOCKET_CONFIG.DEFAULT_TIMEOUT
        )

        protectedHandler(data as T)
      } catch (error) {
        handleWebSocketError(error, eventName, sanitizeEventData(args))
      }
    },
    [eventName]
  )

  useEffect(() => {
    if (!enabled) {
      logger.debug(LOG_CATEGORY, `Event listener for '${eventName}' is disabled`)
      return
    }

    try {
      logger.debug(LOG_CATEGORY, `Setting up event listener for '${eventName}'`)

      // Set up the event listener for broker events
      const cleanup = window.api.on(BROKER_EVENT_CHANNEL, handleBrokerEvent)
      cleanupRef.current = cleanup

      return () => {
        logger.debug(LOG_CATEGORY, `Cleaning up event listener for '${eventName}'`)
        cleanup()
        cleanupRef.current = null
      }
    } catch (error) {
      handleWebSocketError(error, eventName)
      return undefined
    }
  }, [handleBrokerEvent, enabled, eventName])

  /**
   * Manual cleanup function for external cleanup if needed
   * This provides a consistent API for manual cleanup while the automatic
   * cleanup is handled by the useEffect return function
   */
  return useCallback(() => {
    if (cleanupRef.current) {
      logger.debug(LOG_CATEGORY, `Manual cleanup requested for '${eventName}'`)
      cleanupRef.current()
      cleanupRef.current = null
    }
  }, [eventName])
}

/**
 * Hook specifically for listening to balance updates with proper validation
 * Provides type-safe balance update handling with comprehensive error checking
 *
 * @param onBalanceUpdate - Function to call when balance is updated
 * @param enabled - Whether the listener is enabled (default: true)
 * @returns Cleanup function to remove the listener
 *
 * @example
 * ```typescript
 * const cleanup = useBalanceWebSocketEvent(
 *   (balance) => setAccountBalance(balance),
 *   true
 * )
 * ```
 */
export const useBalanceWebSocketEvent = (
  onBalanceUpdate: (balance: number) => void,
  enabled: boolean = WEBSOCKET_CONFIG.DEFAULT_ENABLED
): (() => void) => {
  const handleBalanceUpdate = useCallback(
    (data: unknown) => {
      try {
        // Enhanced validation for balance data
        if (typeof data === 'number' && Number.isFinite(data)) {
          // Direct number (legacy format)
          onBalanceUpdate(data)
          logger.debug(LOG_CATEGORY, `Balance updated: ${data}`)
        } else if (typeof data === 'object' && data !== null && 'balance' in data) {
          // Object format with balance property
          const balanceData = data as { balance: number }
          if (typeof balanceData.balance === 'number' && Number.isFinite(balanceData.balance)) {
            onBalanceUpdate(balanceData.balance)
            logger.debug(LOG_CATEGORY, `Balance updated: ${balanceData.balance}`)
          } else {
            logger.warn(
              LOG_CATEGORY,
              'Invalid balance data: balance property is not a finite number',
              data
            )
          }
        } else {
          logger.warn(LOG_CATEGORY, 'Invalid balance data format received', data)
        }
      } catch (error) {
        handleWebSocketError(error, 'balance', data)
      }
    },
    [onBalanceUpdate]
  )

  return useWebSocketEvent({
    eventName: 'balance', // This matches BROADCAST_EVENTS.ACCOUNT_BALANCE
    handler: handleBalanceUpdate,
    enabled
  })
}

/**
 * Hook for listening to connection state changes with proper validation
 * Provides type-safe connection state handling with enhanced error checking
 *
 * @param onConnectionChange - Function to call when connection state changes
 * @param enabled - Whether the listener is enabled (default: true)
 * @returns Cleanup function to remove the listener
 *
 * @example
 * ```typescript
 * const cleanup = useConnectionWebSocketEvent(
 *   (stateData) => {
 *     console.log(`Connection changed from ${stateData.from} to ${stateData.to}`)
 *   },
 *   true
 * )
 * ```
 */
export const useConnectionWebSocketEvent = (
  onConnectionChange: (stateData: { from: string; to: string; timestamp: number }) => void,
  enabled: boolean = WEBSOCKET_CONFIG.DEFAULT_ENABLED
): (() => void) => {
  const handleConnectionChange = useCallback(
    (data: unknown) => {
      try {
        if (
          typeof data === 'object' &&
          data !== null &&
          'from' in data &&
          'to' in data &&
          'timestamp' in data
        ) {
          const stateData = data as { from: string; to: string; timestamp: number }

          if (
            typeof stateData.from === 'string' &&
            typeof stateData.to === 'string' &&
            typeof stateData.timestamp === 'number'
          ) {
            onConnectionChange(stateData)
            logger.debug(
              LOG_CATEGORY,
              `Connection state changed: ${stateData.from} -> ${stateData.to}`
            )
          } else {
            logger.warn(LOG_CATEGORY, 'Invalid connection state data types', data)
          }
        } else if (typeof data === 'string') {
          // Legacy format - just the state string
          logger.debug(LOG_CATEGORY, `Connection state (legacy format): ${data}`)
          onConnectionChange({ from: 'unknown', to: data, timestamp: Date.now() })
        } else {
          logger.warn(LOG_CATEGORY, 'Invalid connection state data format', data)
        }
      } catch (error) {
        handleWebSocketError(error, 'state_change', data)
      }
    },
    [onConnectionChange]
  )

  return useWebSocketEvent({
    eventName: 'state_change', // This matches BROADCAST_EVENTS.STATE_CHANGE
    handler: handleConnectionChange,
    enabled
  })
}

/**
 * Hook for listening to heartbeat events with proper validation
 * Provides type-safe heartbeat event handling for monitoring connection health
 *
 * @param onHeartbeatEvent - Function to call when heartbeat events occur
 * @param enabled - Whether the listener is enabled (default: true)
 * @returns Cleanup function to remove the listener
 *
 * @example
 * ```typescript
 * const cleanup = useHeartbeatWebSocketEvent(
 *   (heartbeatData) => {
 *     console.log(`Heartbeat: ${heartbeatData.missedBeats} missed beats`)
 *   },
 *   true
 * )
 * ```
 */
export const useHeartbeatWebSocketEvent = (
  onHeartbeatEvent: (heartbeatData: {
    timestamp: number
    missedBeats: number
    responseTime?: number
  }) => void,
  enabled: boolean = WEBSOCKET_CONFIG.DEFAULT_ENABLED
): (() => void) => {
  const handleHeartbeatEvent = useCallback(
    (data: unknown) => {
      try {
        if (
          typeof data === 'object' &&
          data !== null &&
          'timestamp' in data &&
          'missedBeats' in data
        ) {
          const heartbeatData = data as {
            timestamp: number
            missedBeats: number
            responseTime?: number
          }

          if (
            typeof heartbeatData.timestamp === 'number' &&
            typeof heartbeatData.missedBeats === 'number' &&
            Number.isFinite(heartbeatData.timestamp) &&
            Number.isInteger(heartbeatData.missedBeats) &&
            heartbeatData.missedBeats >= 0
          ) {
            onHeartbeatEvent(heartbeatData)
            logger.debug(LOG_CATEGORY, `Heartbeat event: ${heartbeatData.missedBeats} missed beats`)
          } else {
            logger.warn(LOG_CATEGORY, 'Invalid heartbeat data types or values', data)
          }
        } else {
          logger.warn(LOG_CATEGORY, 'Invalid heartbeat data format', data)
        }
      } catch (error) {
        handleWebSocketError(error, 'heartbeat', data)
      }
    },
    [onHeartbeatEvent]
  )

  return useWebSocketEvent({
    eventName: 'heartbeat_received', // This matches BROADCAST_EVENTS.HEARTBEAT_RECEIVED
    handler: handleHeartbeatEvent,
    enabled
  })
}

/**
 * Hook for listening to error events with proper validation
 * Provides type-safe error event handling for monitoring broker errors
 *
 * @param onErrorEvent - Function to call when error events occur
 * @param enabled - Whether the listener is enabled (default: true)
 * @returns Cleanup function to remove the listener
 *
 * @example
 * ```typescript
 * const cleanup = useErrorWebSocketEvent(
 *   (errorData) => {
 *     console.error(`Broker error: ${errorData.error}`)
 *   },
 *   true
 * )
 * ```
 */
export const useErrorWebSocketEvent = (
  onErrorEvent: (errorData: {
    error: string
    code?: string | number
    details?: Record<string, unknown>
  }) => void,
  enabled: boolean = WEBSOCKET_CONFIG.DEFAULT_ENABLED
): (() => void) => {
  const handleErrorEvent = useCallback(
    (data: unknown) => {
      try {
        if (typeof data === 'object' && data !== null && 'error' in data) {
          const errorData = data as {
            error: string
            code?: string | number
            details?: Record<string, unknown>
          }

          if (typeof errorData.error === 'string') {
            onErrorEvent(errorData)
            logger.debug(LOG_CATEGORY, `Error event received: ${errorData.error}`)
          } else {
            logger.warn(LOG_CATEGORY, 'Invalid error data: error is not a string', data)
          }
        } else if (typeof data === 'string') {
          // Legacy format - just the error string
          onErrorEvent({ error: data })
          logger.debug(LOG_CATEGORY, `Error event (legacy format): ${data}`)
        } else {
          logger.warn(LOG_CATEGORY, 'Invalid error data format', data)
        }
      } catch (error) {
        handleWebSocketError(error, 'error', data)
      }
    },
    [onErrorEvent]
  )

  return useWebSocketEvent({
    eventName: 'error', // This matches BROADCAST_EVENTS.ERROR
    handler: handleErrorEvent,
    enabled
  })
}

/**
 * Hook for listening to multiple WebSocket events efficiently
 * This hook properly handles multiple event listeners without violating React hooks rules
 *
 * @param configs - Array of event listener configurations
 * @returns Object containing cleanup functions for each event
 *
 * @example
 * ```typescript
 * const { cleanupBalance, cleanupConnection } = useMultipleWebSocketEvents({
 *   balance: {
 *     eventName: 'balance',
 *     handler: (data) => console.log('Balance:', data),
 *     enabled: true
 *   },
 *   connection: {
 *     eventName: 'state_change',
 *     handler: (data) => console.log('Connection:', data),
 *     enabled: true
 *   }
 * })
 * ```
 */
export const useMultipleWebSocketEvents = <T extends Record<string, WebSocketEventConfig>>(
  configs: T
): Record<keyof T, () => void> => {
  const cleanupFunctions = {} as Record<keyof T, () => void>

  // Create individual event listeners for each config
  Object.entries(configs).forEach(([key, config]) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    cleanupFunctions[key as keyof T] = useWebSocketEvent(config)
  })

  return cleanupFunctions
}

/**
 * Signal Engine WebSocket Event Hooks
 * Enhanced hooks specifically for Signal Engine real-time events
 */

/**
 * Hook for listening to Signal Engine events
 * Provides comprehensive Signal Engine event handling with type safety
 */
export const useSignalEngineEvent = <T = any>(
  eventType: string,
  handler: (data: T) => void,
  enabled: boolean = true
): (() => void) => {
  const eventHandlerRef = useRef(handler)
  const cleanupRef = useRef<(() => void) | null>(null)

  // Update handler ref when handler changes
  useEffect(() => {
    eventHandlerRef.current = handler
  }, [handler])

  useEffect(() => {
    if (!enabled) {
      return
    }

    const handleSignalEngineEvent = (event: any) => {
      try {
        if (event.type === eventType) {
          eventHandlerRef.current(event.data)
        }
      } catch (error) {
        logger.error(LOG_CATEGORY, `Error handling Signal Engine event ${eventType}:`, error)
      }
    }

    // Listen for Signal Engine events
    window.api.on('signal-engine-event', handleSignalEngineEvent)

    cleanupRef.current = () => {
      window.api.removeListener('signal-engine-event', handleSignalEngineEvent)
    }

    return cleanupRef.current
  }, [eventType, enabled])

  return useCallback(() => {
    if (cleanupRef.current) {
      cleanupRef.current()
    }
  }, [])
}

/**
 * Hook for listening to signal generation events
 */
export const useSignalGeneratedEvent = (
  onSignalGenerated: (signal: any) => void,
  enabled: boolean = true
): (() => void) => {
  return useSignalEngineEvent('signal-generated', onSignalGenerated, enabled)
}

/**
 * Hook for listening to strategy events
 */
export const useStrategyEvents = (
  handlers: {
    onStrategyCreated?: (strategy: any) => void
    onStrategyUpdated?: (data: { strategy: any; changes: any }) => void
    onStrategyDeleted?: (data: { strategyId: string }) => void
  },
  enabled: boolean = true
): (() => void) => {
  const cleanup1 = useSignalEngineEvent(
    'strategy-created',
    handlers.onStrategyCreated || (() => {}),
    enabled && !!handlers.onStrategyCreated
  )
  const cleanup2 = useSignalEngineEvent(
    'strategy-updated',
    handlers.onStrategyUpdated || (() => {}),
    enabled && !!handlers.onStrategyUpdated
  )
  const cleanup3 = useSignalEngineEvent(
    'strategy-deleted',
    handlers.onStrategyDeleted || (() => {}),
    enabled && !!handlers.onStrategyDeleted
  )

  return useCallback(() => {
    cleanup1()
    cleanup2()
    cleanup3()
  }, [cleanup1, cleanup2, cleanup3])
}

/**
 * Hook for listening to performance updates
 */
export const usePerformanceUpdateEvent = (
  onPerformanceUpdate: (metrics: any) => void,
  enabled: boolean = true
): (() => void) => {
  return useSignalEngineEvent('performance-update', onPerformanceUpdate, enabled)
}

/**
 * Hook for listening to Signal Engine connection status
 */
export const useSignalEngineConnectionEvent = (
  onConnectionStatusChange: (status: { status: string; details?: string }) => void,
  enabled: boolean = true
): (() => void) => {
  return useSignalEngineEvent('connection-status', onConnectionStatusChange, enabled)
}

/**
 * Hook for listening to Signal Engine errors
 */
export const useSignalEngineErrorEvent = (
  onError: (error: { error: string; code?: string; severity: string; context?: any }) => void,
  enabled: boolean = true
): (() => void) => {
  return useSignalEngineEvent('error', onError, enabled)
}

/**
 * Hook for listening to Signal Engine heartbeat
 */
export const useSignalEngineHeartbeatEvent = (
  onHeartbeat: (data: { timestamp: number; health: string; metrics?: any }) => void,
  enabled: boolean = true
): (() => void) => {
  return useSignalEngineEvent('heartbeat', onHeartbeat, enabled)
}

/**
 * Comprehensive Signal Engine events hook
 * Provides all Signal Engine events in a single hook
 */
export const useSignalEngineEvents = (
  handlers: {
    onSignalGenerated?: (signal: any) => void
    onStrategyCreated?: (strategy: any) => void
    onStrategyUpdated?: (data: { strategy: any; changes: any }) => void
    onStrategyDeleted?: (data: { strategyId: string }) => void
    onPerformanceUpdate?: (metrics: any) => void
    onConnectionStatusChange?: (status: { status: string; details?: string }) => void
    onError?: (error: { error: string; code?: string; severity: string; context?: any }) => void
    onHeartbeat?: (data: { timestamp: number; health: string; metrics?: any }) => void
  },
  enabled: boolean = true
): (() => void) => {
  const cleanupFunctions: (() => void)[] = []

  // Set up individual event listeners
  if (handlers.onSignalGenerated) {
    cleanupFunctions.push(useSignalGeneratedEvent(handlers.onSignalGenerated, enabled))
  }

  if (handlers.onStrategyCreated || handlers.onStrategyUpdated || handlers.onStrategyDeleted) {
    cleanupFunctions.push(
      useStrategyEvents(
        {
          onStrategyCreated: handlers.onStrategyCreated,
          onStrategyUpdated: handlers.onStrategyUpdated,
          onStrategyDeleted: handlers.onStrategyDeleted
        },
        enabled
      )
    )
  }

  if (handlers.onPerformanceUpdate) {
    cleanupFunctions.push(usePerformanceUpdateEvent(handlers.onPerformanceUpdate, enabled))
  }

  if (handlers.onConnectionStatusChange) {
    cleanupFunctions.push(
      useSignalEngineConnectionEvent(handlers.onConnectionStatusChange, enabled)
    )
  }

  if (handlers.onError) {
    cleanupFunctions.push(useSignalEngineErrorEvent(handlers.onError, enabled))
  }

  if (handlers.onHeartbeat) {
    cleanupFunctions.push(useSignalEngineHeartbeatEvent(handlers.onHeartbeat, enabled))
  }

  return useCallback(() => {
    cleanupFunctions.forEach((cleanup) => cleanup())
  }, [cleanupFunctions])
}
