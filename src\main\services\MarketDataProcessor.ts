/**
 * Market Data Processor Service
 * Singleton service that integrates with PocketOption broker to process market data
 * and generate trading signals using the SignalEngine
 */

import { logger } from '../../shared/utils/logger'
import { SignalEngine } from '../core/SignalEngine'
import { SignalEngineEvents } from '../events/SignalEngineEventEmitter'
import {
  transformStreamData,
  transformHistoryData,
  transformPeriodData,
  transformCandleData,
  marketDataToIndicatorData,
  ohlcDataToIndicatorData,
  validateMarketDataPoint,
  validateOHLCMarketData,
  getLatestDataPoint,
  sortDataByTimestamp
} from '../../shared/utils/marketDataTransformers'
import type {
  MarketDataProcessor as IMarketDataProcessor,
  MarketDataProcessorConfig,
  MarketDataProcessorStatus,
  MarketDataPoint,
  OHLCMarketData,
  SignalGenerationResult,
  GeneratedSignal
} from '../../shared/types/signals'
import type { IndicatorDataPoint } from '../../shared/types/indicators'

/**
 * Default configuration for MarketDataProcessor
 */
const DEFAULT_CONFIG: MarketDataProcessorConfig = {
  minConfidenceThreshold: 0.65, // 65% minimum confidence
  maxHistorySize: 1000,
  enableRealTimeSignals: true,
  watchedSymbols: [], // Empty means watch all symbols
  signalGenerationInterval: 1000 // 1 second
}

/**
 * MarketDataProcessor class implementing Singleton pattern
 * Processes market data from PocketOption broker and generates trading signals
 */
export class MarketDataProcessor implements IMarketDataProcessor {
  private static instance: MarketDataProcessor | null = null
  private config: MarketDataProcessorConfig = DEFAULT_CONFIG
  private signalEngine: SignalEngine | null = null
  private isInitialized: boolean = false
  private isProcessing: boolean = false
  private dataPointsProcessed: number = 0
  private signalsGenerated: number = 0
  private lastProcessingTime: number = 0
  private activeSymbols: Set<string> = new Set()
  private marketDataHistory: Map<string, MarketDataPoint[]> = new Map()
  private ohlcDataHistory: Map<string, OHLCMarketData[]> = new Map()
  private signalGenerationTimer: NodeJS.Timeout | null = null

  /**
   * Get singleton instance
   */
  public static getInstance(): MarketDataProcessor {
    if (!MarketDataProcessor.instance) {
      MarketDataProcessor.instance = new MarketDataProcessor()
    }
    return MarketDataProcessor.instance
  }

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    logger.info('MarketDataProcessor', 'MarketDataProcessor instance created')
  }

  /**
   * Initialize the processor with configuration
   */
  public async initialize(config: Partial<MarketDataProcessorConfig> = {}): Promise<void> {
    try {
      logger.info('MarketDataProcessor', 'Starting MarketDataProcessor initialization...')

      // Validate configuration
      if (
        config.minConfidenceThreshold !== undefined &&
        (config.minConfidenceThreshold < 0 || config.minConfidenceThreshold > 1)
      ) {
        throw new Error(
          `Invalid minConfidenceThreshold: ${config.minConfidenceThreshold}. Must be between 0 and 1.`
        )
      }

      this.config = { ...DEFAULT_CONFIG, ...config }
      logger.debug('MarketDataProcessor', 'Configuration merged successfully', this.config)

      // Initialize SignalEngine
      try {
        this.signalEngine = SignalEngine.getInstance()
        logger.debug('MarketDataProcessor', 'SignalEngine instance obtained')
      } catch (error) {
        throw new Error(
          `Failed to get SignalEngine instance: ${error instanceof Error ? error.message : String(error)}`
        )
      }

      // Initialize SignalEngine if not already initialized
      if (!this.signalEngine.isInitialized) {
        try {
          logger.debug('MarketDataProcessor', 'Initializing SignalEngine...')
          await this.signalEngine.initialize()
          logger.debug('MarketDataProcessor', 'SignalEngine initialized successfully')
        } catch (error) {
          throw new Error(
            `Failed to initialize SignalEngine: ${error instanceof Error ? error.message : String(error)}`
          )
        }
      } else {
        logger.debug('MarketDataProcessor', 'SignalEngine already initialized')
      }

      // Create default strategies for signal generation
      try {
        logger.debug('MarketDataProcessor', 'Creating default strategies...')
        await this.createDefaultStrategies()
        logger.debug('MarketDataProcessor', 'Default strategies created successfully')
      } catch (error) {
        throw new Error(
          `Failed to create default strategies: ${error instanceof Error ? error.message : String(error)}`
        )
      }

      // Start signal generation timer if real-time signals are enabled
      if (this.config.enableRealTimeSignals) {
        try {
          this.startSignalGenerationTimer()
          logger.debug('MarketDataProcessor', 'Signal generation timer started')
        } catch (error) {
          logger.warn('MarketDataProcessor', 'Failed to start signal generation timer:', error)
          // Don't throw here as this is not critical for initialization
        }
      }

      this.isInitialized = true
      logger.success('MarketDataProcessor', 'MarketDataProcessor initialized successfully', {
        config: this.config,
        strategiesCount: this.signalEngine.getActiveStrategies().length,
        enableRealTimeSignals: this.config.enableRealTimeSignals
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      const errorStack = error instanceof Error ? error.stack : undefined

      logger.error('MarketDataProcessor', 'Failed to initialize MarketDataProcessor:', {
        message: errorMessage,
        stack: errorStack,
        config: this.config
      })

      // Reset initialization state
      this.isInitialized = false
      this.signalEngine = null

      throw new Error(`MarketDataProcessor initialization failed: ${errorMessage}`)
    }
  }

  /**
   * Process streaming market data
   */
  public async processStreamData(data: StreamData): Promise<SignalGenerationResult[]> {
    if (!this.isInitialized) {
      logger.warn('MarketDataProcessor', 'Processor not initialized, skipping stream data')
      return []
    }

    const startTime = Date.now()
    const results: SignalGenerationResult[] = []

    try {
      this.isProcessing = true

      // Transform stream data
      const marketData = transformStreamData(data)
      if (!marketData || !validateMarketDataPoint(marketData)) {
        logger.warn('MarketDataProcessor', 'Invalid stream data received:', data)
        return []
      }

      // Check if we should process this symbol
      if (!this.shouldProcessSymbol(marketData.symbol)) {
        return []
      }

      // Add to active symbols and history
      this.activeSymbols.add(marketData.symbol)
      this.addToMarketDataHistory(marketData)

      // Convert to indicator data and process
      const indicatorData = marketDataToIndicatorData(marketData)
      const signals = this.signalEngine!.processMarketData(indicatorData)

      // Process generated signals
      for (const signal of signals) {
        if (signal.confidence >= this.config.minConfidenceThreshold) {
          const result = this.createSignalGenerationResult(signal, marketData, startTime)
          results.push(result)
          this.signalsGenerated++

          // Log signal with specified format
          this.logGeneratedSignal(signal, marketData)

          // Broadcast signal event
          SignalEngineEvents.signalGenerated(signal, {
            marketData,
            processingTime: Date.now() - startTime,
            source: 'stream'
          })
        }
      }

      this.dataPointsProcessed++
      this.lastProcessingTime = Date.now()
    } catch (error) {
      logger.error('MarketDataProcessor', 'Error processing stream data:', error)
    } finally {
      this.isProcessing = false
    }

    return results
  }

  /**
   * Process historical market data
   */
  public async processHistoryData(data: HistoryNewFastData): Promise<void> {
    if (!this.isInitialized) {
      logger.warn('MarketDataProcessor', 'Processor not initialized, skipping history data')
      return
    }

    try {
      this.isProcessing = true

      // Transform history data
      const marketDataPoints = transformHistoryData(data)
      if (marketDataPoints.length === 0) {
        logger.warn('MarketDataProcessor', 'No valid history data points received')
        return
      }

      const symbol = data.asset
      this.activeSymbols.add(symbol)

      // Sort by timestamp and add to history
      const sortedData = sortDataByTimestamp(marketDataPoints)
      for (const marketData of sortedData) {
        this.addToMarketDataHistory(marketData)

        // Convert to indicator data and feed to signal engine for historical analysis
        const indicatorData = marketDataToIndicatorData(marketData)
        this.signalEngine!.processMarketData(indicatorData)
      }

      // Process OHLC candles if available
      if (data.candles && data.candles.length > 0) {
        const ohlcData = transformCandleData(data.candles, symbol)
        for (const ohlc of ohlcData) {
          if (validateOHLCMarketData(ohlc)) {
            this.addToOHLCDataHistory(ohlc)
          }
        }
      }

      this.dataPointsProcessed += sortedData.length
      logger.info(
        'MarketDataProcessor',
        `Processed ${sortedData.length} historical data points for ${symbol}`
      )
    } catch (error) {
      logger.error('MarketDataProcessor', 'Error processing history data:', error)
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * Process period market data
   */
  public async processPeriodData(data: HistoryPeriodData): Promise<void> {
    if (!this.isInitialized) {
      logger.warn('MarketDataProcessor', 'Processor not initialized, skipping period data')
      return
    }

    try {
      this.isProcessing = true

      // Transform period data
      const ohlcDataPoints = transformPeriodData(data)
      if (ohlcDataPoints.length === 0) {
        logger.warn('MarketDataProcessor', 'No valid period data points received')
        return
      }

      const symbol = data.asset
      this.activeSymbols.add(symbol)

      // Sort by timestamp and add to history
      const sortedData = sortDataByTimestamp(ohlcDataPoints)
      for (const ohlcData of sortedData) {
        if (validateOHLCMarketData(ohlcData)) {
          this.addToOHLCDataHistory(ohlcData)

          // Convert to indicator data using close price and feed to signal engine
          const indicatorData = ohlcDataToIndicatorData(ohlcData, 'close')
          this.signalEngine!.processMarketData(indicatorData)
        }
      }

      this.dataPointsProcessed += sortedData.length
      logger.info(
        'MarketDataProcessor',
        `Processed ${sortedData.length} period data points for ${symbol}`
      )
    } catch (error) {
      logger.error('MarketDataProcessor', 'Error processing period data:', error)
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * Get current processor status
   */
  public getStatus(): MarketDataProcessorStatus {
    return {
      isInitialized: this.isInitialized,
      isProcessing: this.isProcessing,
      dataPointsProcessed: this.dataPointsProcessed,
      signalsGenerated: this.signalsGenerated,
      lastProcessingTime: this.lastProcessingTime,
      activeSymbols: Array.from(this.activeSymbols),
      memoryUsage: this.calculateMemoryUsage()
    }
  }

  /**
   * Cleanup and shutdown
   */
  public async shutdown(): Promise<void> {
    try {
      if (this.signalGenerationTimer) {
        clearInterval(this.signalGenerationTimer)
        this.signalGenerationTimer = null
      }

      this.isInitialized = false
      this.isProcessing = false
      this.marketDataHistory.clear()
      this.ohlcDataHistory.clear()
      this.activeSymbols.clear()

      logger.info('MarketDataProcessor', 'MarketDataProcessor shutdown completed')
    } catch (error) {
      logger.error('MarketDataProcessor', 'Error during shutdown:', error)
    }
  }

  /**
   * Create default profitable trading strategies
   */
  private async createDefaultStrategies(): Promise<void> {
    try {
      // RSI Oversold/Overbought Strategy
      const rsiStrategy = this.signalEngine!.createStrategy('rsi', {
        name: 'RSI_Reversal_Strategy',
        description: 'RSI-based reversal strategy for overbought/oversold conditions',
        period: 14,
        overboughtThreshold: 75,
        oversoldThreshold: 25,
        minConfidence: 0.7
      })

      // SMA Trend Following Strategy
      const smaStrategy = this.signalEngine!.createStrategy('sma', {
        name: 'SMA_Trend_Strategy',
        description: 'Simple Moving Average trend following strategy',
        period: 20,
        minConfidence: 0.6
      })

      // Bollinger Bands Mean Reversion Strategy
      const bbStrategy = this.signalEngine!.createStrategy('bollingerbands', {
        name: 'BB_Mean_Reversion_Strategy',
        description: 'Bollinger Bands mean reversion strategy',
        period: 20,
        standardDeviations: 2,
        minConfidence: 0.65
      })

      // Multi-indicator Combined Strategy
      const combinedStrategy = this.signalEngine!.createStrategy(['rsi', 'sma'], {
        name: 'RSI_SMA_Combined_Strategy',
        description: 'Combined RSI and SMA strategy for higher confidence signals',
        rsi: { period: 14, overboughtThreshold: 70, oversoldThreshold: 30 },
        sma: { period: 20 },
        combinationLogic: 'AND',
        minConfidence: 0.75
      })

      logger.success('MarketDataProcessor', 'Default trading strategies created successfully')
    } catch (error) {
      logger.error('MarketDataProcessor', 'Failed to create default strategies:', error)
      throw error
    }
  }

  /**
   * Start signal generation timer for periodic signal analysis
   */
  private startSignalGenerationTimer(): void {
    if (this.signalGenerationTimer) {
      clearInterval(this.signalGenerationTimer)
    }

    this.signalGenerationTimer = setInterval(() => {
      this.performPeriodicSignalGeneration()
    }, this.config.signalGenerationInterval)

    logger.debug(
      'MarketDataProcessor',
      `Signal generation timer started with ${this.config.signalGenerationInterval}ms interval`
    )
  }

  /**
   * Perform periodic signal generation for all active symbols
   */
  private performPeriodicSignalGeneration(): void {
    if (!this.isInitialized || this.isProcessing) {
      return
    }

    try {
      for (const symbol of this.activeSymbols) {
        const latestData = this.getLatestMarketData(symbol)
        if (latestData) {
          const indicatorData = marketDataToIndicatorData(latestData)
          const signals = this.signalEngine!.processMarketData(indicatorData)

          for (const signal of signals) {
            if (signal.confidence >= this.config.minConfidenceThreshold) {
              this.logGeneratedSignal(signal, latestData)
              this.signalsGenerated++
            }
          }
        }
      }
    } catch (error) {
      logger.error('MarketDataProcessor', 'Error in periodic signal generation:', error)
    }
  }

  /**
   * Check if symbol should be processed
   */
  private shouldProcessSymbol(symbol: string): boolean {
    // If no specific symbols are watched, process all
    if (this.config.watchedSymbols.length === 0) {
      return true
    }

    return this.config.watchedSymbols.includes(symbol)
  }

  /**
   * Add market data to history with memory management
   */
  private addToMarketDataHistory(data: MarketDataPoint): void {
    const symbol = data.symbol

    if (!this.marketDataHistory.has(symbol)) {
      this.marketDataHistory.set(symbol, [])
    }

    const history = this.marketDataHistory.get(symbol)!
    history.push(data)

    // Manage memory by keeping only recent data
    if (history.length > this.config.maxHistorySize) {
      history.splice(0, history.length - this.config.maxHistorySize)
    }
  }

  /**
   * Add OHLC data to history with memory management
   */
  private addToOHLCDataHistory(data: OHLCMarketData): void {
    const symbol = data.symbol

    if (!this.ohlcDataHistory.has(symbol)) {
      this.ohlcDataHistory.set(symbol, [])
    }

    const history = this.ohlcDataHistory.get(symbol)!
    history.push(data)

    // Manage memory by keeping only recent data
    if (history.length > this.config.maxHistorySize) {
      history.splice(0, history.length - this.config.maxHistorySize)
    }
  }

  /**
   * Get latest market data for a symbol
   */
  private getLatestMarketData(symbol: string): MarketDataPoint | null {
    const history = this.marketDataHistory.get(symbol)
    return history && history.length > 0 ? getLatestDataPoint(history) : null
  }

  /**
   * Create signal generation result
   */
  private createSignalGenerationResult(
    signal: GeneratedSignal,
    marketData: MarketDataPoint | OHLCMarketData,
    startTime: number
  ): SignalGenerationResult {
    return {
      signal,
      marketData,
      processingTime: Date.now() - startTime,
      confidence: signal.confidence,
      indicators: signal.indicators || [],
      reasoning: this.generateSignalReasoning(signal, marketData)
    }
  }

  /**
   * Generate reasoning for signal
   */
  private generateSignalReasoning(
    signal: GeneratedSignal,
    marketData: MarketDataPoint | OHLCMarketData
  ): string {
    const indicators = signal.indicators || []
    const indicatorNames = indicators.map((ind) => ind.indicator).join(', ')
    const price = 'price' in marketData ? marketData.price : marketData.close

    return (
      `${signal.signal} signal generated with ${(signal.confidence * 100).toFixed(1)}% confidence. ` +
      `Price: ${price.toFixed(4)}, Indicators: ${indicatorNames}, Strategy: ${signal.strategy}`
    )
  }

  /**
   * Log generated signal with specified format
   */
  private logGeneratedSignal(
    signal: GeneratedSignal,
    marketData: MarketDataPoint | OHLCMarketData
  ): void {
    const price = 'price' in marketData ? marketData.price : marketData.close
    const timestamp = new Date(marketData.timestamp).toISOString()

    logger.trade(
      `🎯 SIGNAL GENERATED: ${signal.signal} | ` +
        `Confidence: ${(signal.confidence * 100).toFixed(1)}% | ` +
        `Symbol: ${marketData.symbol} | ` +
        `Price: ${price.toFixed(4)} | ` +
        `Strategy: ${signal.strategy} | ` +
        `Timestamp: ${timestamp}`
    )
  }

  /**
   * Calculate memory usage
   */
  private calculateMemoryUsage(): number {
    let totalSize = 0

    for (const [, history] of this.marketDataHistory) {
      totalSize += history.length
    }

    for (const [, history] of this.ohlcDataHistory) {
      totalSize += history.length
    }

    return totalSize
  }
}
