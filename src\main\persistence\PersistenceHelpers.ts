/**
 * Persistence Helper Functions
 * Utility functions for file operations, compression, validation, and data management
 */

import { promises as fs } from 'fs'
import { join, dirname } from 'path'
import { createHash } from 'crypto'
import { gzip, gunzip } from 'zlib'
import { promisify } from 'util'
import { logger } from '../../shared/utils/logger'
import {
  PERSISTENCE_ERROR_MESSAGES,
  STORAGE_LIMITS,
  PERFORMANCE_SETTINGS
} from '../../shared/constants/persistence'
import type {
  PersistenceConfig,
  StorageStats,
  StoredSignal,
  StoredStrategy
} from '../../shared/types/persistence'

// Promisify compression functions
const gzipAsync = promisify(gzip)
const gunzipAsync = promisify(gunzip)

/**
 * Persistence helper class with utility methods
 */
export class PersistenceHelpers {
  /**
   * Validate persistence configuration
   */
  public static validateConfig(config: PersistenceConfig): void {
    if (!config.location || typeof config.location !== 'string') {
      throw new Error(PERSISTENCE_ERROR_MESSAGES.INVALID_CONFIG)
    }
    
    if (config.retention.maxAge < 1 || config.retention.maxAge > 3650) {
      throw new Error('Invalid retention max age: must be between 1 and 3650 days')
    }
    
    if (config.retention.maxRecords < 1000 || config.retention.maxRecords > STORAGE_LIMITS.maxTotalSize) {
      throw new Error('Invalid retention max records')
    }
    
    if (config.compression.enabled && !['gzip', 'lz4', 'zstd'].includes(config.compression.algorithm)) {
      throw new Error('Invalid compression algorithm')
    }
  }

  /**
   * Create storage directories
   */
  public static async createDirectories(config: PersistenceConfig): Promise<void> {
    const directories = [
      config.location,
      join(config.location, 'signals'),
      join(config.location, 'strategies'),
      join(config.location, 'indexes'),
      join(config.location, 'temp')
    ]
    
    if (config.backup?.enabled && config.backup.location) {
      directories.push(config.backup.location)
    }
    
    for (const dir of directories) {
      try {
        await fs.mkdir(dir, { recursive: true })
      } catch (error) {
        if ((error as any).code !== 'EEXIST') {
          throw new Error(`Failed to create directory ${dir}: ${error}`)
        }
      }
    }
  }

  /**
   * Generate unique ID
   */
  public static generateId(prefix: string): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 9)
    return `${prefix}_${timestamp}_${random}`
  }

  /**
   * Get file path for signal
   */
  public static getSignalFilePath(config: PersistenceConfig, id: string): string {
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    
    return join(config.location, 'signals', `${year}`, `${month}`, `${day}`, `${id}.json`)
  }

  /**
   * Get file path for strategy
   */
  public static getStrategyFilePath(config: PersistenceConfig, id: string): string {
    return join(config.location, 'strategies', `${id}.json`)
  }

  /**
   * Get index file path
   */
  public static getIndexFilePath(config: PersistenceConfig, type: 'signals' | 'strategies'): string {
    return join(config.location, 'indexes', `${type}.json`)
  }

  /**
   * Save data to file with compression
   */
  public static async saveToFile(filePath: string, data: any, config: PersistenceConfig): Promise<void> {
    try {
      // Ensure directory exists
      await fs.mkdir(dirname(filePath), { recursive: true })
      
      // Serialize data
      const jsonData = JSON.stringify(data, null, 2)
      let fileData = Buffer.from(jsonData, 'utf8')
      
      // Apply compression if enabled
      if (config.compression.enabled) {
        switch (config.compression.algorithm) {
          case 'gzip':
            fileData = await gzipAsync(fileData)
            break
          // Add other compression algorithms as needed
        }
      }
      
      // Write to file
      await fs.writeFile(filePath, fileData)
      
      logger.debug('PersistenceHelpers', `Saved file: ${filePath} (${fileData.length} bytes)`)
    } catch (error) {
      logger.error('PersistenceHelpers', `Failed to save file ${filePath}: ${error}`)
      throw new Error(PERSISTENCE_ERROR_MESSAGES.SAVE_FAILED)
    }
  }

  /**
   * Load data from file with decompression
   */
  public static async loadFromFile<T>(filePath: string, config: PersistenceConfig): Promise<T> {
    try {
      // Read file
      let fileData = await fs.readFile(filePath)
      
      // Apply decompression if enabled
      if (config.compression.enabled) {
        switch (config.compression.algorithm) {
          case 'gzip':
            fileData = await gunzipAsync(fileData)
            break
          // Add other compression algorithms as needed
        }
      }
      
      // Parse JSON
      const jsonData = fileData.toString('utf8')
      const data = JSON.parse(jsonData)
      
      logger.debug('PersistenceHelpers', `Loaded file: ${filePath} (${fileData.length} bytes)`)
      
      return data
    } catch (error) {
      logger.error('PersistenceHelpers', `Failed to load file ${filePath}: ${error}`)
      throw new Error(PERSISTENCE_ERROR_MESSAGES.LOAD_FAILED)
    }
  }

  /**
   * Check if file exists
   */
  public static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath)
      return true
    } catch {
      return false
    }
  }

  /**
   * Get file size
   */
  public static async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await fs.stat(filePath)
      return stats.size
    } catch {
      return 0
    }
  }

  /**
   * Calculate storage size
   */
  public static async calculateStorageSize(config: PersistenceConfig): Promise<number> {
    let totalSize = 0
    
    const directories = [
      join(config.location, 'signals'),
      join(config.location, 'strategies'),
      join(config.location, 'indexes')
    ]
    
    for (const dir of directories) {
      try {
        totalSize += await this.getDirectorySize(dir)
      } catch {
        // Directory might not exist
      }
    }
    
    return totalSize
  }

  /**
   * Get directory size recursively
   */
  public static async getDirectorySize(dirPath: string): Promise<number> {
    let totalSize = 0
    
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = join(dirPath, entry.name)
        
        if (entry.isDirectory()) {
          totalSize += await this.getDirectorySize(fullPath)
        } else {
          totalSize += await this.getFileSize(fullPath)
        }
      }
    } catch {
      // Directory might not exist or be accessible
    }
    
    return totalSize
  }

  /**
   * Create initial storage stats
   */
  public static createInitialStats(): StorageStats {
    return {
      totalSignals: 0,
      totalStrategies: 0,
      totalSize: 0,
      utilization: 0,
      lastCleanup: Date.now(),
      performance: {
        avgReadTime: 0,
        avgWriteTime: 0,
        cacheHitRate: 0
      }
    }
  }

  /**
   * Update performance stats
   */
  public static updateStats(
    stats: StorageStats,
    operation: 'read' | 'write',
    duration: number
  ): void {
    if (operation === 'read') {
      stats.performance.avgReadTime = (stats.performance.avgReadTime + duration) / 2
    } else {
      stats.performance.avgWriteTime = (stats.performance.avgWriteTime + duration) / 2
    }
  }

  /**
   * Clean up old files based on retention policy
   */
  public static async cleanupOldFiles(
    config: PersistenceConfig,
    signalIndex: Map<string, StoredSignal>,
    strategyIndex: Map<string, StoredStrategy>
  ): Promise<number> {
    let cleanedCount = 0
    const cutoffTime = Date.now() - (config.retention.maxAge * 24 * 60 * 60 * 1000)
    
    // Clean up old signals
    for (const [id, signal] of signalIndex) {
      if (signal.storedAt < cutoffTime) {
        try {
          const filePath = this.getSignalFilePath(config, id)
          if (await this.fileExists(filePath)) {
            if (config.retention.archiveOldData) {
              // Move to archive instead of deleting
              const archivePath = join(config.location, 'archive', 'signals', `${id}.json`)
              await fs.mkdir(dirname(archivePath), { recursive: true })
              await fs.rename(filePath, archivePath)
            } else {
              await fs.unlink(filePath)
            }
            signalIndex.delete(id)
            cleanedCount++
          }
        } catch (error) {
          logger.warn('PersistenceHelpers', `Failed to cleanup signal ${id}: ${error}`)
        }
      }
    }
    
    // Clean up old strategies (if they have no recent signals)
    for (const [id, strategy] of strategyIndex) {
      if (strategy.metadata.modifiedAt < cutoffTime) {
        // Check if strategy has recent signals
        const hasRecentSignals = Array.from(signalIndex.values()).some(
          signal => signal.strategy === strategy.config.name && signal.storedAt >= cutoffTime
        )
        
        if (!hasRecentSignals) {
          try {
            const filePath = this.getStrategyFilePath(config, id)
            if (await this.fileExists(filePath)) {
              if (config.retention.archiveOldData) {
                const archivePath = join(config.location, 'archive', 'strategies', `${id}.json`)
                await fs.mkdir(dirname(archivePath), { recursive: true })
                await fs.rename(filePath, archivePath)
              } else {
                await fs.unlink(filePath)
              }
              strategyIndex.delete(id)
              cleanedCount++
            }
          } catch (error) {
            logger.warn('PersistenceHelpers', `Failed to cleanup strategy ${id}: ${error}`)
          }
        }
      }
    }
    
    logger.info('PersistenceHelpers', `Cleanup completed: ${cleanedCount} files processed`)
    return cleanedCount
  }

  /**
   * Create backup of data
   */
  public static async createBackup(config: PersistenceConfig): Promise<string> {
    if (!config.backup?.enabled || !config.backup.location) {
      throw new Error('Backup not configured')
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const backupName = `backup_${timestamp}`
    const backupPath = join(config.backup.location, backupName)
    
    try {
      // Create backup directory
      await fs.mkdir(backupPath, { recursive: true })
      
      // Copy data directories
      const sourceDirs = ['signals', 'strategies', 'indexes']
      
      for (const dir of sourceDirs) {
        const sourcePath = join(config.location, dir)
        const targetPath = join(backupPath, dir)
        
        if (await this.fileExists(sourcePath)) {
          await this.copyDirectory(sourcePath, targetPath)
        }
      }
      
      // Create backup metadata
      const metadata = {
        timestamp: Date.now(),
        version: '1.0.0',
        config: config,
        size: await this.getDirectorySize(backupPath)
      }
      
      await fs.writeFile(
        join(backupPath, 'metadata.json'),
        JSON.stringify(metadata, null, 2)
      )
      
      logger.info('PersistenceHelpers', `Backup created: ${backupPath}`)
      return backupPath
    } catch (error) {
      logger.error('PersistenceHelpers', `Backup failed: ${error}`)
      throw new Error(PERSISTENCE_ERROR_MESSAGES.BACKUP_FAILED)
    }
  }

  /**
   * Copy directory recursively
   */
  private static async copyDirectory(source: string, target: string): Promise<void> {
    await fs.mkdir(target, { recursive: true })
    
    const entries = await fs.readdir(source, { withFileTypes: true })
    
    for (const entry of entries) {
      const sourcePath = join(source, entry.name)
      const targetPath = join(target, entry.name)
      
      if (entry.isDirectory()) {
        await this.copyDirectory(sourcePath, targetPath)
      } else {
        await fs.copyFile(sourcePath, targetPath)
      }
    }
  }
}

/**
 * Export helper class
 */
export default PersistenceHelpers
