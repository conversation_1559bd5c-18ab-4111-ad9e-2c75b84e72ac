/**
 * Centralized constants for the Signal Engine system
 * This file provides a single point of import for all constants
 */

// Signal Engine Constants
export const SIGNAL_ENGINE_CONSTANTS = {
  // Signal types
  SIGNAL_TYPES: {
    BUY: 'BUY',
    SELL: 'SELL',
    HOLD: 'HOLD'
  } as const,

  // Confidence levels
  CONFIDENCE_LEVELS: {
    VERY_LOW: 0.2,
    LOW: 0.4,
    MEDIUM: 0.6,
    HIGH: 0.8,
    VERY_HIGH: 0.9
  } as const,

  // Strategy types
  STRATEGY_TYPES: {
    SINGLE_INDICATOR: 'single',
    MULTI_INDICATOR: 'multi'
  } as const,

  // Combination logic
  COMBINATION_LOGIC: {
    AND: 'AND',
    OR: 'OR',
    WEIGHTED: 'WEIGHTED'
  } as const,

  // Performance thresholds
  PERFORMANCE_THRESHOLDS: {
    PROCESSING_TIME_WARNING: 100, // ms
    PROCESSING_TIME_ERROR: 500, // ms
    MEMORY_USAGE_WARNING: 0.8, // 80%
    MEMORY_USAGE_ERROR: 0.95 // 95%
  } as const,

  // Default configurations
  DEFAULTS: {
    REFRESH_INTERVAL: 5000, // ms
    MAX_SIGNALS_HISTORY: 1000,
    MAX_STRATEGIES: 10,
    SIGNAL_COOLDOWN: 5000, // ms
    AUTO_SAVE_INTERVAL: 30 * 60 * 1000 // 30 minutes
  } as const
} as const

// Indicator Constants
export const INDICATOR_CONSTANTS = {
  // Indicator types
  TYPES: {
    RSI: 'rsi',
    SMA: 'sma',
    EMA: 'ema',
    BOLLINGER_BANDS: 'bollingerbands',
    MACD: 'macd',
    STOCHASTIC: 'stochastic'
  } as const,

  // RSI specific
  RSI: {
    DEFAULT_PERIOD: 14,
    MIN_PERIOD: 2,
    MAX_PERIOD: 100,
    OVERBOUGHT_THRESHOLD: 70,
    OVERSOLD_THRESHOLD: 30,
    MIN_THRESHOLD: 0,
    MAX_THRESHOLD: 100
  } as const,

  // SMA specific
  SMA: {
    DEFAULT_PERIOD: 20,
    MIN_PERIOD: 2,
    MAX_PERIOD: 200
  } as const,

  // Bollinger Bands specific
  BOLLINGER_BANDS: {
    DEFAULT_PERIOD: 20,
    DEFAULT_STD_DEV: 2,
    MIN_PERIOD: 2,
    MAX_PERIOD: 100,
    MIN_STD_DEV: 0.5,
    MAX_STD_DEV: 5
  } as const,

  // General limits
  LIMITS: {
    MIN_DATA_POINTS: 2,
    MAX_DATA_POINTS: 10000,
    CALCULATION_TIMEOUT: 5000 // ms
  } as const
} as const

// UI Constants
export const UI_CONSTANTS = {
  // Colors (following project guidelines - avoid blue, use green)
  COLORS: {
    PRIMARY: '#10B981', // green-500
    PRIMARY_LIGHT: '#D1FAE5', // green-100
    PRIMARY_DARK: '#047857', // green-700
    SUCCESS: '#10B981', // green-500
    WARNING: '#F59E0B', // yellow-500
    ERROR: '#EF4444', // red-500
    INFO: '#6B7280', // gray-500
    NEUTRAL: '#9CA3AF', // gray-400

    // Signal colors
    BUY_COLOR: '#10B981', // green-500
    SELL_COLOR: '#EF4444', // red-500
    HOLD_COLOR: '#F59E0B', // yellow-500

    // Confidence colors
    HIGH_CONFIDENCE: '#10B981', // green-500
    MEDIUM_CONFIDENCE: '#F59E0B', // yellow-500
    LOW_CONFIDENCE: '#EF4444' // red-500
  } as const,

  // Emojis for visual enhancement
  EMOJIS: {
    ANALYTICS: '📊',
    SIGNALS: '🎯',
    SETTINGS: '⚙️',
    PERFORMANCE: '📈',
    STRATEGY: '🧠',
    DASHBOARD: '🏠',
    BUILDER: '🔧',
    MANAGEMENT: '📋',
    TRENDS: '📊',
    DISTRIBUTION: '🥧',
    OVERVIEW: '📈',

    // Signal emojis
    BUY: '📈',
    SELL: '📉',
    HOLD: '⏸️',

    // Status emojis
    CONNECTED: '🟢',
    DISCONNECTED: '🔴',
    RECONNECTING: '🟡',
    LOADING: '⏳',
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️',
    INFO: 'ℹ️'
  } as const,

  // Layout constants
  LAYOUT: {
    SIDEBAR_WIDTH: 256,
    SIDEBAR_COLLAPSED_WIDTH: 64,
    HEADER_HEIGHT: 64,
    FOOTER_HEIGHT: 48,
    CONTENT_PADDING: 24,
    CARD_BORDER_RADIUS: 8,
    BUTTON_BORDER_RADIUS: 6
  } as const,

  // Animation durations (ms)
  ANIMATIONS: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
    VERY_SLOW: 1000
  } as const,

  // Breakpoints (px)
  BREAKPOINTS: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    XXL: 1536
  } as const
} as const

// WebSocket Constants
export const WEBSOCKET_CONSTANTS = {
  // Event types
  EVENTS: {
    SIGNAL_GENERATED: 'signal-generated',
    SIGNAL_BATCH_UPDATE: 'signal-batch-update',
    SIGNAL_STATISTICS: 'signal-statistics',
    STRATEGY_CREATED: 'strategy-created',
    STRATEGY_UPDATED: 'strategy-updated',
    STRATEGY_DELETED: 'strategy-deleted',
    STRATEGY_PERFORMANCE: 'strategy-performance',
    MARKET_DATA_UPDATE: 'market-data-update',
    INDICATOR_UPDATE: 'indicator-update',
    BACKTEST_STARTED: 'backtest-started',
    BACKTEST_PROGRESS: 'backtest-progress',
    BACKTEST_COMPLETED: 'backtest-completed',
    PERFORMANCE_UPDATE: 'performance-update',
    CONNECTION_STATUS: 'connection-status',
    ERROR: 'error',
    HEARTBEAT: 'heartbeat'
  } as const,

  // Connection states
  CONNECTION_STATES: {
    CONNECTING: 'connecting',
    CONNECTED: 'connected',
    DISCONNECTED: 'disconnected',
    RECONNECTING: 'reconnecting',
    ERROR: 'error'
  } as const,

  // Timeouts and intervals
  TIMEOUTS: {
    CONNECTION_TIMEOUT: 10000, // ms
    HEARTBEAT_INTERVAL: 30000, // ms
    HEARTBEAT_TIMEOUT: 10000, // ms
    RECONNECT_DELAY: 5000, // ms
    MAX_RECONNECT_ATTEMPTS: 5
  } as const
} as const

// Backtesting Constants
export const BACKTESTING_CONSTANTS = {
  // Default configuration
  DEFAULTS: {
    INITIAL_CAPITAL: 10000,
    COMMISSION: 0.001, // 0.1%
    SLIPPAGE: 0.0005, // 0.05%
    MAX_POSITION_SIZE: 0.1, // 10%
    STOP_LOSS: 0.05, // 5%
    TAKE_PROFIT: 0.1 // 10%
  } as const,

  // Limits
  LIMITS: {
    MIN_CAPITAL: 1000,
    MAX_CAPITAL: 1000000,
    MIN_DATA_POINTS: 100,
    MAX_DATA_POINTS: 100000,
    MAX_DURATION_DAYS: 365
  } as const,

  // Metrics thresholds
  METRICS: {
    GOOD_SHARPE_RATIO: 1.0,
    EXCELLENT_SHARPE_RATIO: 2.0,
    MAX_ACCEPTABLE_DRAWDOWN: 0.2, // 20%
    MIN_WIN_RATE: 0.4 // 40%
  } as const
} as const

// Persistence Constants
export const PERSISTENCE_CONSTANTS = {
  // File extensions
  FILE_EXTENSIONS: {
    JSON: '.json',
    CSV: '.csv',
    XLSX: '.xlsx'
  } as const,

  // Storage limits
  LIMITS: {
    MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
    MAX_SIGNALS_PER_FILE: 10000,
    MAX_QUERY_RESULTS: 1000,
    CACHE_TTL: 5 * 60 * 1000 // 5 minutes
  } as const,

  // Default paths
  PATHS: {
    SIGNALS: 'signals',
    STRATEGIES: 'strategies',
    SNAPSHOTS: 'snapshots',
    EXPORTS: 'exports',
    BACKUPS: 'backups'
  } as const
} as const

// Error Constants
export const ERROR_CONSTANTS = {
  // Error codes
  CODES: {
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    NETWORK_ERROR: 'NETWORK_ERROR',
    TIMEOUT_ERROR: 'TIMEOUT_ERROR',
    PERMISSION_ERROR: 'PERMISSION_ERROR',
    NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
    INTERNAL_ERROR: 'INTERNAL_ERROR',
    CONFIGURATION_ERROR: 'CONFIGURATION_ERROR'
  } as const,

  // Severity levels
  SEVERITY: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical'
  } as const,

  // Default messages
  MESSAGES: {
    GENERIC_ERROR: 'An unexpected error occurred',
    NETWORK_ERROR: 'Network connection failed',
    TIMEOUT_ERROR: 'Operation timed out',
    VALIDATION_ERROR: 'Invalid input provided',
    PERMISSION_ERROR: 'Insufficient permissions',
    NOT_FOUND_ERROR: 'Resource not found'
  } as const
} as const

// Export all constants as a single object for convenience
export const ALL_CONSTANTS = {
  SIGNAL_ENGINE: SIGNAL_ENGINE_CONSTANTS,
  INDICATORS: INDICATOR_CONSTANTS,
  UI: UI_CONSTANTS,
  WEBSOCKET: WEBSOCKET_CONSTANTS,
  BACKTESTING: BACKTESTING_CONSTANTS,
  PERSISTENCE: PERSISTENCE_CONSTANTS,
  ERRORS: ERROR_CONSTANTS
} as const

// Type definitions for constants
export type SignalType =
  (typeof SIGNAL_ENGINE_CONSTANTS.SIGNAL_TYPES)[keyof typeof SIGNAL_ENGINE_CONSTANTS.SIGNAL_TYPES]
export type StrategyType =
  (typeof SIGNAL_ENGINE_CONSTANTS.STRATEGY_TYPES)[keyof typeof SIGNAL_ENGINE_CONSTANTS.STRATEGY_TYPES]
export type CombinationLogic =
  (typeof SIGNAL_ENGINE_CONSTANTS.COMBINATION_LOGIC)[keyof typeof SIGNAL_ENGINE_CONSTANTS.COMBINATION_LOGIC]
export type IndicatorType =
  (typeof INDICATOR_CONSTANTS.TYPES)[keyof typeof INDICATOR_CONSTANTS.TYPES]
export type ConnectionState =
  (typeof WEBSOCKET_CONSTANTS.CONNECTION_STATES)[keyof typeof WEBSOCKET_CONSTANTS.CONNECTION_STATES]
export type ErrorCode = (typeof ERROR_CONSTANTS.CODES)[keyof typeof ERROR_CONSTANTS.CODES]
export type ErrorSeverity = (typeof ERROR_CONSTANTS.SEVERITY)[keyof typeof ERROR_CONSTANTS.SEVERITY]
