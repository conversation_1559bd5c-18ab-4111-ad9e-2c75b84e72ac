/**
 * Accessibility Utilities for Trading Bot UI
 * Provides utilities for enhanced accessibility features
 */

/**
 * Generates unique IDs for form elements
 */
export const generateId = (prefix: string, suffix?: string): string => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  const parts = [prefix, timestamp, random]
  if (suffix) {
    parts.push(suffix)
  }
  return parts.join('-')
}

/**
 * Creates accessible label-input relationships
 */
export const createLabelInputRelationship = (
  baseId: string
): { inputId: string; labelId: string; errorId: string; helpId: string } => {
  return {
    inputId: `${baseId}-input`,
    labelId: `${baseId}-label`,
    errorId: `${baseId}-error`,
    helpId: `${baseId}-help`
  }
}

/**
 * Announces messages to screen readers
 */
export const announceToScreenReader = (
  message: string,
  priority: 'polite' | 'assertive' = 'polite'
): void => {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', priority)
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message

  document.body.appendChild(announcement)

  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

/**
 * Manages focus for keyboard navigation
 */
export class FocusManager {
  private focusableElements: HTMLElement[] = []
  private currentIndex = 0

  constructor(container: HTMLElement) {
    this.updateFocusableElements(container)
  }

  /**
   * Updates the list of focusable elements
   */
  updateFocusableElements(container: HTMLElement): void {
    const focusableSelectors = [
      'input:not([disabled])',
      'button:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      'a[href]'
    ].join(', ')

    this.focusableElements = Array.from(
      container.querySelectorAll(focusableSelectors)
    ) as HTMLElement[]
  }

  /**
   * Moves focus to the next element
   */
  focusNext(): void {
    if (this.focusableElements.length === 0) return

    this.currentIndex = (this.currentIndex + 1) % this.focusableElements.length
    this.focusableElements[this.currentIndex]?.focus()
  }

  /**
   * Moves focus to the previous element
   */
  focusPrevious(): void {
    if (this.focusableElements.length === 0) return

    this.currentIndex =
      (this.currentIndex - 1 + this.focusableElements.length) % this.focusableElements.length
    this.focusableElements[this.currentIndex]?.focus()
  }

  /**
   * Moves focus to the first element
   */
  focusFirst(): void {
    if (this.focusableElements.length === 0) return

    this.currentIndex = 0
    this.focusableElements[this.currentIndex]?.focus()
  }

  /**
   * Moves focus to the last element
   */
  focusLast(): void {
    if (this.focusableElements.length === 0) return

    this.currentIndex = this.focusableElements.length - 1
    this.focusableElements[this.currentIndex]?.focus()
  }
}

/**
 * Keyboard navigation handler for form elements
 */
export const handleFormKeyNavigation = (event: KeyboardEvent, container: HTMLElement): boolean => {
  const focusManager = new FocusManager(container)

  switch (event.key) {
    case 'Tab':
      // Let default tab behavior handle this
      return false
    case 'ArrowDown':
    case 'ArrowRight':
      event.preventDefault()
      focusManager.focusNext()
      return true
    case 'ArrowUp':
    case 'ArrowLeft':
      event.preventDefault()
      focusManager.focusPrevious()
      return true
    case 'Home':
      event.preventDefault()
      focusManager.focusFirst()
      return true
    case 'End':
      event.preventDefault()
      focusManager.focusLast()
      return true
    default:
      return false
  }
}

/**
 * Creates ARIA attributes for form validation
 */
export const createValidationAttributes = (
  hasError: boolean,
  errorId?: string,
  helpId?: string
): Record<string, string | boolean> => {
  const attributes: Record<string, string | boolean> = {
    'aria-invalid': hasError
  }

  if (hasError && errorId) {
    attributes['aria-describedby'] = errorId
  } else if (helpId) {
    attributes['aria-describedby'] = helpId
  }

  return attributes
}

/**
 * Creates ARIA attributes for required fields
 */
export const createRequiredFieldAttributes = (
  required: boolean
): Record<string, boolean> | Record<string, never> => {
  return required ? { 'aria-required': true } : {}
}

/**
 * Creates ARIA attributes for numeric inputs
 */
export const createNumericInputAttributes = (
  min?: number,
  max?: number,
  step?: number
): Record<string, string | number> => {
  const attributes: Record<string, string | number> = {}

  if (min !== undefined) {
    attributes['aria-valuemin'] = min
  }
  if (max !== undefined) {
    attributes['aria-valuemax'] = max
  }
  if (step !== undefined) {
    attributes['aria-step'] = step
  }

  return attributes
}

/**
 * Creates ARIA attributes for button states
 */
export const createButtonStateAttributes = (
  pressed?: boolean,
  expanded?: boolean,
  disabled?: boolean
): Record<string, boolean> => {
  const attributes: Record<string, boolean> = {}

  if (pressed !== undefined) {
    attributes['aria-pressed'] = pressed
  }
  if (expanded !== undefined) {
    attributes['aria-expanded'] = expanded
  }
  if (disabled !== undefined) {
    attributes['aria-disabled'] = disabled
  }

  return attributes
}

/**
 * Provides screen reader friendly descriptions for trading bot states
 */
export const getTradingBotStateDescription = (
  state: 'stopped' | 'running',
  isValid: boolean
): string => {
  if (state === 'running') {
    return 'Trading bot is currently active and running trades'
  }

  if (!isValid) {
    return 'Trading bot is stopped. Please complete the form to start trading'
  }

  return 'Trading bot is ready to start. All form fields are valid'
}

/**
 * Provides screen reader friendly descriptions for form validation
 */
export const getValidationDescription = (
  fieldName: string,
  hasError: boolean,
  errorMessage?: string
): string => {
  if (hasError && errorMessage) {
    return `${fieldName} has an error: ${errorMessage}`
  }

  return `${fieldName} is valid`
}
