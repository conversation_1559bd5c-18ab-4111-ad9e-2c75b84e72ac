/**
 * Base Indicator Class
 * Abstract base class providing common functionality for all technical indicators
 */

import { logger } from '../../shared/utils/logger'
import {
  INDICATOR_CONSTANTS,
  INDICATOR_LOG_CATEGORIES,
  INDICATOR_ERROR_MESSAGES
} from '../../shared/constants/indicators'
import type {
  BaseIndicator,
  BaseIndicatorConfig,
  IndicatorDataPoint,
  IndicatorOutputPoint,
  IndicatorResult,
  IndicatorMetadata,
  IndicatorValidationResult,
  IndicatorEvent,
  IndicatorEventType,
  IndicatorEventCallback
  // IndicatorPerformanceMetrics
} from '../../shared/types/indicators'

/**
 * Abstract base class for all technical indicators
 * Provides common functionality including validation, error handling, logging, and event management
 */
export abstract class BaseIndicatorClass implements BaseIndicator {
  protected readonly _name: string
  protected _config: BaseIndicatorConfig
  protected _dataPoints: IndicatorDataPoint[] = []
  protected _outputValues: IndicatorOutputPoint[] = []
  protected _eventCallbacks: Map<IndicatorEventType, IndicatorEventCallback[]> = new Map()
  protected _isReady: boolean = false
  protected _lastCalculationTime: number = 0
  protected _operationCount: number = 0

  /**
   * Constructor for base indicator
   * @param name - Name of the indicator
   * @param config - Configuration for the indicator
   */
  constructor(name: string, config: BaseIndicatorConfig) {
    this._name = name
    this._config = { ...INDICATOR_CONSTANTS.DEFAULT_CONFIG, ...config }
    this._validateConfig()
    this._initializeEventCallbacks()

    logger.debug(INDICATOR_LOG_CATEGORIES.CALCULATION, `${this._name} indicator initialized`, {
      config: this._config
    })
  }

  // Getters
  public get name(): string {
    return this._name
  }

  public get config(): BaseIndicatorConfig {
    return { ...this._config }
  }

  public get isReady(): boolean {
    return this._isReady
  }

  public get dataCount(): number {
    return this._dataPoints.length
  }

  public get latestValue(): IndicatorOutputPoint | null {
    return this._outputValues.length > 0 ? this._outputValues[this._outputValues.length - 1] : null
  }

  /**
   * Add a single data point and calculate indicator value
   * @param dataPoint - The data point to add
   * @returns The calculated indicator value or null if not enough data
   */
  public addData(dataPoint: IndicatorDataPoint): IndicatorOutputPoint | null {
    const startTime = performance.now()

    try {
      // Validate input
      const validation = this.validateInput([dataPoint])
      if (!validation.isValid) {
        const error = `Invalid data point: ${validation.errors.join(', ')}`
        this._emitEvent('error-occurred', { error, dataPoint })
        throw new Error(error)
      }

      // Add timestamp if not provided
      const processedDataPoint: IndicatorDataPoint = {
        ...dataPoint,
        timestamp: dataPoint.timestamp || Date.now()
      }

      // Add to data points
      this._dataPoints.push(processedDataPoint)
      this._operationCount++

      // Manage memory
      this._manageMemory()

      // Calculate new value if we have enough data
      let outputValue: IndicatorOutputPoint | null = null
      if (this._dataPoints.length >= this._config.period) {
        outputValue = this._calculateSingleValue(this._dataPoints.length - 1)
        if (outputValue) {
          this._outputValues.push(outputValue)
          this._isReady = true
          this._emitEvent('value-calculated', { outputValue, dataPoint: processedDataPoint })
        }
      }

      this._emitEvent('data-added', { dataPoint: processedDataPoint, outputValue })

      // Log performance
      const calculationTime = performance.now() - startTime
      this._lastCalculationTime = calculationTime

      if (calculationTime > INDICATOR_CONSTANTS.PERFORMANCE_CONFIG.MAX_CALCULATION_TIME) {
        logger.warn(
          INDICATOR_LOG_CATEGORIES.PERFORMANCE,
          `${this._name} calculation time exceeded threshold`,
          {
            calculationTime,
            threshold: INDICATOR_CONSTANTS.PERFORMANCE_CONFIG.MAX_CALCULATION_TIME
          }
        )
      }

      return outputValue
    } catch (error) {
      logger.error(INDICATOR_LOG_CATEGORIES.ERROR, `${this._name} addData failed`, error)
      this._emitEvent('error-occurred', {
        error: error instanceof Error ? error.message : String(error)
      })
      return null
    }
  }

  /**
   * Add multiple data points in batch
   * @param dataPoints - Array of data points to add
   * @returns Array of calculated indicator values
   */
  public addDataBatch(dataPoints: IndicatorDataPoint[]): IndicatorOutputPoint[] {
    const startTime = performance.now()
    const results: IndicatorOutputPoint[] = []

    try {
      // Validate all inputs first
      const validation = this.validateInput(dataPoints)
      if (!validation.isValid) {
        const error = `Invalid data points: ${validation.errors.join(', ')}`
        this._emitEvent('error-occurred', { error, dataPoints })
        throw new Error(error)
      }

      // Process in batches for performance
      const batchSize = INDICATOR_CONSTANTS.PERFORMANCE_CONFIG.BATCH_SIZE
      for (let i = 0; i < dataPoints.length; i += batchSize) {
        const batch = dataPoints.slice(i, i + batchSize)
        for (const dataPoint of batch) {
          const result = this.addData(dataPoint)
          if (result) {
            results.push(result)
          }
        }
      }

      const calculationTime = performance.now() - startTime
      logger.debug(
        INDICATOR_LOG_CATEGORIES.PERFORMANCE,
        `${this._name} batch processing completed`,
        {
          dataPointsCount: dataPoints.length,
          resultsCount: results.length,
          calculationTime
        }
      )

      return results
    } catch (error) {
      logger.error(INDICATOR_LOG_CATEGORIES.ERROR, `${this._name} addDataBatch failed`, error)
      this._emitEvent('error-occurred', {
        error: error instanceof Error ? error.message : String(error)
      })
      return results
    }
  }

  /**
   * Calculate indicator for all current data
   * @returns Complete indicator result
   */
  public calculate(): IndicatorResult {
    const startTime = performance.now()

    try {
      this._outputValues = []

      // Calculate values for all data points that have enough history
      for (let i = this._config.period - 1; i < this._dataPoints.length; i++) {
        const outputValue = this._calculateSingleValue(i)
        if (outputValue) {
          this._outputValues.push(outputValue)
        }
      }

      this._isReady = this._outputValues.length > 0

      const calculationTime = performance.now() - startTime
      const metadata: IndicatorMetadata = {
        name: this._name,
        config: this._config,
        inputCount: this._dataPoints.length,
        outputCount: this._outputValues.length,
        calculatedAt: Date.now(),
        performance: {
          calculationTime,
          operationCount: this._operationCount
        }
      }

      logger.debug(
        INDICATOR_LOG_CATEGORIES.CALCULATION,
        `${this._name} full calculation completed`,
        metadata
      )

      return {
        values: [...this._outputValues],
        metadata,
        success: true
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error(INDICATOR_LOG_CATEGORIES.ERROR, `${this._name} calculate failed`, error)

      return {
        values: [],
        metadata: {
          name: this._name,
          config: this._config,
          inputCount: this._dataPoints.length,
          outputCount: 0,
          calculatedAt: Date.now()
        },
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Reset the indicator to initial state
   */
  public reset(): void {
    this._dataPoints = []
    this._outputValues = []
    this._isReady = false
    this._operationCount = 0
    this._lastCalculationTime = 0

    this._emitEvent('reset', { timestamp: Date.now() })
    logger.debug(INDICATOR_LOG_CATEGORIES.CALCULATION, `${this._name} indicator reset`)
  }

  /**
   * Update indicator configuration
   * @param newConfig - New configuration to apply
   */
  public updateConfig(newConfig: Partial<BaseIndicatorConfig>): void {
    const oldConfig = { ...this._config }
    this._config = { ...this._config, ...newConfig }

    try {
      this._validateConfig()

      // If period changed, we may need to recalculate
      if (oldConfig.period !== this._config.period) {
        this.calculate()
      }

      this._emitEvent('config-changed', { oldConfig, newConfig: this._config })
      logger.debug(INDICATOR_LOG_CATEGORIES.CALCULATION, `${this._name} configuration updated`, {
        oldConfig,
        newConfig: this._config
      })
    } catch (error) {
      // Revert config on validation failure
      this._config = oldConfig
      logger.error(INDICATOR_LOG_CATEGORIES.ERROR, `${this._name} config update failed`, error)
      throw error
    }
  }

  /**
   * Get all calculated values
   * @returns Array of all indicator values
   */
  public getValues(): IndicatorOutputPoint[] {
    return [...this._outputValues]
  }

  /**
   * Get the last N calculated values
   * @param count - Number of values to retrieve
   * @returns Array of the last N indicator values
   */
  public getLastValues(count: number): IndicatorOutputPoint[] {
    const startIndex = Math.max(0, this._outputValues.length - count)
    return this._outputValues.slice(startIndex)
  }

  /**
   * Validate input data
   * @param dataPoints - Data points to validate
   * @returns Validation result
   */
  public validateInput(dataPoints: IndicatorDataPoint[]): IndicatorValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (!this._config.validateInputs) {
      return { isValid: true, errors, warnings }
    }

    // Check if data points exist
    if (!dataPoints || dataPoints.length === 0) {
      errors.push(INDICATOR_ERROR_MESSAGES.data.empty)
      return { isValid: false, errors, warnings }
    }

    // Validate each data point
    for (let i = 0; i < dataPoints.length; i++) {
      const point = dataPoints[i]

      if (typeof point.value !== 'number' || !isFinite(point.value)) {
        errors.push(`${INDICATOR_ERROR_MESSAGES.data.invalid} at index ${i}`)
      }

      if (point.timestamp && (typeof point.timestamp !== 'number' || point.timestamp < 0)) {
        errors.push(`${INDICATOR_ERROR_MESSAGES.data.invalidTimestamp} at index ${i}`)
      }

      if (INDICATOR_CONSTANTS.VALIDATION_SETTINGS.CHECK_NUMERIC_VALIDITY) {
        if (
          point.value > INDICATOR_CONSTANTS.VALIDATION_SETTINGS.MAX_DATA_VALUE ||
          point.value < INDICATOR_CONSTANTS.VALIDATION_SETTINGS.MIN_DATA_VALUE
        ) {
          warnings.push(`Data value out of recommended range at index ${i}`)
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // Event management methods
  public on(eventType: IndicatorEventType, callback: IndicatorEventCallback): void {
    if (!this._eventCallbacks.has(eventType)) {
      this._eventCallbacks.set(eventType, [])
    }
    this._eventCallbacks.get(eventType)!.push(callback)
  }

  public off(eventType: IndicatorEventType, callback: IndicatorEventCallback): void {
    const callbacks = this._eventCallbacks.get(eventType)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  public emit(event: IndicatorEvent): void {
    const callbacks = this._eventCallbacks.get(event.type)
    if (callbacks) {
      callbacks.forEach((callback) => {
        try {
          callback(event)
        } catch (error) {
          logger.error(INDICATOR_LOG_CATEGORIES.ERROR, `${this._name} event callback failed`, {
            event,
            error
          })
        }
      })
    }
  }

  // Protected abstract methods that subclasses must implement
  protected abstract _calculateSingleValue(index: number): IndicatorOutputPoint | null

  // Protected helper methods
  protected _validateConfig(): void {
    if (
      !this._config.period ||
      this._config.period < INDICATOR_CONSTANTS.VALIDATION_CONSTRAINTS.period.min
    ) {
      throw new Error(INDICATOR_ERROR_MESSAGES.period.tooLow)
    }

    if (this._config.period > INDICATOR_CONSTANTS.VALIDATION_CONSTRAINTS.period.max) {
      throw new Error(INDICATOR_ERROR_MESSAGES.period.tooHigh)
    }
  }

  protected _initializeEventCallbacks(): void {
    // Initialize empty callback arrays for all event types
    const eventTypes: IndicatorEventType[] = [
      'data-added',
      'value-calculated',
      'error-occurred',
      'config-changed',
      'reset',
      'stream-started',
      'stream-stopped'
    ]

    eventTypes.forEach((type) => {
      this._eventCallbacks.set(type, [])
    })
  }

  protected _emitEvent(type: IndicatorEventType, payload: unknown): void {
    const event: IndicatorEvent = {
      type,
      indicatorName: this._name,
      payload,
      timestamp: Date.now()
    }
    this.emit(event)
  }

  protected _manageMemory(): void {
    if (!INDICATOR_CONSTANTS.MEMORY_MANAGEMENT.AUTO_CLEANUP) return

    const maxHistory =
      this._config.maxHistorySize || INDICATOR_CONSTANTS.MEMORY_MANAGEMENT.MAX_RETAINED_HISTORY

    // Trim data points if exceeding max history
    if (this._dataPoints.length > maxHistory) {
      const excessCount = this._dataPoints.length - maxHistory
      this._dataPoints.splice(0, excessCount)

      // Also trim output values proportionally
      if (this._outputValues.length > maxHistory - this._config.period + 1) {
        const outputExcess = this._outputValues.length - (maxHistory - this._config.period + 1)
        this._outputValues.splice(0, outputExcess)
      }

      logger.debug(INDICATOR_LOG_CATEGORIES.PERFORMANCE, `${this._name} memory cleanup performed`, {
        removedDataPoints: excessCount,
        remainingDataPoints: this._dataPoints.length
      })
    }
  }
}
