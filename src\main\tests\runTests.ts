/**
 * Test Runner for Signal Generation System
 * Executes comprehensive integration tests and generates reports
 */

import { logger } from '../../shared/utils/logger'
import { SignalGenerationIntegrationTest } from './SignalGenerationIntegrationTest'

/**
 * Test runner configuration
 */
interface TestRunnerConfig {
  enableDetailedLogging: boolean
  testDataSize: number
  timeoutMs: number
  expectedMinSignals: number
  expectedMinWinRate: number
  generateReport: boolean
  reportPath?: string
}

/**
 * Default test configuration
 */
const DEFAULT_CONFIG: TestRunnerConfig = {
  enableDetailedLogging: false,
  testDataSize: 100,
  timeoutMs: 60000, // 1 minute
  expectedMinSignals: 5,
  expectedMinWinRate: 0.6,
  generateReport: true,
  reportPath: './test-results.json'
}

/**
 * Test report generator
 */
class TestReportGenerator {
  /**
   * Generate comprehensive test report
   */
  public static generateReport(results: any[], config: TestRunnerConfig): string {
    const totalTests = results.length
    const passedTests = results.filter(t => t.passed).length
    const failedTests = totalTests - passedTests
    const successRate = (passedTests / totalTests) * 100
    const totalDuration = results.reduce((sum, t) => sum + t.duration, 0)

    const report = {
      summary: {
        totalTests,
        passedTests,
        failedTests,
        successRate: parseFloat(successRate.toFixed(1)),
        totalDuration,
        timestamp: new Date().toISOString()
      },
      configuration: config,
      testResults: results,
      failedTests: results.filter(t => !t.passed).map(t => ({
        testName: t.testName,
        errors: t.errors,
        duration: t.duration
      })),
      performanceMetrics: {
        averageTestDuration: totalDuration / totalTests,
        slowestTest: results.reduce((slowest, current) => 
          current.duration > slowest.duration ? current : slowest
        ),
        fastestTest: results.reduce((fastest, current) => 
          current.duration < fastest.duration ? current : fastest
        )
      }
    }

    return JSON.stringify(report, null, 2)
  }

  /**
   * Generate markdown report
   */
  public static generateMarkdownReport(results: any[], config: TestRunnerConfig): string {
    const totalTests = results.length
    const passedTests = results.filter(t => t.passed).length
    const failedTests = totalTests - passedTests
    const successRate = (passedTests / totalTests) * 100
    const totalDuration = results.reduce((sum, t) => sum + t.duration, 0)

    let markdown = `# Signal Generation System Test Report\n\n`
    markdown += `**Generated:** ${new Date().toISOString()}\n\n`
    
    markdown += `## Summary\n\n`
    markdown += `| Metric | Value |\n`
    markdown += `|--------|-------|\n`
    markdown += `| Total Tests | ${totalTests} |\n`
    markdown += `| Passed | ${passedTests} |\n`
    markdown += `| Failed | ${failedTests} |\n`
    markdown += `| Success Rate | ${successRate.toFixed(1)}% |\n`
    markdown += `| Total Duration | ${totalDuration}ms |\n\n`

    markdown += `## Test Results\n\n`
    markdown += `| Test Name | Status | Duration | Details |\n`
    markdown += `|-----------|--------|----------|----------|\n`
    
    results.forEach(test => {
      const status = test.passed ? '✅ PASS' : '❌ FAIL'
      const details = test.passed ? 'Success' : test.errors.join(', ')
      markdown += `| ${test.testName} | ${status} | ${test.duration}ms | ${details} |\n`
    })

    if (failedTests > 0) {
      markdown += `\n## Failed Tests Details\n\n`
      results.filter(t => !t.passed).forEach(test => {
        markdown += `### ${test.testName}\n\n`
        markdown += `**Duration:** ${test.duration}ms\n\n`
        markdown += `**Errors:**\n`
        test.errors.forEach((error: string) => {
          markdown += `- ${error}\n`
        })
        markdown += `\n`
      })
    }

    markdown += `\n## Configuration\n\n`
    markdown += `\`\`\`json\n${JSON.stringify(config, null, 2)}\n\`\`\`\n`

    return markdown
  }
}

/**
 * Main test runner function
 */
export async function runSignalGenerationTests(
  customConfig: Partial<TestRunnerConfig> = {}
): Promise<void> {
  const config = { ...DEFAULT_CONFIG, ...customConfig }
  
  logger.info('TestRunner', '🚀 Starting Signal Generation System Tests')
  logger.info('TestRunner', `Configuration: ${JSON.stringify(config, null, 2)}`)

  try {
    // Initialize test suite
    const testSuite = new SignalGenerationIntegrationTest({
      enableLogging: config.enableDetailedLogging,
      testDataSize: config.testDataSize,
      timeoutMs: config.timeoutMs,
      expectedMinSignals: config.expectedMinSignals,
      expectedMinWinRate: config.expectedMinWinRate
    })

    // Run all tests
    const startTime = Date.now()
    const results = await testSuite.runAllTests()
    const totalDuration = Date.now() - startTime

    // Generate summary
    const totalTests = results.length
    const passedTests = results.filter(t => t.passed).length
    const failedTests = totalTests - passedTests
    const successRate = (passedTests / totalTests) * 100

    // Log results
    logger.info('TestRunner', '📊 Test Execution Complete')
    logger.info('TestRunner', `Total Tests: ${totalTests}`)
    logger.info('TestRunner', `Passed: ${passedTests}`)
    logger.info('TestRunner', `Failed: ${failedTests}`)
    logger.info('TestRunner', `Success Rate: ${successRate.toFixed(1)}%`)
    logger.info('TestRunner', `Total Duration: ${totalDuration}ms`)

    // Generate and save report if requested
    if (config.generateReport) {
      try {
        const fs = await import('fs/promises')
        
        // Generate JSON report
        const jsonReport = TestReportGenerator.generateReport(results, config)
        if (config.reportPath) {
          await fs.writeFile(config.reportPath, jsonReport)
          logger.info('TestRunner', `📄 JSON report saved to: ${config.reportPath}`)
        }

        // Generate Markdown report
        const markdownReport = TestReportGenerator.generateMarkdownReport(results, config)
        const markdownPath = config.reportPath?.replace('.json', '.md') || './test-results.md'
        await fs.writeFile(markdownPath, markdownReport)
        logger.info('TestRunner', `📄 Markdown report saved to: ${markdownPath}`)

      } catch (error) {
        logger.error('TestRunner', 'Failed to generate test report:', error)
      }
    }

    // Exit with appropriate code
    if (failedTests > 0) {
      logger.error('TestRunner', '❌ Some tests failed')
      process.exit(1)
    } else {
      logger.success('TestRunner', '✅ All tests passed!')
      process.exit(0)
    }

  } catch (error) {
    logger.error('TestRunner', '💥 Test execution failed:', error)
    process.exit(1)
  }
}

/**
 * CLI entry point
 */
if (require.main === module) {
  // Parse command line arguments
  const args = process.argv.slice(2)
  const config: Partial<TestRunnerConfig> = {}

  // Simple argument parsing
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i]
    const value = args[i + 1]

    switch (key) {
      case '--verbose':
        config.enableDetailedLogging = true
        i-- // No value for this flag
        break
      case '--data-size':
        config.testDataSize = parseInt(value)
        break
      case '--timeout':
        config.timeoutMs = parseInt(value)
        break
      case '--min-signals':
        config.expectedMinSignals = parseInt(value)
        break
      case '--min-win-rate':
        config.expectedMinWinRate = parseFloat(value)
        break
      case '--no-report':
        config.generateReport = false
        i-- // No value for this flag
        break
      case '--report-path':
        config.reportPath = value
        break
      case '--help':
        console.log(`
Signal Generation System Test Runner

Usage: node runTests.js [options]

Options:
  --verbose              Enable detailed logging
  --data-size <number>   Number of test data points (default: 100)
  --timeout <number>     Test timeout in milliseconds (default: 60000)
  --min-signals <number> Minimum expected signals (default: 5)
  --min-win-rate <float> Minimum expected win rate (default: 0.6)
  --no-report           Disable report generation
  --report-path <path>   Custom report file path (default: ./test-results.json)
  --help                Show this help message

Examples:
  node runTests.js --verbose --data-size 200
  node runTests.js --min-win-rate 0.7 --report-path ./custom-report.json
        `)
        process.exit(0)
    }
  }

  // Run tests with parsed configuration
  runSignalGenerationTests(config).catch(error => {
    console.error('Failed to run tests:', error)
    process.exit(1)
  })
}

// Export for programmatic use
export { runSignalGenerationTests, TestReportGenerator }
export type { TestRunnerConfig }
