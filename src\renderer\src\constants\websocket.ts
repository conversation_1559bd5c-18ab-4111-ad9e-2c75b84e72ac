/**
 * WebSocket event constants for renderer process
 * Re-exports and extends constants from shared broker constants
 */

import { BROADCAST_EVENTS } from '../../../shared/constants/broker'

/**
 * WebSocket event names used in the renderer process
 * These correspond to events broadcast from the main process broker
 */
export const WEBSOCKET_EVENTS = {
  /** Account balance update event */
  BALANCE_UPDATE: BROADCAST_EVENTS.ACCOUNT_BALANCE,
  /** Connection state change event */
  STATE_CHANGE: BROADCAST_EVENTS.STATE_CHANGE,
  /** Broker connected event */
  CONNECTED: BROADCAST_EVENTS.CONNECTED,
  /** Broker disconnected event */
  DISCONNECTED: BROADCAST_EVENTS.DISCONNECTED,
  /** Connection error event */
  CONNECTION_ERROR: BROADCAST_EVENTS.CONNECTION_ERROR,
  /** General error event */
  ERROR: BROADCAST_EVENTS.ERROR,
  /** Heartbeat sent event */
  HEARTBEAT_SENT: BROADCAST_EVENTS.HEARTBEAT_SENT,
  /** Heartbeat received event */
  HEARTBEAT_RECEIVED: BROADCAST_EVENTS.HEARTBEAT_RECEIVED,
  /** Heartbeat failed event */
  HEARTBEAT_FAILED: BROADCAST_EVENTS.HEARTBEAT_FAILED,
  /** Heartbeat health change event */
  HEARTBEAT_HEALTH_CHANGE: BROADCAST_EVENTS.HEARTBEAT_HEALTH_CHANGE
} as const

/**
 * Main IPC channel for broker events
 */
export const BROKER_EVENT_CHANNEL = 'broker:event' as const

/**
 * WebSocket event configuration defaults
 */
export const WEBSOCKET_CONFIG = {
  /** Default enabled state for event listeners */
  DEFAULT_ENABLED: true,
  /** Default timeout for event handlers (ms) */
  DEFAULT_TIMEOUT: 5000,
  /** Maximum number of event handler retries */
  MAX_RETRIES: 3
} as const

/**
 * Event validation settings
 */
export const EVENT_VALIDATION = {
  /** Maximum allowed balance value */
  MAX_BALANCE: 1000000,
  /** Minimum allowed balance value */
  MIN_BALANCE: 0,
  /** Maximum event data size in bytes */
  MAX_EVENT_SIZE: 1024 * 1024, // 1MB
  /** Timeout for event validation (ms) */
  VALIDATION_TIMEOUT: 1000
} as const
