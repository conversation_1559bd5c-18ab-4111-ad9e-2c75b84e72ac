export class Region {
  private static readonly REGIONS: Record<string, string> = {
    ASIA: `wss://api-asia.po.market`,
    EU: `wss://api-eu.po.market`,
    US: `wss://api-us.po.market`,
    DEV: `wss://dev-api-eu.po.market`
  }

  private static readonly ORIGINS: Record<string, string> = {
    DEMO: `https://pocketoption.com`
  }

  public static readonly DEMO_REGION: string = `wss://demo-api-eu.po.market`
  public static readonly SOCKET_OPTIONS = {
    transports: ['websocket'],
    query: {
      EIO: '4',
      transport: ['websocket']
    },
    extraHeaders: {
      Origin: Region.ORIGINS.DEMO
    },
    path: '/socket.io/'
  }

  static get(key: keyof typeof Region.REGIONS): string {
    const endpoint = Region.REGIONS[key as string]
    if (!endpoint) {
      throw new Error(`Region ${key} not found`)
    }

    return endpoint
  }

  static getRegion(randomize: boolean = true): string[] {
    const endpoint = Object.values(Region.REGIONS)
    if (randomize) {
      return endpoint.sort(() => Math.random() - 0.5)
    }

    return endpoint
  }
}
