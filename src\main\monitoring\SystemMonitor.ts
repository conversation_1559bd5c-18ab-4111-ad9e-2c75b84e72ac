/**
 * System Monitor
 * Comprehensive monitoring system for signal generation pipeline
 * Tracks performance, errors, memory usage, and system health
 */

import { logger } from '../../shared/utils/logger'
import { SignalBroadcaster } from '../events/SignalBroadcaster'
import type { GeneratedSignal } from '../../shared/types/signals'

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  // Processing metrics
  totalSignalsProcessed: number
  averageProcessingTime: number
  maxProcessingTime: number
  minProcessingTime: number

  // Error metrics
  totalErrors: number
  errorRate: number
  errorsByType: Record<string, number>

  // Memory metrics
  memoryUsage: {
    heapUsed: number
    heapTotal: number
    external: number
    rss: number
  }

  // System metrics
  cpuUsage: number
  uptime: number

  // Signal metrics
  signalGenerationRate: number
  signalQuality: {
    averageConfidence: number
    confidenceDistribution: Record<string, number>
  }

  // Timestamp
  timestamp: number
}

/**
 * Error tracking information
 */
export interface ErrorInfo {
  id: string
  type: string
  message: string
  stack?: string
  context: Record<string, any>
  timestamp: number
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  component: string
}

/**
 * Health status
 */
export interface SystemHealth {
  status: 'HEALTHY' | 'DEGRADED' | 'UNHEALTHY' | 'CRITICAL'
  score: number // 0-100
  issues: Array<{
    component: string
    issue: string
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    timestamp: number
  }>
  lastCheck: number
}

/**
 * System Monitor - Singleton
 */
export class SystemMonitor {
  private static instance: SystemMonitor | null = null
  private isEnabled: boolean = true
  private monitoringInterval: NodeJS.Timeout | null = null
  private metricsHistory: PerformanceMetrics[] = []
  private errorHistory: ErrorInfo[] = []
  private processingTimes: number[] = []
  private signalHistory: Array<{ timestamp: number; confidence: number }> = []
  private errorCounter: number = 0
  private maxHistorySize: number = 1000
  private healthCheckInterval: number = 30000 // 30 seconds
  private performanceCheckInterval: number = 5000 // 5 seconds

  /**
   * Get singleton instance
   */
  public static getInstance(): SystemMonitor {
    if (!SystemMonitor.instance) {
      SystemMonitor.instance = new SystemMonitor()
    }
    return SystemMonitor.instance
  }

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    this.startMonitoring()
    logger.info('SystemMonitor', 'SystemMonitor instance created and monitoring started')
  }

  /**
   * Enable/disable monitoring
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled

    if (enabled && !this.monitoringInterval) {
      this.startMonitoring()
    } else if (!enabled && this.monitoringInterval) {
      this.stopMonitoring()
    }

    logger.info('SystemMonitor', `System monitoring ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Record signal processing time
   */
  public recordProcessingTime(processingTime: number): void {
    if (!this.isEnabled) return

    this.processingTimes.push(processingTime)

    // Maintain size limit
    if (this.processingTimes.length > this.maxHistorySize) {
      this.processingTimes.shift()
    }
  }

  /**
   * Record generated signal for quality tracking
   */
  public recordSignal(signal: GeneratedSignal): void {
    if (!this.isEnabled) return

    this.signalHistory.push({
      timestamp: signal.timestamp,
      confidence: signal.confidence
    })

    // Maintain size limit
    if (this.signalHistory.length > this.maxHistorySize) {
      this.signalHistory.shift()
    }
  }

  /**
   * Record error with context
   */
  public recordError(
    type: string,
    message: string,
    component: string,
    severity: ErrorInfo['severity'] = 'MEDIUM',
    context: Record<string, any> = {},
    stack?: string
  ): void {
    if (!this.isEnabled) return

    const errorInfo: ErrorInfo = {
      id: `error_${++this.errorCounter}`,
      type,
      message,
      stack,
      context,
      timestamp: Date.now(),
      severity,
      component
    }

    this.errorHistory.push(errorInfo)

    // Maintain size limit
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory.shift()
    }

    // Log error based on severity
    switch (severity) {
      case 'CRITICAL':
        logger.error('SystemMonitor', `CRITICAL ERROR in ${component}: ${message}`, context)
        break
      case 'HIGH':
        logger.error('SystemMonitor', `HIGH SEVERITY ERROR in ${component}: ${message}`, context)
        break
      case 'MEDIUM':
        logger.warn('SystemMonitor', `ERROR in ${component}: ${message}`, context)
        break
      case 'LOW':
        logger.debug('SystemMonitor', `Minor error in ${component}: ${message}`, context)
        break
    }

    // Broadcast error event for critical and high severity errors
    if (severity === 'CRITICAL' || severity === 'HIGH') {
      this.broadcastErrorAlert(errorInfo)
    }
  }

  /**
   * Get current performance metrics
   */
  public getPerformanceMetrics(): PerformanceMetrics {
    const memoryUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()

    // Calculate processing time statistics
    const avgProcessingTime =
      this.processingTimes.length > 0
        ? this.processingTimes.reduce((sum, time) => sum + time, 0) / this.processingTimes.length
        : 0
    const maxProcessingTime =
      this.processingTimes.length > 0 ? Math.max(...this.processingTimes) : 0
    const minProcessingTime =
      this.processingTimes.length > 0 ? Math.min(...this.processingTimes) : 0

    // Calculate error rate (errors per hour)
    const oneHourAgo = Date.now() - 60 * 60 * 1000
    const recentErrors = this.errorHistory.filter((error) => error.timestamp > oneHourAgo)
    const errorRate = recentErrors.length

    // Calculate signal generation rate (signals per minute)
    const oneMinuteAgo = Date.now() - 60 * 1000
    const recentSignals = this.signalHistory.filter((signal) => signal.timestamp > oneMinuteAgo)
    const signalGenerationRate = recentSignals.length

    // Calculate signal quality metrics
    const averageConfidence =
      this.signalHistory.length > 0
        ? this.signalHistory.reduce((sum, signal) => sum + signal.confidence, 0) /
          this.signalHistory.length
        : 0

    const confidenceDistribution = this.calculateConfidenceDistribution()

    // Group errors by type
    const errorsByType: Record<string, number> = {}
    this.errorHistory.forEach((error) => {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1
    })

    return {
      totalSignalsProcessed: this.signalHistory.length,
      averageProcessingTime: avgProcessingTime,
      maxProcessingTime,
      minProcessingTime,

      totalErrors: this.errorHistory.length,
      errorRate,
      errorsByType,

      memoryUsage: {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss
      },

      cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
      uptime: process.uptime(),

      signalGenerationRate,
      signalQuality: {
        averageConfidence,
        confidenceDistribution
      },

      timestamp: Date.now()
    }
  }

  /**
   * Get system health status
   */
  public getSystemHealth(): SystemHealth {
    const metrics = this.getPerformanceMetrics()
    const issues: SystemHealth['issues'] = []
    let score = 100

    // Check memory usage
    const memoryUsagePercent = (metrics.memoryUsage.heapUsed / metrics.memoryUsage.heapTotal) * 100
    if (memoryUsagePercent > 90) {
      issues.push({
        component: 'Memory',
        issue: `High memory usage: ${memoryUsagePercent.toFixed(1)}%`,
        severity: 'HIGH',
        timestamp: Date.now()
      })
      score -= 20
    } else if (memoryUsagePercent > 75) {
      issues.push({
        component: 'Memory',
        issue: `Elevated memory usage: ${memoryUsagePercent.toFixed(1)}%`,
        severity: 'MEDIUM',
        timestamp: Date.now()
      })
      score -= 10
    }

    // Check error rate
    if (metrics.errorRate > 10) {
      issues.push({
        component: 'ErrorRate',
        issue: `High error rate: ${metrics.errorRate} errors/hour`,
        severity: 'HIGH',
        timestamp: Date.now()
      })
      score -= 25
    } else if (metrics.errorRate > 5) {
      issues.push({
        component: 'ErrorRate',
        issue: `Elevated error rate: ${metrics.errorRate} errors/hour`,
        severity: 'MEDIUM',
        timestamp: Date.now()
      })
      score -= 10
    }

    // Check processing time
    if (metrics.averageProcessingTime > 1000) {
      issues.push({
        component: 'Performance',
        issue: `Slow processing: ${metrics.averageProcessingTime.toFixed(0)}ms average`,
        severity: 'MEDIUM',
        timestamp: Date.now()
      })
      score -= 15
    }

    // Check signal generation rate
    if (metrics.signalGenerationRate === 0 && metrics.uptime > 300) {
      // No signals for 5+ minutes
      issues.push({
        component: 'SignalGeneration',
        issue: 'No signals generated recently',
        severity: 'HIGH',
        timestamp: Date.now()
      })
      score -= 30
    }

    // Determine overall status
    let status: SystemHealth['status']
    if (score >= 90) {
      status = 'HEALTHY'
    } else if (score >= 70) {
      status = 'DEGRADED'
    } else if (score >= 50) {
      status = 'UNHEALTHY'
    } else {
      status = 'CRITICAL'
    }

    return {
      status,
      score: Math.max(0, score),
      issues,
      lastCheck: Date.now()
    }
  }

  /**
   * Get error history
   */
  public getErrorHistory(limit: number = 100): ErrorInfo[] {
    return this.errorHistory.slice(-limit)
  }

  /**
   * Get metrics history
   */
  public getMetricsHistory(limit: number = 100): PerformanceMetrics[] {
    return this.metricsHistory.slice(-limit)
  }

  /**
   * Clear all monitoring data
   */
  public clearHistory(): void {
    this.metricsHistory = []
    this.errorHistory = []
    this.processingTimes = []
    this.signalHistory = []
    this.errorCounter = 0

    logger.info('SystemMonitor', 'Monitoring history cleared')
  }

  /**
   * Shutdown monitoring
   */
  public shutdown(): void {
    this.stopMonitoring()
    this.clearHistory()
    this.isEnabled = false

    logger.info('SystemMonitor', 'SystemMonitor shutdown completed')
  }

  /**
   * Start monitoring intervals
   */
  private startMonitoring(): void {
    if (this.monitoringInterval) {
      return
    }

    this.monitoringInterval = setInterval(() => {
      this.collectMetrics()
      this.performHealthCheck()
    }, this.performanceCheckInterval)

    logger.debug('SystemMonitor', 'Monitoring intervals started')
  }

  /**
   * Stop monitoring intervals
   */
  private stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }

    logger.debug('SystemMonitor', 'Monitoring intervals stopped')
  }

  /**
   * Collect performance metrics
   */
  private collectMetrics(): void {
    if (!this.isEnabled) return

    try {
      const metrics = this.getPerformanceMetrics()
      this.metricsHistory.push(metrics)

      // Maintain history size limit
      if (this.metricsHistory.length > this.maxHistorySize) {
        this.metricsHistory.shift()
      }

      // Broadcast metrics update
      this.broadcastMetricsUpdate(metrics)
    } catch (error) {
      this.recordError(
        'METRICS_COLLECTION_ERROR',
        'Failed to collect performance metrics',
        'SystemMonitor',
        'MEDIUM',
        { error: error instanceof Error ? error.message : String(error) }
      )
    }
  }

  /**
   * Perform health check
   */
  private performHealthCheck(): void {
    if (!this.isEnabled) return

    try {
      const health = this.getSystemHealth()

      // Log health status changes
      if (health.status !== 'HEALTHY') {
        logger.warn('SystemMonitor', `System health: ${health.status} (Score: ${health.score})`, {
          issues: health.issues
        })
      }

      // Broadcast health update
      this.broadcastHealthUpdate(health)
    } catch (error) {
      this.recordError(
        'HEALTH_CHECK_ERROR',
        'Failed to perform health check',
        'SystemMonitor',
        'MEDIUM',
        { error: error instanceof Error ? error.message : String(error) }
      )
    }
  }

  /**
   * Calculate confidence distribution
   */
  private calculateConfidenceDistribution(): Record<string, number> {
    const distribution = {
      low: 0, // 0-0.6
      medium: 0, // 0.6-0.8
      high: 0, // 0.8-0.9
      very_high: 0 // 0.9-1.0
    }

    this.signalHistory.forEach((signal) => {
      if (signal.confidence < 0.6) {
        distribution.low++
      } else if (signal.confidence < 0.8) {
        distribution.medium++
      } else if (signal.confidence < 0.9) {
        distribution.high++
      } else {
        distribution.very_high++
      }
    })

    return distribution
  }

  /**
   * Broadcast error alert
   */
  private broadcastErrorAlert(errorInfo: ErrorInfo): void {
    try {
      const broadcaster = SignalBroadcaster.getInstance()
      broadcaster.queueEvent('error-alert', {
        error: errorInfo,
        timestamp: Date.now()
      })
    } catch (error) {
      logger.error('SystemMonitor', 'Failed to broadcast error alert:', error)
    }
  }

  /**
   * Broadcast metrics update
   */
  private broadcastMetricsUpdate(metrics: PerformanceMetrics): void {
    try {
      const broadcaster = SignalBroadcaster.getInstance()
      broadcaster.queueEvent('performance-metrics', metrics)
    } catch (error) {
      logger.error('SystemMonitor', 'Failed to broadcast metrics update:', error)
    }
  }

  /**
   * Broadcast health update
   */
  private broadcastHealthUpdate(health: SystemHealth): void {
    try {
      const broadcaster = SignalBroadcaster.getInstance()
      broadcaster.queueEvent('system-health', health)
    } catch (error) {
      logger.error('SystemMonitor', 'Failed to broadcast health update:', error)
    }
  }
}

/**
 * Error handling utilities
 */
export class ErrorHandler {
  private static monitor = SystemMonitor.getInstance()

  /**
   * Handle and record error with context
   */
  public static handleError(
    error: Error | unknown,
    component: string,
    context: Record<string, any> = {},
    severity: ErrorInfo['severity'] = 'MEDIUM'
  ): void {
    const errorMessage = error instanceof Error ? error.message : String(error)
    const errorStack = error instanceof Error ? error.stack : undefined
    const errorType = error instanceof Error ? error.constructor.name : 'UnknownError'

    this.monitor.recordError(errorType, errorMessage, component, severity, context, errorStack)
  }

  /**
   * Wrap async function with error handling
   */
  public static async wrapAsync<T>(
    fn: () => Promise<T>,
    component: string,
    context: Record<string, any> = {}
  ): Promise<T | null> {
    try {
      return await fn()
    } catch (error) {
      this.handleError(error, component, context, 'HIGH')
      return null
    }
  }

  /**
   * Wrap sync function with error handling
   */
  public static wrapSync<T>(
    fn: () => T,
    component: string,
    context: Record<string, any> = {}
  ): T | null {
    try {
      return fn()
    } catch (error) {
      this.handleError(error, component, context, 'HIGH')
      return null
    }
  }
}

/**
 * Performance monitoring utilities
 */
export class PerformanceTracker {
  private static monitor = SystemMonitor.getInstance()
  private static activeTimers = new Map<string, number>()

  /**
   * Start timing an operation
   */
  public static startTimer(operationId: string): void {
    this.activeTimers.set(operationId, Date.now())
  }

  /**
   * End timing and record performance
   */
  public static endTimer(operationId: string): number {
    const startTime = this.activeTimers.get(operationId)
    if (!startTime) {
      return 0
    }

    const duration = Date.now() - startTime
    this.activeTimers.delete(operationId)
    this.monitor.recordProcessingTime(duration)

    return duration
  }

  /**
   * Time an async operation
   */
  public static async timeAsync<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<{ result: T; duration: number }> {
    const startTime = Date.now()
    const result = await operation()
    const duration = Date.now() - startTime

    this.monitor.recordProcessingTime(duration)

    return { result, duration }
  }

  /**
   * Time a sync operation
   */
  public static timeSync<T>(
    operation: () => T,
    operationName: string
  ): { result: T; duration: number } {
    const startTime = Date.now()
    const result = operation()
    const duration = Date.now() - startTime

    this.monitor.recordProcessingTime(duration)

    return { result, duration }
  }
}
