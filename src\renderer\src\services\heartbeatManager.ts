/**
 * Heartbeat Manager for Broker Service
 * Manages heartbeat mechanism with configurable intervals, automatic reconnection,
 * and comprehensive event broadcasting for connection health monitoring
 */

import type {
  HeartbeatConfig,
  HeartbeatSentData,
  HeartbeatReceivedData,
  HeartbeatFailedData,
  HeartbeatHealthChangeData,
  BrokerEventHandlers
} from '../interfaces/broker'
import {
  DEFAULT_BROKER_CONFIG,
  HEARTBEAT_STATUS,
  LOG_PREFIXES,
  ERROR_MESSAGES,
  BROKER_EVENTS
} from '../constants/brokerService'

/**
 * Heartbeat Manager Class
 * Handles heartbeat monitoring, health tracking, and automatic reconnection
 */
export class HeartbeatManager {
  private config: HeartbeatConfig
  private intervalId: NodeJS.Timeout | null = null
  private timeoutId: NodeJS.Timeout | null = null
  private missedBeats = 0
  private lastSent = 0
  private lastReceived = 0
  private currentHealth = HEARTBEAT_STATUS.STOPPED
  private isActive = false
  private eventHandlers: BrokerEventHandlers = {}
  private reconnectAttempts = 0
  private maxReconnectAttempts: number
  private reconnectDelay: number

  /**
   * Creates a new HeartbeatManager instance
   * @param config - Heartbeat configuration (optional, uses defaults if not provided)
   * @param maxReconnectAttempts - Maximum reconnection attempts
   * @param reconnectDelay - Delay between reconnection attempts
   */
  constructor(
    config: Partial<HeartbeatConfig> = {},
    maxReconnectAttempts = DEFAULT_BROKER_CONFIG.maxReconnectAttempts,
    reconnectDelay = DEFAULT_BROKER_CONFIG.reconnectDelay
  ) {
    this.config = { ...DEFAULT_BROKER_CONFIG.heartbeat, ...config }
    this.maxReconnectAttempts = maxReconnectAttempts
    this.reconnectDelay = reconnectDelay
  }

  /**
   * Starts the heartbeat mechanism
   * @param eventHandlers - Event handlers for heartbeat events
   */
  public start(eventHandlers: BrokerEventHandlers = {}): void {
    if (this.isActive) {
      console.warn(`${LOG_PREFIXES.HEARTBEAT} Heartbeat already active`)
      return
    }

    if (!this.config.enabled) {
      console.info(`${LOG_PREFIXES.HEARTBEAT} Heartbeat disabled in configuration`)
      return
    }

    this.eventHandlers = eventHandlers
    this.isActive = true
    this.missedBeats = 0
    this.reconnectAttempts = 0
    this.updateHealth(HEARTBEAT_STATUS.HEALTHY)

    this.scheduleNextHeartbeat()
    console.info(`${LOG_PREFIXES.HEARTBEAT} Started with interval ${this.config.interval}ms`)
  }

  /**
   * Stops the heartbeat mechanism
   */
  public stop(): void {
    if (!this.isActive) {
      return
    }

    this.clearTimers()
    this.isActive = false
    this.updateHealth(HEARTBEAT_STATUS.STOPPED)
    console.info(`${LOG_PREFIXES.HEARTBEAT} Stopped`)
  }

  /**
   * Records a successful heartbeat response
   * @param responseTime - Response time in milliseconds
   */
  public recordHeartbeatReceived(responseTime: number): void {
    if (!this.isActive) {
      return
    }

    this.lastReceived = Date.now()
    this.missedBeats = 0
    this.clearTimeout()
    this.updateHealth(HEARTBEAT_STATUS.HEALTHY)

    const data: HeartbeatReceivedData = {
      timestamp: this.lastReceived,
      responseTime,
      missedBeats: this.missedBeats
    }

    this.broadcastEvent(BROKER_EVENTS.HEARTBEAT_RECEIVED, data)
    this.eventHandlers.onHeartbeatReceived?.(data)

    // Schedule next heartbeat
    this.scheduleNextHeartbeat()
  }

  /**
   * Records a failed heartbeat (timeout or error)
   */
  public recordHeartbeatFailed(): void {
    if (!this.isActive) {
      return
    }

    this.missedBeats++
    this.clearTimeout()

    const data: HeartbeatFailedData = {
      missedBeats: this.missedBeats,
      maxMissedBeats: this.config.maxMissedBeats
    }

    this.broadcastEvent(BROKER_EVENTS.HEARTBEAT_FAILED, data)
    this.eventHandlers.onHeartbeatFailed?.(data)

    // Update health based on missed beats
    if (this.missedBeats >= this.config.maxMissedBeats) {
      this.updateHealth(HEARTBEAT_STATUS.UNHEALTHY)
      this.handleConnectionFailure()
    } else if (this.missedBeats >= 2) {
      this.updateHealth(HEARTBEAT_STATUS.DEGRADED)
    }

    // Schedule next heartbeat attempt
    this.scheduleNextHeartbeat()
  }

  /**
   * Gets current heartbeat statistics
   * @returns Current heartbeat statistics
   */
  public getStats() {
    return {
      health: this.currentHealth,
      missedBeats: this.missedBeats,
      lastSent: this.lastSent,
      lastReceived: this.lastReceived,
      isActive: this.isActive
    }
  }

  /**
   * Updates heartbeat configuration
   * @param newConfig - New configuration values
   */
  public updateConfig(newConfig: Partial<HeartbeatConfig>): void {
    const wasActive = this.isActive

    if (wasActive) {
      this.stop()
    }

    this.config = { ...this.config, ...newConfig }

    if (wasActive && this.config.enabled) {
      this.start(this.eventHandlers)
    }
  }

  /**
   * Schedules the next heartbeat
   */
  private scheduleNextHeartbeat(): void {
    this.clearTimers()

    if (!this.isActive || !this.config.enabled) {
      return
    }

    this.intervalId = setTimeout(() => {
      this.sendHeartbeat()
    }, this.config.interval)
  }

  /**
   * Sends a heartbeat and sets up timeout
   */
  private sendHeartbeat(): void {
    if (!this.isActive) {
      return
    }

    this.lastSent = Date.now()

    const data: HeartbeatSentData = {
      timestamp: this.lastSent,
      missedBeats: this.missedBeats
    }

    this.broadcastEvent(BROKER_EVENTS.HEARTBEAT_SENT, data)
    this.eventHandlers.onHeartbeatSent?.(data)

    // Set timeout for heartbeat response
    this.timeoutId = setTimeout(() => {
      this.recordHeartbeatFailed()
    }, this.config.timeout)

    // Send heartbeat via IPC (this would be implemented by the broker service)
    this.requestHeartbeat()
  }

  /**
   * Requests heartbeat from main process
   */
  private async requestHeartbeat(): Promise<void> {
    try {
      // This would be implemented by the broker service
      // For now, we'll simulate the heartbeat request
      await window.api.invoke('broker:heartbeat')
    } catch (error) {
      console.error(`${LOG_PREFIXES.HEARTBEAT} Failed to send heartbeat:`, error)
      this.recordHeartbeatFailed()
    }
  }

  /**
   * Updates health status and broadcasts change
   * @param newHealth - New health status
   */
  private updateHealth(newHealth: string): void {
    if (this.currentHealth === newHealth) {
      return
    }

    const oldHealth = this.currentHealth
    this.currentHealth = newHealth

    const data: HeartbeatHealthChangeData = {
      from: oldHealth,
      to: newHealth,
      missedBeats: this.missedBeats
    }

    this.broadcastEvent(BROKER_EVENTS.HEARTBEAT_HEALTH_CHANGE, data)
    this.eventHandlers.onHeartbeatHealthChange?.(data)

    console.info(`${LOG_PREFIXES.HEARTBEAT} Health changed from ${oldHealth} to ${newHealth}`)
  }

  /**
   * Handles connection failure and attempts reconnection
   */
  private async handleConnectionFailure(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(`${LOG_PREFIXES.HEARTBEAT} Max reconnection attempts reached`)
      this.eventHandlers.onError?.(ERROR_MESSAGES.RECONNECT_FAILED)
      return
    }

    this.reconnectAttempts++
    console.info(
      `${LOG_PREFIXES.HEARTBEAT} Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}`
    )

    try {
      // Attempt reconnection via broker service
      await window.api.invoke('broker:reconnect')

      // Reset counters on successful reconnection
      this.reconnectAttempts = 0
      this.missedBeats = 0
      this.updateHealth(HEARTBEAT_STATUS.HEALTHY)
    } catch (error) {
      console.error(
        `${LOG_PREFIXES.HEARTBEAT} Reconnection attempt ${this.reconnectAttempts} failed:`,
        error
      )

      // Schedule next reconnection attempt
      setTimeout(() => {
        this.handleConnectionFailure()
      }, this.reconnectDelay * this.reconnectAttempts) // Exponential backoff
    }
  }

  /**
   * Broadcasts event to main process
   * @param eventName - Name of the event
   * @param data - Event data
   */
  private broadcastEvent(eventName: string, data: unknown): void {
    try {
      window.api.send('broker:event', eventName, data)
    } catch (error) {
      console.error(`${LOG_PREFIXES.HEARTBEAT} Failed to broadcast event ${eventName}:`, error)
    }
  }

  /**
   * Clears all active timers
   */
  private clearTimers(): void {
    if (this.intervalId) {
      clearTimeout(this.intervalId)
      this.intervalId = null
    }
    this.clearTimeout()
  }

  /**
   * Clears the heartbeat timeout
   */
  private clearTimeout(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId)
      this.timeoutId = null
    }
  }
}
