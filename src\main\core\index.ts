/**
 * Signal Engine and Strategy Factory Core Module
 * 
 * This module provides comprehensive trading signal generation capabilities
 * with flexible strategy creation and real-time signal processing.
 * 
 * @example
 * ```typescript
 * import { SignalEngine, StrategyFactory } from './core'
 * 
 * // Get singleton instances
 * const signalEngine = SignalEngine.getInstance()
 * const strategyFactory = StrategyFactory.getInstance()
 * 
 * // Create and use strategies
 * const strategy = signalEngine.createStrategy('rsi', { period: 14 })
 * const signals = signalEngine.processMarketData({ value: 45.67, timestamp: Date.now() })
 * ```
 */

// Export main classes
export { SignalEngine } from './SignalEngine'
export { StrategyFactory } from './StrategyFactory'

// Re-export types for convenience
export type {
  // Core interfaces
  Strategy,
  SignalEngine as ISignalEngine,
  StrategyFactory as IStrategyFactory,
  
  // Configuration types
  StrategyConfig,
  SingleIndicatorStrategyConfig,
  MultiIndicatorStrategyConfig,
  StrategyFactoryConfig,
  SignalEngineConfig,
  
  // Signal types
  TradingSignal,
  GeneratedSignal,
  IndicatorContribution,
  
  // Analysis types
  StrategyAnalysisResult,
  StrategyAnalysis,
  IndicatorAnalysis,
  
  // Rules and conditions
  IndicatorSignalRules,
  SignalCondition,
  SignalConditionParams,
  
  // Combination logic
  StrategyCombinationLogic,
  IndicatorWeight,
  
  // Performance and validation
  StrategyPerformanceMetrics,
  SignalEnginePerformanceMetrics,
  StrategyValidationResult,
  
  // Metadata
  SignalMetadata
} from '../../shared/types/signals'

// Re-export validation utilities
export {
  SignalValidationError,
  validateStrategyConfig,
  validateGeneratedSignal,
  isValidTradingSignal,
  sanitizeSignalError
} from '../../shared/utils/signalValidation'

// Re-export constants
export {
  // Signal constants
  TRADING_SIGNALS,
  COMBINATION_LOGIC,
  SIGNAL_CONDITION_TYPES,
  COMPARISON_OPERATORS,
  CROSSOVER_DIRECTIONS,
  RISK_LEVELS,
  
  // Default configurations
  DEFAULT_STRATEGY_CONFIG,
  DEFAULT_STRATEGY_FACTORY_CONFIG,
  DEFAULT_SIGNAL_ENGINE_CONFIG,
  
  // Default signal rules
  DEFAULT_RSI_SIGNAL_RULES,
  DEFAULT_SMA_SIGNAL_RULES,
  DEFAULT_BOLLINGER_BANDS_SIGNAL_RULES,
  DEFAULT_SIGNAL_RULES_BY_INDICATOR,
  
  // Validation constraints
  VALIDATION_CONSTRAINTS,
  
  // Performance constants
  PERFORMANCE_CONSTANTS,
  
  // Error messages
  SIGNAL_ENGINE_ERROR_MESSAGES,
  
  // Log categories
  SIGNAL_ENGINE_LOG_CATEGORIES,
  
  // Events
  SIGNAL_ENGINE_EVENTS,
  
  // Confidence levels
  DEFAULT_CONFIDENCE_LEVELS,
  
  // Priority and types
  SIGNAL_PRIORITY,
  STRATEGY_TYPES,
  MARKET_CONDITIONS
} from '../../shared/constants/signals'

/**
 * Utility functions for working with the Signal Engine
 */
export const SignalEngineUtils = {
  /**
   * Create a quick RSI strategy with default settings
   * @param period - RSI period (default: 14)
   * @param overbought - Overbought threshold (default: 70)
   * @param oversold - Oversold threshold (default: 30)
   * @returns Strategy instance
   */
  createQuickRSIStrategy: (
    period: number = 14,
    overbought: number = 70,
    oversold: number = 30
  ) => {
    const signalEngine = SignalEngine.getInstance()
    return signalEngine.createStrategy('rsi', {
      name: `rsi_${period}_${oversold}_${overbought}`,
      period,
      overbought,
      oversold
    })
  },

  /**
   * Create a quick SMA strategy with default settings
   * @param period - SMA period (default: 20)
   * @returns Strategy instance
   */
  createQuickSMAStrategy: (period: number = 20) => {
    const signalEngine = SignalEngine.getInstance()
    return signalEngine.createStrategy('sma', {
      name: `sma_${period}`,
      period
    })
  },

  /**
   * Create a combined RSI + SMA strategy
   * @param rsiPeriod - RSI period (default: 14)
   * @param smaPeriod - SMA period (default: 20)
   * @param combinationLogic - How to combine signals (default: 'AND')
   * @returns Strategy instance
   */
  createRSISMAStrategy: (
    rsiPeriod: number = 14,
    smaPeriod: number = 20,
    combinationLogic: StrategyCombinationLogic = 'AND'
  ) => {
    const signalEngine = SignalEngine.getInstance()
    return signalEngine.createStrategy(['rsi', 'sma'], {
      name: `rsi_${rsiPeriod}_sma_${smaPeriod}_${combinationLogic.toLowerCase()}`,
      rsi: { period: rsiPeriod },
      sma: { period: smaPeriod },
      combinationLogic
    })
  },

  /**
   * Get signal strength description based on confidence
   * @param confidence - Signal confidence (0-1)
   * @returns Signal strength description
   */
  getSignalStrength: (confidence: number): string => {
    if (confidence >= 0.8) return 'Very Strong'
    if (confidence >= 0.6) return 'Strong'
    if (confidence >= 0.4) return 'Moderate'
    if (confidence >= 0.2) return 'Weak'
    return 'Very Weak'
  },

  /**
   * Format signal for display
   * @param signal - Generated signal
   * @returns Formatted signal string
   */
  formatSignal: (signal: GeneratedSignal): string => {
    const strength = SignalEngineUtils.getSignalStrength(signal.confidence)
    const time = new Date(signal.timestamp).toLocaleTimeString()
    return `${signal.signal} (${strength} - ${(signal.confidence * 100).toFixed(1)}%) at ${time}`
  },

  /**
   * Get performance summary
   * @param metrics - Performance metrics
   * @returns Formatted performance summary
   */
  getPerformanceSummary: (metrics: SignalEnginePerformanceMetrics): string => {
    const memoryMB = Object.values(metrics.memoryUsage).reduce((sum, val) => sum + val, 0) / 1024 / 1024
    return [
      `Active Strategies: ${metrics.activeStrategies}/${metrics.totalStrategies}`,
      `Total Signals: ${metrics.totalSignals}`,
      `Avg Processing: ${metrics.averageProcessingTime.toFixed(2)}ms`,
      `Memory Usage: ${memoryMB.toFixed(2)}MB`
    ].join(' | ')
  }
}

/**
 * Strategy presets for common trading scenarios
 */
export const StrategyPresets = {
  /**
   * Conservative RSI strategy with higher thresholds
   */
  CONSERVATIVE_RSI: {
    name: 'conservative_rsi',
    description: 'Conservative RSI strategy with 20/80 thresholds',
    period: 14,
    overbought: 80,
    oversold: 20,
    minConfidence: 0.7
  },

  /**
   * Aggressive RSI strategy with lower thresholds
   */
  AGGRESSIVE_RSI: {
    name: 'aggressive_rsi',
    description: 'Aggressive RSI strategy with 30/70 thresholds',
    period: 14,
    overbought: 70,
    oversold: 30,
    minConfidence: 0.5
  },

  /**
   * Trend following SMA strategy
   */
  TREND_FOLLOWING_SMA: {
    name: 'trend_sma',
    description: 'Trend following strategy using SMA crossovers',
    period: 20,
    minConfidence: 0.6
  },

  /**
   * Multi-timeframe RSI strategy
   */
  MULTI_RSI: {
    name: 'multi_rsi',
    description: 'Multiple RSI periods with majority logic',
    indicators: ['rsi'],
    rsi_short: { period: 7 },
    rsi_medium: { period: 14 },
    rsi_long: { period: 21 },
    combinationLogic: 'MAJORITY' as StrategyCombinationLogic
  }
}

// Import types for utility functions
import type { 
  SignalEngine, 
  StrategyFactory, 
  GeneratedSignal, 
  SignalEnginePerformanceMetrics,
  StrategyCombinationLogic 
} from '../../shared/types/signals'
