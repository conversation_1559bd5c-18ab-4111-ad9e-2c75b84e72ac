/**
 * Test file demonstrating the usage of the improved PocketOption class
 * This is not a full test suite but shows how to use the Singleton pattern
 * and the new methods available in the class.
 */

import { PocketOption } from './PocketOption'

/**
 * Example usage of the PocketOption Singleton class
 */
export class PocketOptionExample {
  /**
   * Demonstrates basic usage of the PocketOption class
   */
  public static async demonstrateUsage(): Promise<void> {
    try {
      // Get singleton instance - only one instance will ever exist
      const broker1 = PocketOption.getInstance('test-session-id', true)
      const broker2 = PocketOption.getInstance('another-session-id', false)

      // Both variables point to the same instance (Singleton pattern)
      console.log('Same instance?', broker1 === broker2) // true

      // Check initial connection state
      console.log('Initial state:', broker1.getConnectionState()) // 'disconnected'
      console.log('Is connected?', broker1.isConnected()) // false

      // Connect to PocketOption
      await broker1.connect()

      // Check connection state after connecting
      console.log('State after connect:', broker1.getConnectionState()) // 'connecting' or 'connected'

      // Wait a bit for connection to establish
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Check if connected
      if (broker1.isConnected()) {
        console.log('Successfully connected to PocketOption!')
      }

      // Demonstrate reconnection
      await broker1.reconnect()

      // Disconnect when done
      broker1.disconnect()
      console.log('Final state:', broker1.getConnectionState()) // 'disconnected'
    } catch (error) {
      console.error('Error in demonstration:', error)
    } finally {
      // Clean up singleton instance
      PocketOption.destroyInstance()
    }
  }

  /**
   * Demonstrates error handling scenarios
   */
  public static async demonstrateErrorHandling(): Promise<void> {
    try {
      // Try to connect with invalid session ID
      const broker = PocketOption.getInstance('invalid-session', true)

      await broker.connect()
    } catch (error) {
      console.log('Expected error caught:', error instanceof Error ? error.message : error)
    } finally {
      PocketOption.destroyInstance()
    }
  }

  /**
   * Demonstrates connection state and heartbeat monitoring
   */
  public static monitorConnectionStates(): void {
    // In a real application, you would listen to the 'broker:event' events
    // from the Electron renderer process to monitor state changes

    console.log('Monitor connection states by listening to broker:event in renderer process')
    console.log('Available events:')
    console.log('- Connection: connected, disconnected, connection_error, error, state_change')
    console.log(
      '- Heartbeat: heartbeat_sent, heartbeat_received, heartbeat_failed, heartbeat_health_change'
    )

    // Example of getting instance for monitoring
    const broker = PocketOption.getInstance('test-session', true)
    console.log('Current state:', broker.getConnectionState())
    console.log('Heartbeat health:', broker.getHeartbeatHealth())
    console.log('Heartbeat stats:', broker.getHeartbeatStats())

    PocketOption.destroyInstance()
  }

  /**
   * Demonstrates heartbeat functionality
   */
  public static async demonstrateHeartbeat(): Promise<void> {
    try {
      const broker = PocketOption.getInstance('test-session', true)

      // Connect to start heartbeat
      await broker.connect()

      // Monitor heartbeat stats
      console.log('Initial heartbeat stats:', broker.getHeartbeatStats())

      // Wait for some heartbeat cycles
      await new Promise((resolve) => setTimeout(resolve, 5000))

      console.log('Heartbeat stats after 5 seconds:', broker.getHeartbeatStats())

      // Disconnect to stop heartbeat
      broker.disconnect()

      console.log('Final heartbeat stats:', broker.getHeartbeatStats())
    } catch (error) {
      console.error('Heartbeat demonstration error:', error)
    } finally {
      PocketOption.destroyInstance()
    }
  }
}

// Example of how to use in main process
if (require.main === module) {
  console.log('Running PocketOption demonstration...')

  PocketOptionExample.demonstrateUsage()
    .then(() => console.log('Demonstration completed'))
    .catch((error) => console.error('Demonstration failed:', error))
}
