/**
 * Utility types for the Signal Engine system
 * This file provides common utility types and type helpers
 */

// Basic utility types
export type Nullable<T> = T | null
export type Optional<T> = T | undefined
export type Maybe<T> = T | null | undefined

// Make all properties optional
export type Partial<T> = {
  [P in keyof T]?: T[P]
}

// Make all properties required
export type Required<T> = {
  [P in keyof T]-?: T[P]
}

// Pick specific properties
export type Pick<T, K extends keyof T> = {
  [P in K]: T[P]
}

// Omit specific properties
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>

// Make specific properties optional
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// Make specific properties required
export type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>

// Deep partial - makes all nested properties optional
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// Deep required - makes all nested properties required
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P]
}

// Extract array element type
export type ArrayElement<T> = T extends (infer U)[] ? U : never

// Extract promise type
export type PromiseType<T> = T extends Promise<infer U> ? U : never

// Extract function return type
export type ReturnType<T> = T extends (...args: any[]) => infer R ? R : never

// Extract function parameters
export type Parameters<T> = T extends (...args: infer P) => any ? P : never

// Create a union of all values in an object
export type ValueOf<T> = T[keyof T]

// Create a union of all keys in an object
export type KeyOf<T> = keyof T

// Conditional types
export type If<C extends boolean, T, F> = C extends true ? T : F

// String manipulation types
export type Uppercase<S extends string> = Intrinsic
export type Lowercase<S extends string> = Intrinsic
export type Capitalize<S extends string> = Intrinsic
export type Uncapitalize<S extends string> = Intrinsic

// Branded types for type safety
export type Brand<T, B> = T & { __brand: B }

// Common branded types
export type StrategyId = Brand<string, 'StrategyId'>
export type SignalId = Brand<string, 'SignalId'>
export type IndicatorId = Brand<string, 'IndicatorId'>
export type Timestamp = Brand<number, 'Timestamp'>
export type Percentage = Brand<number, 'Percentage'>
export type Currency = Brand<number, 'Currency'>

// Result type for operations that can fail
export type Result<T, E = Error> = 
  | { success: true; data: T; error?: never }
  | { success: false; data?: never; error: E }

// Async result type
export type AsyncResult<T, E = Error> = Promise<Result<T, E>>

// Event handler types
export type EventHandler<T = any> = (event: T) => void
export type AsyncEventHandler<T = any> = (event: T) => Promise<void>

// Callback types
export type Callback<T = void> = () => T
export type AsyncCallback<T = void> = () => Promise<T>
export type CallbackWithParam<P, T = void> = (param: P) => T
export type AsyncCallbackWithParam<P, T = void> = (param: P) => Promise<T>

// Validation types
export type Validator<T> = (value: T) => boolean
export type AsyncValidator<T> = (value: T) => Promise<boolean>
export type ValidatorWithMessage<T> = (value: T) => string | null
export type AsyncValidatorWithMessage<T> = (value: T) => Promise<string | null>

// Configuration types
export type Config<T> = {
  [K in keyof T]: T[K] extends object ? Config<T[K]> : T[K]
}

export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

// Mutable version of readonly types
export type Mutable<T> = {
  -readonly [P in keyof T]: T[P]
}

export type DeepMutable<T> = {
  -readonly [P in keyof T]: T[P] extends object ? DeepMutable<T[P]> : T[P]
}

// Tuple types
export type Head<T extends readonly any[]> = T extends readonly [infer H, ...any[]] ? H : never
export type Tail<T extends readonly any[]> = T extends readonly [any, ...infer R] ? R : never
export type Last<T extends readonly any[]> = T extends readonly [...any[], infer L] ? L : never

// Object manipulation types
export type Merge<T, U> = Omit<T, keyof U> & U

export type Override<T, U> = Omit<T, keyof U> & U

export type Intersection<T, U> = Pick<T, Extract<keyof T, keyof U>>

export type Difference<T, U> = Pick<T, Exclude<keyof T, keyof U>>

// Function composition types
export type Compose<F, G> = F extends (arg: infer A) => infer B
  ? G extends (arg: B) => infer C
    ? (arg: A) => C
    : never
  : never

// Predicate types
export type Predicate<T> = (value: T) => boolean
export type AsyncPredicate<T> = (value: T) => Promise<boolean>

// Comparator types
export type Comparator<T> = (a: T, b: T) => number
export type EqualityComparator<T> = (a: T, b: T) => boolean

// Mapper types
export type Mapper<T, U> = (value: T) => U
export type AsyncMapper<T, U> = (value: T) => Promise<U>

// Reducer types
export type Reducer<T, U> = (accumulator: U, current: T) => U
export type AsyncReducer<T, U> = (accumulator: U, current: T) => Promise<U>

// Iterator types
export type Iterator<T> = {
  next(): { value: T; done: boolean }
}

export type AsyncIterator<T> = {
  next(): Promise<{ value: T; done: boolean }>
}

// Observable types
export type Observer<T> = {
  next: (value: T) => void
  error?: (error: Error) => void
  complete?: () => void
}

export type Observable<T> = {
  subscribe(observer: Observer<T>): { unsubscribe(): void }
}

// State management types
export type State<T> = {
  readonly value: T
  setValue: (value: T | ((prev: T) => T)) => void
}

export type Store<T> = {
  getState(): T
  setState(state: Partial<T>): void
  subscribe(listener: (state: T) => void): () => void
}

// Cache types
export type CacheEntry<T> = {
  value: T
  timestamp: number
  ttl?: number
}

export type Cache<K, V> = {
  get(key: K): V | undefined
  set(key: K, value: V, ttl?: number): void
  delete(key: K): boolean
  clear(): void
  has(key: K): boolean
  size(): number
}

// Serialization types
export type Serializable = 
  | string 
  | number 
  | boolean 
  | null 
  | undefined
  | Serializable[]
  | { [key: string]: Serializable }

export type Serializer<T> = {
  serialize(value: T): string
  deserialize(data: string): T
}

// Time-related types
export type Duration = Brand<number, 'Duration'> // milliseconds
export type Interval = Brand<number, 'Interval'> // milliseconds

export type TimeUnit = 'ms' | 's' | 'm' | 'h' | 'd' | 'w' | 'M' | 'y'

export type TimeRange = {
  start: Timestamp
  end: Timestamp
}

// Measurement types
export type Metric<T = number> = {
  name: string
  value: T
  unit?: string
  timestamp: Timestamp
  tags?: Record<string, string>
}

export type Measurement = {
  timestamp: Timestamp
  value: number
  metadata?: Record<string, any>
}

// Error handling types
export type ErrorHandler = (error: Error) => void
export type AsyncErrorHandler = (error: Error) => Promise<void>

export type ErrorBoundary<T> = {
  try: () => T
  catch: (error: Error) => T
  finally?: () => void
}

export type AsyncErrorBoundary<T> = {
  try: () => Promise<T>
  catch: (error: Error) => Promise<T>
  finally?: () => Promise<void>
}

// Logging types
export type LogLevel = 'debug' | 'info' | 'warn' | 'error'

export type LogEntry = {
  level: LogLevel
  message: string
  timestamp: Timestamp
  context?: Record<string, any>
}

export type Logger = {
  debug(message: string, context?: Record<string, any>): void
  info(message: string, context?: Record<string, any>): void
  warn(message: string, context?: Record<string, any>): void
  error(message: string, context?: Record<string, any>): void
}

// Export utility functions for type checking
export const isNullable = <T>(value: T | null): value is null => value === null
export const isOptional = <T>(value: T | undefined): value is undefined => value === undefined
export const isMaybe = <T>(value: Maybe<T>): value is null | undefined => value == null

// Type guards
export const isString = (value: unknown): value is string => typeof value === 'string'
export const isNumber = (value: unknown): value is number => typeof value === 'number'
export const isBoolean = (value: unknown): value is boolean => typeof value === 'boolean'
export const isObject = (value: unknown): value is object => typeof value === 'object' && value !== null
export const isArray = <T>(value: unknown): value is T[] => Array.isArray(value)
export const isFunction = (value: unknown): value is Function => typeof value === 'function'

// Assertion functions
export const assertString = (value: unknown): asserts value is string => {
  if (!isString(value)) throw new Error('Expected string')
}

export const assertNumber = (value: unknown): asserts value is number => {
  if (!isNumber(value)) throw new Error('Expected number')
}

export const assertObject = (value: unknown): asserts value is object => {
  if (!isObject(value)) throw new Error('Expected object')
}
