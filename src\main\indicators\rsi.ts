/**
 * Relative Strength Index (RSI) Indicator
 * High-performance implementation with O(n) complexity, streaming support, and comprehensive error handling
 */

import { BaseIndicatorClass } from './BaseIndicator'
import { logger } from '../../shared/utils/logger'
import {
  INDICATOR_CONSTANTS,
  INDICATOR_LOG_CATEGORIES,
  DEFAULT_RSI_CONFIG
} from '../../shared/constants/indicators'
import type {
  RSIIndicator,
  RSIConfig,
  IndicatorDataPoint,
  OHLCDataPoint,
  IndicatorOutputPoint,
  IndicatorStreamUpdate
} from '../../shared/types/indicators'

/**
 * Relative Strength Index indicator implementation
 * Uses rolling window optimization for O(n) performance and supports real-time streaming
 * RSI = 100 - (100 / (1 + RS)), where RS = Average Gain / Average Loss
 */
export class RelativeStrengthIndex extends BaseIndicatorClass implements RSIIndicator {
  private _gains: number[] = []
  private _losses: number[] = []
  private _averageGain: number = 0
  private _averageLoss: number = 0
  private _previousPrice: number | null = null
  private _streamingEnabled: boolean = false
  private _streamUpdateCallbacks: ((update: IndicatorStreamUpdate) => void)[] = []

  /**
   * Create a new RSI indicator
   * @param config - Configuration for the RSI indicator
   */
  constructor(config: Partial<RSIConfig> = {}) {
    const mergedConfig = { ...DEFAULT_RSI_CONFIG, ...config }
    super(INDICATOR_CONSTANTS.NAMES.RSI, mergedConfig)
    this._streamingEnabled = mergedConfig.enableStreaming || false

    logger.debug(INDICATOR_LOG_CATEGORIES.CALCULATION, 'RSI indicator created', {
      config: mergedConfig
    })
  }

  /**
   * Get RSI-specific configuration
   */
  public get config(): RSIConfig {
    return this._config as RSIConfig
  }

  /**
   * Get the current RSI value without adding new data
   * @returns Current RSI value (0-100) or null if not enough data
   */
  public getCurrentRSI(): number | null {
    if (this._outputValues.length === 0) return null
    return this._outputValues[this._outputValues.length - 1].value
  }

  /**
   * Check if current RSI indicates overbought condition
   * @returns True if RSI is above overbought threshold
   */
  public isOverbought(): boolean {
    const currentRSI = this.getCurrentRSI()
    if (currentRSI === null) return false
    return currentRSI > (this.config.overboughtThreshold || 70)
  }

  /**
   * Check if current RSI indicates oversold condition
   * @returns True if RSI is below oversold threshold
   */
  public isOversold(): boolean {
    const currentRSI = this.getCurrentRSI()
    if (currentRSI === null) return false
    return currentRSI < (this.config.oversoldThreshold || 30)
  }

  /**
   * Get the current average gain
   * @returns Current average gain value
   */
  public getCurrentAverageGain(): number | null {
    return this._gains.length >= this._config.period ? this._averageGain : null
  }

  /**
   * Get the current average loss
   * @returns Current average loss value
   */
  public getCurrentAverageLoss(): number | null {
    return this._losses.length >= this._config.period ? this._averageLoss : null
  }

  /**
   * Get the current Relative Strength (RS) value
   * @returns Current RS value (Average Gain / Average Loss)
   */
  public getCurrentRS(): number | null {
    const avgGain = this.getCurrentAverageGain()
    const avgLoss = this.getCurrentAverageLoss()

    if (avgGain === null || avgLoss === null) return null
    if (avgLoss === 0) return avgGain === 0 ? 1 : Number.POSITIVE_INFINITY

    return avgGain / avgLoss
  }

  /**
   * Enable streaming updates with callback
   * @param callback - Function to call on each update
   */
  public onStreamUpdate(callback: (update: IndicatorStreamUpdate) => void): void {
    this._streamUpdateCallbacks.push(callback)
    this._streamingEnabled = true
  }

  /**
   * Disable streaming updates
   */
  public stopStreaming(): void {
    this._streamingEnabled = false
    this._streamUpdateCallbacks = []
    this._emitEvent('stream-stopped', { timestamp: Date.now() })
  }

  /**
   * Calculate RSI value for a specific index using stateless calculation
   * @param index - Index in the data points array
   * @returns Calculated RSI output point or null
   */
  protected _calculateSingleValue(index: number): IndicatorOutputPoint | null {
    if (index < 0 || index >= this._dataPoints.length) {
      return null
    }

    // Need at least period + 1 data points (one for initial price, period for changes)
    if (index < this._config.period) {
      return null
    }

    const dataPoint = this._dataPoints[index]

    // Calculate gains and losses for the period ending at this index
    const gains: number[] = []
    const losses: number[] = []

    for (let i = index - this._config.period + 1; i <= index; i++) {
      const currentPrice = this._extractPriceValue(this._dataPoints[i])
      const previousPrice = this._extractPriceValue(this._dataPoints[i - 1])

      const change = currentPrice - previousPrice
      gains.push(change > 0 ? change : 0)
      losses.push(change < 0 ? Math.abs(change) : 0)
    }

    // Calculate average gain and loss based on smoothing method
    const avgGain = this._calculateAverage(gains)
    const avgLoss = this._calculateAverage(losses)

    // Calculate RSI
    let rsiValue: number
    if (avgLoss === 0) {
      rsiValue = avgGain === 0 ? 50 : 100 // If no losses, RSI is 100 (unless no gains either)
    } else {
      const rs = avgGain / avgLoss
      rsiValue = 100 - 100 / (1 + rs)
    }

    // Ensure RSI is within valid range
    rsiValue = Math.max(0, Math.min(100, rsiValue))

    return {
      value: rsiValue,
      timestamp: dataPoint.timestamp || Date.now(),
      index
    }
  }

  /**
   * Calculate average based on configured smoothing method
   * @param values - Array of values to average
   * @returns Calculated average
   */
  private _calculateAverage(values: number[]): number {
    if (values.length === 0) return 0

    const smoothingMethod = this.config.smoothingMethod || 'sma'

    if (smoothingMethod === 'ema') {
      // Exponential Moving Average
      const alpha = 2 / (values.length + 1)
      let ema = values[0]

      for (let i = 1; i < values.length; i++) {
        ema = alpha * values[i] + (1 - alpha) * ema
      }

      return ema
    } else {
      // Simple Moving Average (default)
      return values.reduce((sum, value) => sum + value, 0) / values.length
    }
  }

  /**
   * Extract price value based on configured price type
   * @param dataPoint - Input data point
   * @returns Extracted price value
   */
  private _extractPriceValue(dataPoint: IndicatorDataPoint): number {
    const priceType = this.config.priceType || 'close'
    const ohlcData = dataPoint as OHLCDataPoint

    switch (priceType) {
      case 'close': {
        return dataPoint.value
      }
      case 'open': {
        return ohlcData.open || dataPoint.value
      }
      case 'high': {
        return ohlcData.high || dataPoint.value
      }
      case 'low': {
        return ohlcData.low || dataPoint.value
      }
      case 'typical': {
        const high = ohlcData.high || dataPoint.value
        const low = ohlcData.low || dataPoint.value
        const close = dataPoint.value
        return (high + low + close) / 3
      }
      case 'weighted': {
        const wHigh = ohlcData.high || dataPoint.value
        const wLow = ohlcData.low || dataPoint.value
        const wClose = dataPoint.value
        return (wHigh + wLow + 2 * wClose) / 4
      }
      default: {
        return dataPoint.value
      }
    }
  }

  /**
   * Update rolling averages using optimized calculation
   */
  private _updateRollingAverages(): void {
    const smoothingMethod = this.config.smoothingMethod || 'sma'

    if (smoothingMethod === 'ema') {
      // Use EMA for rolling calculation
      const alpha = 2 / (this._config.period + 1)

      if (this._averageGain === 0 && this._averageLoss === 0) {
        // First calculation - use SMA
        this._averageGain = this._calculateAverage(this._gains)
        this._averageLoss = this._calculateAverage(this._losses)
      } else {
        // Subsequent calculations - use EMA
        const currentGain = this._gains[this._gains.length - 1]
        const currentLoss = this._losses[this._losses.length - 1]

        this._averageGain = alpha * currentGain + (1 - alpha) * this._averageGain
        this._averageLoss = alpha * currentLoss + (1 - alpha) * this._averageLoss
      }
    } else {
      // Use SMA for rolling calculation
      this._averageGain = this._calculateAverage(this._gains)
      this._averageLoss = this._calculateAverage(this._losses)
    }
  }

  /**
   * Calculate optimized RSI using rolling averages
   * @param dataPoint - Current data point
   * @returns Calculated RSI output point
   */
  private _calculateOptimizedRSI(dataPoint: IndicatorDataPoint): IndicatorOutputPoint {
    // Calculate RSI using current rolling averages
    let rsiValue: number
    if (this._averageLoss === 0) {
      rsiValue = this._averageGain === 0 ? 50 : 100
    } else {
      const rs = this._averageGain / this._averageLoss
      rsiValue = 100 - 100 / (1 + rs)
    }

    // Ensure RSI is within valid range
    rsiValue = Math.max(0, Math.min(100, rsiValue))

    return {
      value: rsiValue,
      timestamp: dataPoint.timestamp || Date.now(),
      index: this._dataPoints.length - 1
    }
  }

  /**
   * Override addData to implement optimized rolling calculation
   * @param dataPoint - The data point to add
   * @returns The calculated RSI value or null if not enough data
   */
  public addData(dataPoint: IndicatorDataPoint): IndicatorOutputPoint | null {
    const startTime = performance.now()

    try {
      // Validate input if enabled
      if (this._config.validateInputs) {
        const validation = this.validateInput([dataPoint])
        if (!validation.isValid) {
          const error = `Invalid data point: ${validation.errors.join(', ')}`
          this._emitEvent('error-occurred', { error, dataPoint })
          throw new Error(error)
        }
      }

      // Process the data point (add timestamp if missing)
      const processedDataPoint: IndicatorDataPoint = {
        ...dataPoint,
        timestamp: dataPoint.timestamp || Date.now()
      }
      const currentPrice = this._extractPriceValue(processedDataPoint)

      // Add to data points
      this._dataPoints.push(processedDataPoint)
      this._operationCount++

      // Calculate gain/loss if we have a previous price
      if (this._previousPrice !== null) {
        const change = currentPrice - this._previousPrice
        const gain = change > 0 ? change : 0
        const loss = change < 0 ? Math.abs(change) : 0

        this._gains.push(gain)
        this._losses.push(loss)

        // Maintain rolling window
        if (this._gains.length > this._config.period) {
          this._gains.shift()
          this._losses.shift()
        }

        // Update rolling averages using optimized calculation
        if (this._gains.length === this._config.period) {
          this._updateRollingAverages()
        }
      }

      this._previousPrice = currentPrice

      // Manage memory
      this._manageMemory()

      // Calculate new value if we have enough data
      let outputValue: IndicatorOutputPoint | null = null
      if (this._dataPoints.length > this._config.period) {
        outputValue = this._calculateOptimizedRSI(processedDataPoint)
        if (outputValue) {
          this._outputValues.push(outputValue)
          this._isReady = true
          this._emitEvent('value-calculated', { outputValue, dataPoint: processedDataPoint })

          // Handle streaming updates
          if (this._streamingEnabled && this._streamUpdateCallbacks.length > 0) {
            const streamUpdate: IndicatorStreamUpdate = {
              dataPoint: processedDataPoint,
              indicatorValue: outputValue,
              isNewValue: true,
              updateTimestamp: Date.now()
            }

            this._streamUpdateCallbacks.forEach((callback) => {
              try {
                callback(streamUpdate)
              } catch (error) {
                logger.error(INDICATOR_LOG_CATEGORIES.STREAMING, 'RSI stream callback failed', {
                  error
                })
              }
            })
          }
        }
      }

      // Emit data-added event
      this._emitEvent('data-added', { dataPoint: processedDataPoint })

      // Update performance metrics
      this._lastCalculationTime = performance.now() - startTime

      return outputValue
    } catch (error) {
      logger.error(INDICATOR_LOG_CATEGORIES.ERROR, 'RSI calculation failed', { error, dataPoint })
      this._emitEvent('error-occurred', { error, dataPoint })
      return null
    }
  }

  /**
   * Override memory management to handle RSI-specific state
   */
  protected _manageMemory(): void {
    if (!INDICATOR_CONSTANTS.MEMORY_MANAGEMENT.AUTO_CLEANUP) return

    const maxHistory =
      this._config.maxHistorySize || INDICATOR_CONSTANTS.MEMORY_MANAGEMENT.MAX_RETAINED_HISTORY

    // Trim data points if exceeding max history
    if (this._dataPoints.length > maxHistory) {
      const excessCount = this._dataPoints.length - maxHistory
      this._dataPoints.splice(0, excessCount)

      // Also trim output values proportionally
      if (this._outputValues.length > maxHistory - this._config.period) {
        const outputExcess = this._outputValues.length - (maxHistory - this._config.period)
        this._outputValues.splice(0, outputExcess)
      }

      // Trim gains and losses arrays but keep at least period length
      const keepGainLoss = Math.min(this._config.period, this._gains.length)
      if (this._gains.length > keepGainLoss) {
        this._gains.splice(0, this._gains.length - keepGainLoss)
        this._losses.splice(0, this._losses.length - keepGainLoss)
      }

      logger.debug(INDICATOR_LOG_CATEGORIES.PERFORMANCE, `${this._name} memory cleanup completed`, {
        removedDataPoints: excessCount,
        remainingDataPoints: this._dataPoints.length,
        remainingOutputs: this._outputValues.length
      })
    }
  }
}

/**
 * Factory function to create a new RSI indicator
 * @param config - Configuration for the RSI indicator
 * @returns New RSI indicator instance
 */
export const createRSI = (config: Partial<RSIConfig> = {}): RelativeStrengthIndex => {
  return new RelativeStrengthIndex(config)
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use RelativeStrengthIndex class or createRSI factory function instead
 * @param prices - Array of price values
 * @param period - Period for RSI calculation (default: 14)
 * @param smoothingMethod - Smoothing method ('sma' or 'ema', default: 'sma')
 * @returns Array of RSI values
 */
export const calculateRSI = (
  prices: number[],
  period: number = 14,
  smoothingMethod: 'sma' | 'ema' = 'sma'
): number[] => {
  logger.warn(
    INDICATOR_LOG_CATEGORIES.CALCULATION,
    'Using deprecated calculateRSI function. Consider using RelativeStrengthIndex class for better performance.'
  )

  if (prices.length <= period) return []

  const rsi = createRSI({ period, smoothingMethod })
  const dataPoints: IndicatorDataPoint[] = prices.map((price, index) => ({
    value: price,
    timestamp: Date.now() + index
  }))

  rsi.addDataBatch(dataPoints)
  return rsi.getValues().map((point) => point.value)
}
