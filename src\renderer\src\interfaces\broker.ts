/**
 * Broker Service Interfaces
 * TypeScript interfaces for broker service operations and IPC communication
 */

import type { TradingBotConfig } from '../../../shared/types/trading'
import type { HeartbeatStats } from '../../../shared/types/broker'

/**
 * Generic response interface for IPC operations between renderer and main process
 * @template T - Type of the data payload
 */
export interface IPCResponse<T = unknown> {
  /** Whether the operation was successful */
  success: boolean
  /** Success message (optional) */
  message?: string
  /** Error message if operation failed */
  error?: string
  /** Response data payload */
  data?: T
  /** Trading bot configuration (for specific operations) */
  config?: TradingBotConfig
}

/**
 * Comprehensive broker status data interface
 * Contains all information about broker connection and bot state
 */
export interface BrokerStatusData {
  /** Whether the broker is currently connected */
  isConnected: boolean
  /** Current connection state string representation */
  connectionState: string
  /** Current heartbeat health status */
  heartbeatHealth: string
  /** Whether the trading bot is currently active */
  isBotActive: boolean
  /** Detailed heartbeat statistics */
  heartbeatStats: HeartbeatStats
}

/**
 * Event handler functions interface for broker events
 * Defines all possible event handlers that can be registered
 */
export interface BrokerEventHandlers {
  /** Called when broker successfully connects */
  onConnected?: () => void
  /** Called when broker disconnects */
  onDisconnected?: () => void
  /** Called when an error occurs */
  onError?: (error: string) => void
  /** Called when account balance is updated */
  onBalanceUpdate?: (balance: number) => void
  /** Called when trading bot starts */
  onBotStarted?: (config: TradingBotConfig) => void
  /** Called when trading bot stops */
  onBotStopped?: () => void
  /** Called when connection state changes */
  onStateChange?: (data: ConnectionStateChangeData) => void
  /** Called when heartbeat health changes */
  onHeartbeatHealthChange?: (data: HeartbeatHealthChangeData) => void
  /** Called when heartbeat is sent */
  onHeartbeatSent?: (data: HeartbeatSentData) => void
  /** Called when heartbeat is received */
  onHeartbeatReceived?: (data: HeartbeatReceivedData) => void
  /** Called when heartbeat fails */
  onHeartbeatFailed?: (data: HeartbeatFailedData) => void
}

/**
 * Connection state change event data
 */
export interface ConnectionStateChangeData {
  /** Previous connection state */
  from: string
  /** New connection state */
  to: string
  /** Timestamp of the change */
  timestamp: number
}

/**
 * Heartbeat health change event data
 */
export interface HeartbeatHealthChangeData {
  /** Previous health status */
  from: string
  /** New health status */
  to: string
  /** Current number of missed heartbeats */
  missedBeats: number
}

/**
 * Heartbeat sent event data
 */
export interface HeartbeatSentData {
  /** Timestamp when heartbeat was sent */
  timestamp: number
  /** Current number of missed heartbeats */
  missedBeats: number
}

/**
 * Heartbeat received event data
 */
export interface HeartbeatReceivedData {
  /** Timestamp when heartbeat was received */
  timestamp: number
  /** Response time in milliseconds */
  responseTime: number
  /** Current number of missed heartbeats */
  missedBeats: number
}

/**
 * Heartbeat failed event data
 */
export interface HeartbeatFailedData {
  /** Current number of missed heartbeats */
  missedBeats: number
  /** Maximum allowed missed heartbeats before failure */
  maxMissedBeats: number
}

/**
 * Heartbeat configuration interface
 */
export interface HeartbeatConfig {
  /** Interval between heartbeats in milliseconds (default: 30000) */
  interval: number
  /** Timeout for heartbeat response in milliseconds (default: 10000) */
  timeout: number
  /** Maximum missed heartbeats before considering connection failed (default: 3) */
  maxMissedBeats: number
  /** Whether heartbeat is currently enabled */
  enabled: boolean
}

/**
 * Broker service configuration interface
 */
export interface BrokerServiceConfig {
  /** Heartbeat configuration */
  heartbeat: HeartbeatConfig
  /** Whether to enable automatic reconnection */
  autoReconnect: boolean
  /** Maximum number of reconnection attempts */
  maxReconnectAttempts: number
  /** Base delay between reconnection attempts in milliseconds */
  reconnectDelay: number
}

/**
 * Event listener cleanup function type
 */
export type EventListenerCleanup = () => void

/**
 * Event listener registration result
 */
export interface EventListenerRegistration {
  /** Cleanup function to remove the event listener */
  cleanup: EventListenerCleanup
  /** Whether the listener was successfully registered */
  success: boolean
  /** Error message if registration failed */
  error?: string
}
