/**
 * Enhanced Broker Service for Renderer Process
 * Provides comprehensive broker operations with heartbeat monitoring, automatic reconnection,
 * and event-driven architecture for real-time communication with the main process
 *
 * @example
 * ```typescript
 * // Get singleton instance
 * const broker = BrokerService.getInstance()
 *
 * // Set up event listeners
 * const cleanup = broker.setupEventListeners({
 *   onConnected: () => console.log('Broker connected'),
 *   onBalanceUpdate: (balance) => console.log('Balance:', balance)
 * })
 *
 * // Start trading bot
 * const result = await broker.startTradingBot({
 *   tradeCapital: 1000,
 *   targetProfit: 100,
 *   tradeAmount: 10,
 *   tradeExpiryDuration: 'M1'
 * })
 * ```
 */

import type { TradingBotConfig } from '../../../shared/types/trading'
import type {
  IPCResponse,
  BrokerStatusData,
  BrokerEventHandlers,
  BrokerServiceConfig,
  EventListenerCleanup
} from '../interfaces/broker'
import { HeartbeatManager } from './heartbeatManager'
import {
  isIPCResponse,
  isBrokerStatusData,
  validateTradingBotConfig,
  validateEventHandlers,
  sanitizeError,
  ValidationError
} from '../utils/validation'
import {
  IPC_CHANNELS,
  BROKER_EVENTS,
  ERROR_MESSAGES,
  DEFAULT_BROKER_CONFIG,
  LOG_PREFIXES,
  PERFORMANCE
} from '../constants/brokerService'

/**
 * Enhanced Broker Service Class
 * Provides comprehensive broker operations with heartbeat monitoring, automatic reconnection,
 * and event-driven architecture for real-time communication with PocketOption broker
 *
 * Features:
 * - Singleton design pattern for consistent state management
 * - Comprehensive heartbeat mechanism with configurable intervals
 * - Automatic reconnection on connection failures
 * - Event-driven architecture with proper cleanup
 * - Enhanced error handling and validation
 * - Type-safe operations with comprehensive TypeScript support
 */
export class BrokerService {
  private static instance: BrokerService | null = null
  private heartbeatManager: HeartbeatManager
  private config: BrokerServiceConfig
  private activeEventListeners: Map<string, () => void> = new Map()
  private isInitialized = false

  /**
   * Private constructor to enforce singleton pattern
   * Initializes the service with default configuration and heartbeat manager
   */
  private constructor() {
    this.config = { ...DEFAULT_BROKER_CONFIG }
    this.heartbeatManager = new HeartbeatManager(
      this.config.heartbeat,
      this.config.maxReconnectAttempts,
      this.config.reconnectDelay
    )
    this.initialize()
  }

  /**
   * Gets the singleton instance of BrokerService
   * Ensures only one instance exists throughout the application lifecycle
   *
   * @returns The singleton BrokerService instance
   *
   * @example
   * ```typescript
   * const brokerService = BrokerService.getInstance()
   * ```
   */
  public static getInstance(): BrokerService {
    if (!BrokerService.instance) {
      BrokerService.instance = new BrokerService()
    }
    return BrokerService.instance
  }

  /**
   * Initializes the broker service
   * Sets up internal event listeners and prepares the service for operation
   */
  private initialize(): void {
    if (this.isInitialized) {
      return
    }

    // Set up internal event listeners for heartbeat management
    this.setupInternalEventListeners()
    this.isInitialized = true

    console.info(`${LOG_PREFIXES.BROKER_SERVICE} Initialized successfully`)
  }

  /**
   * Starts the trading bot with comprehensive validation and error handling
   *
   * @param config - Trading bot configuration object
   * @param config.tradeCapital - Total capital available for trading (must be positive)
   * @param config.targetProfit - Desired profit target (must be positive)
   * @param config.tradeAmount - Amount per individual trade (must be positive and <= tradeCapital)
   * @param config.tradeExpiryDuration - Duration for trades (e.g., 'M1', 'M5', 'H1')
   *
   * @returns Promise resolving to operation result with success status and optional data
   *
   * @throws {ValidationError} When configuration is invalid
   *
   * @example
   * ```typescript
   * const result = await brokerService.startTradingBot({
   *   tradeCapital: 1000,
   *   targetProfit: 100,
   *   tradeAmount: 10,
   *   tradeExpiryDuration: 'M1'
   * })
   *
   * if (result.success) {
   *   console.log('Trading bot started successfully')
   * } else {
   *   console.error('Failed to start:', result.error)
   * }
   * ```
   */
  public async startTradingBot(config: TradingBotConfig): Promise<IPCResponse> {
    const methodName = 'startTradingBot'
    console.info(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} called`)

    try {
      // Comprehensive validation
      validateTradingBotConfig(config)

      const response = await this.invokeWithTimeout<IPCResponse>(
        IPC_CHANNELS.TRADING_BOT_START,
        config,
        PERFORMANCE.IPC_RESPONSE_TIMEOUT
      )

      if (!isIPCResponse(response)) {
        throw new ValidationError(ERROR_MESSAGES.INVALID_RESPONSE)
      }

      if (!response.success) {
        throw new Error(response.error || ERROR_MESSAGES.START_FAILED)
      }

      // Start heartbeat monitoring when bot starts
      this.startHeartbeatMonitoring()

      console.info(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} completed successfully`)
      return response
    } catch (error) {
      const errorMessage = sanitizeError(error)
      console.error(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} failed:`, errorMessage)

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Stops the trading bot with proper cleanup and validation
   *
   * @returns Promise resolving to operation result with success status
   *
   * @example
   * ```typescript
   * const result = await brokerService.stopTradingBot()
   *
   * if (result.success) {
   *   console.log('Trading bot stopped successfully')
   * } else {
   *   console.error('Failed to stop:', result.error)
   * }
   * ```
   */
  public async stopTradingBot(): Promise<IPCResponse> {
    const methodName = 'stopTradingBot'
    console.info(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} called`)

    try {
      const response = await this.invokeWithTimeout<IPCResponse>(
        IPC_CHANNELS.TRADING_BOT_STOP,
        undefined,
        PERFORMANCE.IPC_RESPONSE_TIMEOUT
      )

      if (!isIPCResponse(response)) {
        throw new ValidationError(ERROR_MESSAGES.INVALID_RESPONSE)
      }

      if (!response.success) {
        throw new Error(response.error || ERROR_MESSAGES.STOP_FAILED)
      }

      // Stop heartbeat monitoring when bot stops
      this.stopHeartbeatMonitoring()

      console.info(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} completed successfully`)
      return response
    } catch (error) {
      const errorMessage = sanitizeError(error)
      console.error(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} failed:`, errorMessage)

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Gets the current broker and trading bot status with comprehensive validation
   *
   * @returns Promise resolving to status data including connection state, heartbeat health, and bot status
   *
   * @example
   * ```typescript
   * const statusResult = await brokerService.getBrokerStatus()
   *
   * if (statusResult.success && statusResult.data) {
   *   const { isConnected, connectionState, heartbeatHealth, isBotActive } = statusResult.data
   *   console.log('Broker Status:', { isConnected, connectionState, heartbeatHealth, isBotActive })
   * }
   * ```
   */
  public async getBrokerStatus(): Promise<IPCResponse<BrokerStatusData>> {
    const methodName = 'getBrokerStatus'
    console.info(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} called`)

    try {
      const response = await this.invokeWithTimeout<IPCResponse<BrokerStatusData>>(
        IPC_CHANNELS.TRADING_BOT_STATUS,
        undefined,
        PERFORMANCE.IPC_RESPONSE_TIMEOUT
      )

      if (!isIPCResponse<BrokerStatusData>(response)) {
        throw new ValidationError(ERROR_MESSAGES.INVALID_RESPONSE)
      }

      if (!response.success) {
        throw new Error(response.error || ERROR_MESSAGES.STATUS_FAILED)
      }

      // Validate the data structure if present
      if (response.data && !isBrokerStatusData(response.data)) {
        throw new ValidationError(ERROR_MESSAGES.INVALID_STATUS_DATA)
      }

      console.info(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} completed successfully`)
      return response
    } catch (error) {
      const errorMessage = sanitizeError(error)
      console.error(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} failed:`, errorMessage)

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Connects the broker to PocketOption with enhanced error handling and heartbeat initialization
   *
   * @returns Promise resolving to operation result with success status
   *
   * @example
   * ```typescript
   * const result = await brokerService.connectBroker()
   *
   * if (result.success) {
   *   console.log('Broker connected successfully')
   * } else {
   *   console.error('Connection failed:', result.error)
   * }
   * ```
   */
  public async connectBroker(): Promise<IPCResponse> {
    const methodName = 'connectBroker'
    console.info(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} called`)

    try {
      const response = await this.invokeWithTimeout<IPCResponse>(
        IPC_CHANNELS.BROKER_CONNECT,
        undefined,
        PERFORMANCE.IPC_RESPONSE_TIMEOUT
      )

      if (!isIPCResponse(response)) {
        throw new ValidationError(ERROR_MESSAGES.INVALID_RESPONSE)
      }

      if (!response.success) {
        throw new Error(response.error || ERROR_MESSAGES.CONNECT_FAILED)
      }

      // Start heartbeat monitoring on successful connection
      this.startHeartbeatMonitoring()

      console.info(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} completed successfully`)
      return response
    } catch (error) {
      const errorMessage = sanitizeError(error)
      console.error(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} failed:`, errorMessage)

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Disconnects the broker from PocketOption with proper cleanup
   *
   * @returns Promise resolving to operation result with success status
   *
   * @example
   * ```typescript
   * const result = await brokerService.disconnectBroker()
   *
   * if (result.success) {
   *   console.log('Broker disconnected successfully')
   * } else {
   *   console.error('Disconnection failed:', result.error)
   * }
   * ```
   */
  public async disconnectBroker(): Promise<IPCResponse> {
    const methodName = 'disconnectBroker'
    console.info(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} called`)

    try {
      // Stop heartbeat monitoring before disconnecting
      this.stopHeartbeatMonitoring()

      const response = await this.invokeWithTimeout<IPCResponse>(
        IPC_CHANNELS.BROKER_DISCONNECT,
        undefined,
        PERFORMANCE.IPC_RESPONSE_TIMEOUT
      )

      if (!isIPCResponse(response)) {
        throw new ValidationError(ERROR_MESSAGES.INVALID_RESPONSE)
      }

      if (!response.success) {
        throw new Error(response.error || ERROR_MESSAGES.DISCONNECT_FAILED)
      }

      console.info(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} completed successfully`)
      return response
    } catch (error) {
      const errorMessage = sanitizeError(error)
      console.error(`${LOG_PREFIXES.BROKER_SERVICE} ${methodName} failed:`, errorMessage)

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Sets up comprehensive event listeners for broker events with proper type safety
   *
   * @param eventHandlers - Object containing event handler functions with proper typing
   * @returns Cleanup function to remove all registered event listeners
   *
   * @example
   * ```typescript
   * const cleanup = brokerService.setupEventListeners({
   *   onConnected: () => console.log('Broker connected'),
   *   onDisconnected: () => console.log('Broker disconnected'),
   *   onBalanceUpdate: (balance) => console.log('Balance updated:', balance),
   *   onError: (error) => console.error('Broker error:', error)
   * })
   *
   * // Later, when component unmounts or service is no longer needed
   * cleanup()
   * ```
   */
  public setupEventListeners(eventHandlers: BrokerEventHandlers): EventListenerCleanup {
    try {
      validateEventHandlers(eventHandlers)
    } catch (error) {
      console.error(`${LOG_PREFIXES.EVENTS} Invalid event handlers:`, sanitizeError(error))
      return () => {} // Return empty cleanup function
    }

    const unsubscribeFunctions: (() => void)[] = []

    // Set up broker event listeners with proper type handling
    if (eventHandlers.onConnected) {
      const unsubscribe = window.api.on(IPC_CHANNELS.BROKER_EVENT, (...args: unknown[]) => {
        const [event] = args
        if (event === BROKER_EVENTS.CONNECTED && eventHandlers.onConnected) {
          eventHandlers.onConnected()
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onDisconnected) {
      const unsubscribe = window.api.on(IPC_CHANNELS.BROKER_EVENT, (...args: unknown[]) => {
        const [event] = args
        if (event === BROKER_EVENTS.DISCONNECTED && eventHandlers.onDisconnected) {
          eventHandlers.onDisconnected()
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onError) {
      const unsubscribe = window.api.on(IPC_CHANNELS.BROKER_EVENT, (...args: unknown[]) => {
        const [event, data] = args
        if (
          event === BROKER_EVENTS.ERROR &&
          eventHandlers.onError &&
          typeof data === 'object' &&
          data !== null &&
          'error' in data
        ) {
          const errorData = data as { error?: string }
          if (errorData.error) {
            eventHandlers.onError(errorData.error)
          }
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onBalanceUpdate) {
      const unsubscribe = window.api.on(IPC_CHANNELS.BROKER_EVENT, (...args: unknown[]) => {
        const [event, balance] = args
        if (
          event === BROKER_EVENTS.BALANCE &&
          eventHandlers.onBalanceUpdate &&
          typeof balance === 'number'
        ) {
          eventHandlers.onBalanceUpdate(balance)
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onBotStarted) {
      const unsubscribe = window.api.on(IPC_CHANNELS.BROKER_EVENT, (...args: unknown[]) => {
        const [event, data] = args
        if (event === BROKER_EVENTS.BOT_STARTED && eventHandlers.onBotStarted) {
          eventHandlers.onBotStarted(data as TradingBotConfig)
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onBotStopped) {
      const unsubscribe = window.api.on(IPC_CHANNELS.BROKER_EVENT, (...args: unknown[]) => {
        const [event] = args
        if (event === BROKER_EVENTS.BOT_STOPPED && eventHandlers.onBotStopped) {
          eventHandlers.onBotStopped()
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    // Set up additional event handlers for heartbeat events
    this.setupHeartbeatEventListeners(eventHandlers, unsubscribeFunctions)

    console.info(`${LOG_PREFIXES.EVENTS} Event listeners registered successfully`)

    // Return cleanup function
    return () => {
      unsubscribeFunctions.forEach((unsubscribe) => unsubscribe())
      console.info(`${LOG_PREFIXES.EVENTS} Event listeners cleaned up`)
    }
  }

  /**
   * Helper method to invoke IPC with timeout
   * @param channel - IPC channel name
   * @param data - Data to send
   * @param timeout - Timeout in milliseconds
   * @returns Promise resolving to response
   */
  private async invokeWithTimeout<T>(
    channel: string,
    data?: unknown,
    timeout: number = PERFORMANCE.IPC_RESPONSE_TIMEOUT
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`IPC call to ${channel} timed out after ${timeout}ms`))
      }, timeout)

      window.api
        .invoke<T>(channel, data)
        .then((response) => {
          clearTimeout(timeoutId)
          resolve(response)
        })
        .catch((error) => {
          clearTimeout(timeoutId)
          reject(error)
        })
    })
  }

  /**
   * Starts heartbeat monitoring
   */
  private startHeartbeatMonitoring(): void {
    if (!this.config.heartbeat.enabled) {
      return
    }

    this.heartbeatManager.start({
      onHeartbeatHealthChange: (data) => {
        console.info(`${LOG_PREFIXES.HEARTBEAT} Health changed from ${data.from} to ${data.to}`)
      },
      onHeartbeatFailed: (data) => {
        console.warn(
          `${LOG_PREFIXES.HEARTBEAT} Failed - missed beats: ${data.missedBeats}/${data.maxMissedBeats}`
        )
      }
    })
  }

  /**
   * Stops heartbeat monitoring
   */
  private stopHeartbeatMonitoring(): void {
    this.heartbeatManager.stop()
  }

  /**
   * Sets up additional event listeners for heartbeat events
   * @param eventHandlers - Event handlers object
   * @param unsubscribeFunctions - Array to store cleanup functions
   */
  private setupHeartbeatEventListeners(
    eventHandlers: BrokerEventHandlers,
    unsubscribeFunctions: (() => void)[]
  ): void {
    // Set up heartbeat-specific event listeners
    if (eventHandlers.onHeartbeatHealthChange) {
      const unsubscribe = window.api.on(IPC_CHANNELS.BROKER_EVENT, (...args: unknown[]) => {
        const [event, data] = args
        if (
          event === BROKER_EVENTS.HEARTBEAT_HEALTH_CHANGE &&
          eventHandlers.onHeartbeatHealthChange
        ) {
          eventHandlers.onHeartbeatHealthChange(data as HeartbeatHealthChangeData)
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onHeartbeatSent) {
      const unsubscribe = window.api.on(IPC_CHANNELS.BROKER_EVENT, (...args: unknown[]) => {
        const [event, data] = args
        if (event === BROKER_EVENTS.HEARTBEAT_SENT && eventHandlers.onHeartbeatSent) {
          eventHandlers.onHeartbeatSent(data as HeartbeatSentData)
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onHeartbeatReceived) {
      const unsubscribe = window.api.on(IPC_CHANNELS.BROKER_EVENT, (...args: unknown[]) => {
        const [event, data] = args
        if (event === BROKER_EVENTS.HEARTBEAT_RECEIVED && eventHandlers.onHeartbeatReceived) {
          eventHandlers.onHeartbeatReceived(data as HeartbeatReceivedData)
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onHeartbeatFailed) {
      const unsubscribe = window.api.on(IPC_CHANNELS.BROKER_EVENT, (...args: unknown[]) => {
        const [event, data] = args
        if (event === BROKER_EVENTS.HEARTBEAT_FAILED && eventHandlers.onHeartbeatFailed) {
          eventHandlers.onHeartbeatFailed(data as HeartbeatFailedData)
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onStateChange) {
      const unsubscribe = window.api.on(IPC_CHANNELS.BROKER_EVENT, (...args: unknown[]) => {
        const [event, data] = args
        if (event === BROKER_EVENTS.STATE_CHANGE && eventHandlers.onStateChange) {
          eventHandlers.onStateChange(data as ConnectionStateChangeData)
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }
  }

  /**
   * Sets up internal event listeners for heartbeat management
   */
  private setupInternalEventListeners(): void {
    // Listen for heartbeat responses from main process
    const heartbeatListener = window.api.on('broker:heartbeat-response', (...args: unknown[]) => {
      const [responseTime] = args
      if (typeof responseTime === 'number') {
        this.heartbeatManager.recordHeartbeatReceived(responseTime)
      }
    })

    this.activeEventListeners.set('heartbeat-response', heartbeatListener)
  }

  /**
   * Updates broker service configuration
   * @param newConfig - New configuration values
   */
  public updateConfig(newConfig: Partial<BrokerServiceConfig>): void {
    this.config = { ...this.config, ...newConfig }

    if (newConfig.heartbeat) {
      this.heartbeatManager.updateConfig(newConfig.heartbeat)
    }
  }

  /**
   * Gets current broker service configuration
   * @returns Current configuration
   */
  public getConfig(): BrokerServiceConfig {
    return { ...this.config }
  }

  /**
   * Gets current heartbeat statistics
   * @returns Heartbeat statistics including health status, missed beats, and timing information
   */
  public getHeartbeatStats(): ReturnType<HeartbeatManager['getStats']> {
    return this.heartbeatManager.getStats()
  }

  /**
   * Cleanup method for proper resource management
   * Should be called when the service is no longer needed
   */
  public cleanup(): void {
    this.stopHeartbeatMonitoring()

    // Clean up all active event listeners
    this.activeEventListeners.forEach((cleanup) => cleanup())
    this.activeEventListeners.clear()

    console.info(`${LOG_PREFIXES.BROKER_SERVICE} Cleanup completed`)
  }
}

// Export singleton instance
export const brokerService = BrokerService.getInstance()
