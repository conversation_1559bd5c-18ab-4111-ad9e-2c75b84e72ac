/**
 * Signal Engine and Strategy Factory Examples
 * 
 * This file contains comprehensive examples demonstrating how to use the Signal Engine
 * and Strategy Factory for various trading scenarios.
 */

import { SignalEngine, StrategyFactory, SignalEngineUtils, StrategyPresets } from './index'
import type { GeneratedSignal, Strategy } from '../../shared/types/signals'
import type { IndicatorDataPoint } from '../../shared/types/indicators'

/**
 * Example 1: Basic RSI Strategy
 * Creates a simple RSI strategy and processes market data
 */
export async function basicRSIExample(): Promise<void> {
  console.log('=== Basic RSI Strategy Example ===')
  
  // Get SignalEngine instance
  const signalEngine = SignalEngine.getInstance()
  
  // Create RSI strategy
  const rsiStrategy = signalEngine.createStrategy('rsi', {
    name: 'basic_rsi_strategy',
    description: 'Basic RSI strategy for oversold/overbought detection',
    period: 14,
    overbought: 70,
    oversold: 30,
    minConfidence: 0.6
  })
  
  console.log(`Created strategy: ${rsiStrategy.name}`)
  console.log(`Strategy ready: ${rsiStrategy.isReady}`)
  
  // Simulate market data
  const marketData: IndicatorDataPoint[] = [
    { value: 100, timestamp: Date.now() - 5000 },
    { value: 95, timestamp: Date.now() - 4000 },
    { value: 90, timestamp: Date.now() - 3000 },
    { value: 85, timestamp: Date.now() - 2000 },
    { value: 25, timestamp: Date.now() - 1000 }, // Oversold condition
    { value: 30, timestamp: Date.now() }
  ]
  
  // Process market data
  for (const data of marketData) {
    const signals = signalEngine.processMarketData(data)
    if (signals.length > 0) {
      signals.forEach(signal => {
        console.log(`Signal: ${SignalEngineUtils.formatSignal(signal)}`)
      })
    }
  }
  
  // Get strategy performance
  const performance = rsiStrategy.getPerformanceMetrics()
  console.log('Strategy Performance:', {
    totalSignals: performance.totalSignals,
    buySignals: performance.buySignals,
    sellSignals: performance.sellSignals,
    averageConfidence: performance.averageConfidence.toFixed(2)
  })
}

/**
 * Example 2: Multi-Indicator Strategy
 * Creates a strategy combining RSI and SMA with AND logic
 */
export async function multiIndicatorExample(): Promise<void> {
  console.log('\n=== Multi-Indicator Strategy Example ===')
  
  const signalEngine = SignalEngine.getInstance()
  
  // Create combined RSI + SMA strategy
  const combinedStrategy = signalEngine.createStrategy(['rsi', 'sma'], {
    name: 'rsi_sma_combined',
    description: 'RSI oversold with SMA trend confirmation',
    rsi: {
      period: 14,
      overbought: 70,
      oversold: 30
    },
    sma: {
      period: 20
    },
    combinationLogic: 'AND', // Both indicators must agree
    minConfidence: 0.7
  })
  
  console.log(`Created combined strategy: ${combinedStrategy.name}`)
  
  // Simulate trending down market with oversold RSI
  const trendingData: IndicatorDataPoint[] = [
    { value: 120, timestamp: Date.now() - 10000 },
    { value: 115, timestamp: Date.now() - 9000 },
    { value: 110, timestamp: Date.now() - 8000 },
    { value: 105, timestamp: Date.now() - 7000 },
    { value: 100, timestamp: Date.now() - 6000 },
    { value: 95, timestamp: Date.now() - 5000 },
    { value: 90, timestamp: Date.now() - 4000 },
    { value: 85, timestamp: Date.now() - 3000 },
    { value: 80, timestamp: Date.now() - 2000 },
    { value: 75, timestamp: Date.now() - 1000 },
    { value: 25, timestamp: Date.now() } // Strong oversold
  ]
  
  // Process data and look for signals
  for (const data of trendingData) {
    const signals = signalEngine.processMarketData(data)
    if (signals.length > 0) {
      signals.forEach(signal => {
        console.log(`Combined Signal: ${SignalEngineUtils.formatSignal(signal)}`)
        console.log(`Contributing indicators:`, signal.indicators.map(ind => 
          `${ind.name}: ${ind.signal} (${(ind.confidence * 100).toFixed(1)}%)`
        ).join(', '))
      })
    }
  }
}

/**
 * Example 3: Weighted Strategy
 * Creates a strategy with weighted indicator contributions
 */
export async function weightedStrategyExample(): Promise<void> {
  console.log('\n=== Weighted Strategy Example ===')
  
  const signalEngine = SignalEngine.getInstance()
  
  // Create weighted strategy (RSI has more influence)
  const weightedStrategy = signalEngine.createStrategy(['rsi', 'sma'], {
    name: 'weighted_rsi_sma',
    description: 'RSI-weighted strategy with SMA confirmation',
    rsi: { period: 14 },
    sma: { period: 20 },
    combinationLogic: 'WEIGHTED',
    weights: [
      { indicator: 'rsi', weight: 0.7 }, // RSI has 70% influence
      { indicator: 'sma', weight: 0.3 }  // SMA has 30% influence
    ]
  })
  
  console.log(`Created weighted strategy: ${weightedStrategy.name}`)
  
  // Test with conflicting signals
  const conflictingData: IndicatorDataPoint[] = [
    { value: 25, timestamp: Date.now() } // RSI oversold, but price might be below SMA
  ]
  
  for (const data of conflictingData) {
    const signals = signalEngine.processMarketData(data)
    signals.forEach(signal => {
      console.log(`Weighted Signal: ${SignalEngineUtils.formatSignal(signal)}`)
      console.log('Indicator contributions:')
      signal.indicators.forEach(ind => {
        console.log(`  ${ind.name}: ${ind.signal} (confidence: ${(ind.confidence * 100).toFixed(1)}%, weight: ${ind.weight || 'N/A'})`)
      })
    })
  }
}

/**
 * Example 4: Strategy Presets
 * Demonstrates using predefined strategy configurations
 */
export async function strategyPresetsExample(): Promise<void> {
  console.log('\n=== Strategy Presets Example ===')
  
  const signalEngine = SignalEngine.getInstance()
  
  // Create conservative RSI strategy
  const conservativeStrategy = signalEngine.createStrategy('rsi', StrategyPresets.CONSERVATIVE_RSI)
  console.log(`Created conservative strategy: ${conservativeStrategy.name}`)
  
  // Create aggressive RSI strategy
  const aggressiveStrategy = signalEngine.createStrategy('rsi', StrategyPresets.AGGRESSIVE_RSI)
  console.log(`Created aggressive strategy: ${aggressiveStrategy.name}`)
  
  // Test with borderline oversold condition
  const borderlineData: IndicatorDataPoint = { value: 35, timestamp: Date.now() }
  
  const signals = signalEngine.processMarketData(borderlineData)
  console.log(`Signals generated: ${signals.length}`)
  
  signals.forEach(signal => {
    console.log(`${signal.strategy}: ${SignalEngineUtils.formatSignal(signal)}`)
  })
}

/**
 * Example 5: Performance Monitoring
 * Demonstrates performance monitoring and metrics collection
 */
export async function performanceMonitoringExample(): Promise<void> {
  console.log('\n=== Performance Monitoring Example ===')
  
  const signalEngine = SignalEngine.getInstance()
  
  // Create multiple strategies
  const strategies = [
    signalEngine.createStrategy('rsi', { name: 'rsi_fast', period: 7 }),
    signalEngine.createStrategy('rsi', { name: 'rsi_slow', period: 21 }),
    signalEngine.createStrategy('sma', { name: 'sma_short', period: 10 }),
    signalEngine.createStrategy('sma', { name: 'sma_long', period: 50 })
  ]
  
  console.log(`Created ${strategies.length} strategies`)
  
  // Generate some market data
  const performanceData: IndicatorDataPoint[] = Array.from({ length: 100 }, (_, i) => ({
    value: 50 + Math.sin(i * 0.1) * 30 + Math.random() * 10,
    timestamp: Date.now() - (100 - i) * 1000
  }))
  
  // Process data and measure performance
  const startTime = Date.now()
  let totalSignals = 0
  
  for (const data of performanceData) {
    const signals = signalEngine.processMarketData(data)
    totalSignals += signals.length
  }
  
  const processingTime = Date.now() - startTime
  
  // Get performance metrics
  const engineMetrics = signalEngine.getPerformanceMetrics()
  console.log('Engine Performance:')
  console.log(SignalEngineUtils.getPerformanceSummary(engineMetrics))
  console.log(`Total processing time: ${processingTime}ms`)
  console.log(`Signals generated: ${totalSignals}`)
  
  // Individual strategy performance
  console.log('\nStrategy Performance:')
  strategies.forEach(strategy => {
    const metrics = strategy.getPerformanceMetrics()
    console.log(`${strategy.name}: ${metrics.totalSignals} signals, ${(metrics.averageConfidence * 100).toFixed(1)}% avg confidence`)
  })
}

/**
 * Example 6: Signal History and Analysis
 * Demonstrates signal history management and analysis
 */
export async function signalHistoryExample(): Promise<void> {
  console.log('\n=== Signal History Example ===')
  
  const signalEngine = SignalEngine.getInstance()
  
  // Create a strategy
  const strategy = SignalEngineUtils.createQuickRSIStrategy(14, 70, 30)
  
  // Generate historical data
  const historicalData: IndicatorDataPoint[] = [
    { value: 80, timestamp: Date.now() - 5000 }, // Overbought
    { value: 75, timestamp: Date.now() - 4000 },
    { value: 50, timestamp: Date.now() - 3000 }, // Neutral
    { value: 25, timestamp: Date.now() - 2000 }, // Oversold
    { value: 30, timestamp: Date.now() - 1000 },
    { value: 75, timestamp: Date.now() }         // Back to overbought
  ]
  
  // Process data
  for (const data of historicalData) {
    signalEngine.processMarketData(data)
  }
  
  // Analyze signal history
  const allSignals = signalEngine.getSignalHistory()
  const strategySignals = signalEngine.getSignalHistory(strategy.name)
  
  console.log(`Total signals in history: ${allSignals.length}`)
  console.log(`Signals from ${strategy.name}: ${strategySignals.length}`)
  
  // Analyze signal patterns
  const signalCounts = strategySignals.reduce((counts, signal) => {
    counts[signal.signal] = (counts[signal.signal] || 0) + 1
    return counts
  }, {} as Record<string, number>)
  
  console.log('Signal distribution:', signalCounts)
  
  // Show recent signals
  console.log('\nRecent signals:')
  strategySignals.slice(-3).forEach(signal => {
    console.log(`  ${SignalEngineUtils.formatSignal(signal)}`)
  })
}

/**
 * Example 7: Error Handling and Validation
 * Demonstrates proper error handling and validation
 */
export async function errorHandlingExample(): Promise<void> {
  console.log('\n=== Error Handling Example ===')
  
  const signalEngine = SignalEngine.getInstance()
  
  try {
    // Try to create strategy with invalid indicator
    const invalidStrategy = signalEngine.createStrategy('invalid_indicator', {})
  } catch (error) {
    console.log('Expected error caught:', error.message)
  }
  
  try {
    // Try to create strategy with invalid configuration
    const invalidConfig = signalEngine.createStrategy('rsi', {
      period: -5, // Invalid period
      minConfidence: 2.0 // Invalid confidence
    })
  } catch (error) {
    console.log('Configuration error caught:', error.message)
  }
  
  // Create valid strategy
  const validStrategy = signalEngine.createStrategy('rsi', {
    name: 'valid_strategy',
    period: 14
  })
  
  console.log(`Successfully created: ${validStrategy.name}`)
  
  // Test strategy limits
  try {
    // Try to create too many strategies (if limit is set low)
    for (let i = 0; i < 20; i++) {
      signalEngine.createStrategy('rsi', { name: `strategy_${i}`, period: 14 })
    }
  } catch (error) {
    console.log('Strategy limit error:', error.message)
  }
}

/**
 * Run all examples
 */
export async function runAllExamples(): Promise<void> {
  console.log('🚀 Running Signal Engine Examples\n')
  
  try {
    await basicRSIExample()
    await multiIndicatorExample()
    await weightedStrategyExample()
    await strategyPresetsExample()
    await performanceMonitoringExample()
    await signalHistoryExample()
    await errorHandlingExample()
    
    console.log('\n✅ All examples completed successfully!')
    
    // Final cleanup
    const signalEngine = SignalEngine.getInstance()
    const finalMetrics = signalEngine.getPerformanceMetrics()
    console.log('\nFinal Engine State:')
    console.log(SignalEngineUtils.getPerformanceSummary(finalMetrics))
    
  } catch (error) {
    console.error('❌ Example execution failed:', error)
  }
}

// Export individual examples for selective testing
export const Examples = {
  basicRSI: basicRSIExample,
  multiIndicator: multiIndicatorExample,
  weightedStrategy: weightedStrategyExample,
  strategyPresets: strategyPresetsExample,
  performanceMonitoring: performanceMonitoringExample,
  signalHistory: signalHistoryExample,
  errorHandling: errorHandlingExample,
  runAll: runAllExamples
}
