/**
 * Start/Stop Button Component
 * Circular button with toggle functionality, glowing hover effect, and state management
 */

import React, { useCallback } from 'react'
import type { StartStopButtonProps } from '../../../shared/types/trading'
import { FORM_LABELS, CSS_CLASSES, UI_CONSTANTS } from '../../../shared/constants/trading'

/**
 * StartStopButton Component
 * Renders a circular button that toggles between start and stop states
 */
export const StartStopButton: React.FC<StartStopButtonProps> = ({
  state,
  onClick,
  disabled = false,
  ariaLabel
}) => {
  /**
   * Handles button click
   */
  const handleClick = useCallback(() => {
    if (!disabled) {
      onClick()
    }
  }, [onClick, disabled])

  /**
   * Handles keyboard interaction
   */
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLButtonElement>) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault()
        handleClick()
      }
    },
    [handleClick]
  )

  const isRunning = state === 'running'
  const buttonText = isRunning
    ? FORM_LABELS.startStopButton.stop
    : FORM_LABELS.startStopButton.start

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Main Circular Button */}
      <button
        type="button"
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        aria-label={ariaLabel || FORM_LABELS.startStopButton.ariaLabel}
        aria-pressed={isRunning}
        className={`
          ${CSS_CLASSES.startStopButton}
          ${isRunning ? CSS_CLASSES.startStopButtonRunning : ''}
          relative
          w-32
          h-32
          rounded-full
          font-bold
          text-lg
          transition-all
          duration-300
          ease-in-out
          focus:outline-none
          focus:ring-4
          focus:ring-offset-2
          focus:ring-offset-gray-900
          transform
          active:scale-95
          ${
            disabled
              ? 'bg-gray-700 text-gray-500 cursor-not-allowed border-gray-600'
              : isRunning
                ? 'bg-red-600 text-white border-red-500 hover:bg-red-700 focus:ring-red-500 shadow-lg'
                : 'bg-green-600 text-white border-green-500 hover:bg-green-700 focus:ring-green-500 shadow-lg'
          }
          border-2
        `}
        style={{
          borderRadius: UI_CONSTANTS.button.borderRadius,
          boxShadow: disabled
            ? 'none'
            : `${UI_CONSTANTS.button.glowIntensity} ${isRunning ? '#dc2626aa' : '#16a34aaa'}`
        }}
      >
        {/* Button Content */}
        <div className="flex flex-col items-center justify-center h-full">
          {/* Icon */}
          <div className="mb-1">
            {isRunning ? (
              // Stop Icon
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <rect x="6" y="6" width="12" height="12" rx="2" />
              </svg>
            ) : (
              // Play Icon
              <svg
                className="w-8 h-8 ml-1"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <polygon points="5,3 19,12 5,21" />
              </svg>
            )}
          </div>

          {/* Button Text */}
          <span className="text-sm font-medium leading-tight text-center">{buttonText}</span>
        </div>

        {/* Pulse Animation for Running State */}
        {isRunning && !disabled && (
          <div
            className="absolute inset-0 rounded-full animate-ping"
            style={{
              backgroundColor: 'rgba(220, 38, 38, 0.3)',
              borderRadius: UI_CONSTANTS.button.borderRadius
            }}
          />
        )}

        {/* Hover Glow Effect */}
        {!disabled && (
          <div
            className={`
              absolute 
              inset-0 
              rounded-full 
              opacity-0 
              hover:opacity-100 
              transition-opacity 
              duration-300
              pointer-events-none
            `}
            style={{
              borderRadius: UI_CONSTANTS.button.borderRadius,
              boxShadow: `0 0 20px ${isRunning ? '#dc2626' : '#16a34a'}`,
              filter: 'blur(4px)'
            }}
          />
        )}
      </button>

      {/* Status Text */}
      <div className="text-center">
        <p
          className={`text-sm font-medium ${
            disabled ? 'text-gray-500' : isRunning ? 'text-red-400' : 'text-green-400'
          }`}
        >
          {disabled ? 'Complete form to start' : isRunning ? 'Trading Active' : 'Ready to Start'}
        </p>

        {/* Additional Status Info */}
        {isRunning && <p className="text-xs text-gray-400 mt-1">Click to stop trading</p>}
      </div>
    </div>
  )
}

export default StartStopButton
