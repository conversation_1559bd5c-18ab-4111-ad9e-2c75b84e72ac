/**
 * Strategy Factory Class
 * Factory class responsible for creating individual indicators and combining them into cohesive trading strategies
 * Implements Singleton pattern for consistent strategy management across the application
 *
 * @example
 * ```typescript
 * // Get singleton instance
 * const factory = StrategyFactory.getInstance()
 *
 * // Create single indicator strategy
 * const rsiStrategy = factory.createSingleIndicatorStrategy('rsi', {
 *   name: 'rsi_oversold',
 *   indicatorConfig: { period: 14 },
 *   signalRules: {
 *     buyConditions: [{ type: 'threshold', params: { threshold: 30, operator: 'lte' } }],
 *     sellConditions: [{ type: 'threshold', params: { threshold: 70, operator: 'gte' } }]
 *   }
 * })
 *
 * // Create multi-indicator strategy
 * const combinedStrategy = factory.createMultiIndicatorStrategy(['rsi', 'sma'], {
 *   name: 'rsi_sma_combined',
 *   indicatorConfigs: {
 *     rsi: { period: 14 },
 *     sma: { period: 20 }
 *   },
 *   signalRules: {
 *     rsi: { buyConditions: [...], sellConditions: [...] },
 *     sma: { buyConditions: [...], sellConditions: [...] }
 *   },
 *   combinationLogic: 'AND'
 * })
 * ```
 */

import { logger } from '../../shared/utils/logger'
import { indicatorRegistry } from '../indicators'
import type {
  Strategy,
  StrategyFactory as IStrategyFactory,
  StrategyConfig,
  SingleIndicatorStrategyConfig,
  MultiIndicatorStrategyConfig,
  StrategyValidationResult,
  StrategyFactoryConfig,
  GeneratedSignal,
  StrategyAnalysisResult,
  IndicatorContribution,
  StrategyAnalysis,
  IndicatorAnalysis,
  StrategyPerformanceMetrics,
  TradingSignal,
  StrategyCombinationLogic,
  IndicatorSignalRules,
  IndicatorWeight
} from '../../shared/types/signals'
import type {
  BaseIndicator,
  BaseIndicatorConfig,
  IndicatorDataPoint
} from '../../shared/types/indicators'
import {
  DEFAULT_STRATEGY_FACTORY_CONFIG,
  DEFAULT_STRATEGY_CONFIG,
  DEFAULT_SIGNAL_RULES_BY_INDICATOR,
  TRADING_SIGNALS,
  COMBINATION_LOGIC,
  SIGNAL_ENGINE_ERROR_MESSAGES,
  SIGNAL_ENGINE_LOG_CATEGORIES,
  DEFAULT_CONFIDENCE_LEVELS
} from '../../shared/constants/signals'
import {
  validateStrategyConfig,
  SignalValidationError,
  sanitizeSignalError
} from '../../shared/utils/signalValidation'

/**
 * Strategy implementation class
 * Implements the Strategy interface with comprehensive signal generation capabilities
 */
class StrategyImpl implements Strategy {
  private _indicators: Map<string, BaseIndicator> = new Map()
  private _signalHistory: GeneratedSignal[] = []
  private _performanceMetrics: StrategyPerformanceMetrics
  private _isReady: boolean = false

  constructor(public readonly config: StrategyConfig) {
    this._performanceMetrics = {
      totalSignals: 0,
      buySignals: 0,
      sellSignals: 0,
      holdSignals: 0,
      averageConfidence: 0,
      lastUpdated: Date.now()
    }

    this.initializeStrategy()
  }

  /**
   * Initialize strategy by creating indicators
   */
  private async initializeStrategy(): Promise<void> {
    try {
      if (this.isSingleIndicatorStrategy(this.config)) {
        await this.initializeSingleIndicatorStrategy(this.config)
      } else if (this.isMultiIndicatorStrategy(this.config)) {
        await this.initializeMultiIndicatorStrategy(this.config)
      }

      this._isReady = true
      logger.info(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} Strategy '${this.config.name}' initialized successfully`
      )
    } catch (error) {
      const errorMessage = sanitizeSignalError(error)
      logger.error(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.ERROR} Failed to initialize strategy '${this.config.name}': ${errorMessage}`
      )
      throw new SignalValidationError(
        `Strategy initialization failed: ${errorMessage}`,
        'strategy_initialization'
      )
    }
  }

  /**
   * Initialize single indicator strategy
   */
  private async initializeSingleIndicatorStrategy(
    config: SingleIndicatorStrategyConfig
  ): Promise<void> {
    try {
      const indicator = indicatorRegistry.create<BaseIndicator>(
        config.indicator,
        config.indicatorConfig
      )
      this._indicators.set(config.indicator, indicator)
    } catch (error) {
      logger.error(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.ERROR} Failed to create indicator '${config.indicator}' for strategy '${this.config.name}': ${sanitizeSignalError(
          error
        )}`
      )
      throw new SignalValidationError(
        `${SIGNAL_ENGINE_ERROR_MESSAGES.INDICATOR_CREATION_FAILED}: ${config.indicator}`,
        'indicator_creation'
      )
    }
  }

  /**
   * Initialize multi-indicator strategy
   */
  private async initializeMultiIndicatorStrategy(
    config: MultiIndicatorStrategyConfig
  ): Promise<void> {
    for (const indicatorName of config.indicators) {
      try {
        const indicatorConfig = config.indicatorConfigs[indicatorName]
        const indicator = indicatorRegistry.create<BaseIndicator>(indicatorName, indicatorConfig)
        this._indicators.set(indicatorName, indicator)
      } catch (error) {
        logger.error(
          `${SIGNAL_ENGINE_LOG_CATEGORIES.ERROR} Failed to create indicator '${indicatorName}' for strategy '${this.config.name}': ${sanitizeSignalError(
            error
          )}`
        )
        throw new SignalValidationError(
          `${SIGNAL_ENGINE_ERROR_MESSAGES.INDICATOR_CREATION_FAILED}: ${indicatorName}`,
          'indicator_creation'
        )
      }
    }
  }

  // Getters
  get name(): string {
    return this.config.name
  }

  get isReady(): boolean {
    return this._isReady && this.areIndicatorsReady()
  }

  get indicators(): Map<string, BaseIndicator> {
    return new Map(this._indicators)
  }

  get signalHistory(): GeneratedSignal[] {
    return [...this._signalHistory]
  }

  /**
   * Check if all indicators are ready
   */
  private areIndicatorsReady(): boolean {
    return Array.from(this._indicators.values()).every((indicator) => indicator.isReady)
  }

  /**
   * Type guard for single indicator strategy
   */
  private isSingleIndicatorStrategy(
    config: StrategyConfig
  ): config is SingleIndicatorStrategyConfig {
    return 'indicator' in config && typeof config.indicator === 'string'
  }

  /**
   * Type guard for multi-indicator strategy
   */
  private isMultiIndicatorStrategy(config: StrategyConfig): config is MultiIndicatorStrategyConfig {
    return 'indicators' in config && Array.isArray(config.indicators)
  }

  /**
   * Analyze market data and generate signal
   */
  async analyze(data: IndicatorDataPoint): Promise<StrategyAnalysisResult> {
    if (!this.isReady) {
      throw new SignalValidationError(
        SIGNAL_ENGINE_ERROR_MESSAGES.INSUFFICIENT_DATA,
        'strategy_not_ready'
      )
    }

    try {
      // Add data to all indicators and collect their outputs
      const indicatorAnalyses: IndicatorAnalysis[] = []
      const indicatorContributions: IndicatorContribution[] = []

      for (const [name, indicator] of this._indicators) {
        const output = indicator.addData(data)
        if (output) {
          const analysis = await this.analyzeIndicator(name, indicator, output.value)
          indicatorAnalyses.push(analysis)

          indicatorContributions.push({
            name,
            value: output.value,
            signal: analysis.signal,
            confidence: analysis.confidence
          })
        }
      }

      // Combine signals based on strategy configuration
      const combinedSignal = this.combineSignals(indicatorContributions)

      // Create generated signal
      const generatedSignal: GeneratedSignal = {
        signal: combinedSignal.signal,
        confidence: combinedSignal.confidence,
        timestamp: Date.now(),
        strategy: this.config.name,
        indicators: indicatorContributions
      }

      // Update signal history
      this.addToSignalHistory(generatedSignal)

      // Update performance metrics
      this.updatePerformanceMetrics(generatedSignal)

      // Create analysis result
      const strategyAnalysis: StrategyAnalysis = {
        indicatorAnalyses,
        combinationLogic: this.getCombinationLogic(),
        reasoning: this.generateReasoning(indicatorAnalyses, combinedSignal)
      }

      return {
        signal: generatedSignal,
        analysis: strategyAnalysis,
        performance: this._performanceMetrics
      }
    } catch (error) {
      const errorMessage = sanitizeSignalError(error)
      logger.error(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.ERROR} Signal analysis failed for strategy '${this.config.name}': ${errorMessage}`
      )
      throw new SignalValidationError(
        `${SIGNAL_ENGINE_ERROR_MESSAGES.SIGNAL_GENERATION_FAILED}: ${errorMessage}`,
        'signal_analysis'
      )
    }
  }

  /**
   * Add market data for analysis
   */
  addData(data: IndicatorDataPoint): GeneratedSignal | null {
    if (!this.isReady) {
      return null
    }

    try {
      // Add data to all indicators
      const indicatorContributions: IndicatorContribution[] = []
      let hasValidOutput = false

      for (const [name, indicator] of this._indicators) {
        const output = indicator.addData(data)
        if (output) {
          hasValidOutput = true
          const signal = this.generateSignalFromIndicator(name, output.value)
          const confidence = this.calculateIndicatorConfidence(name, output.value)

          indicatorContributions.push({
            name,
            value: output.value,
            signal,
            confidence
          })
        }
      }

      if (!hasValidOutput) {
        return null
      }

      // Combine signals
      const combinedSignal = this.combineSignals(indicatorContributions)

      // Create generated signal
      const generatedSignal: GeneratedSignal = {
        signal: combinedSignal.signal,
        confidence: combinedSignal.confidence,
        timestamp: Date.now(),
        strategy: this.config.name,
        indicators: indicatorContributions
      }

      // Update signal history and metrics
      this.addToSignalHistory(generatedSignal)
      this.updatePerformanceMetrics(generatedSignal)

      return generatedSignal
    } catch (error) {
      logger.error(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.ERROR} Failed to add data to strategy '${this.config.name}': ${sanitizeSignalError(error)}`
      )
      return null
    }
  }

  /**
   * Get latest signal
   */
  getLatestSignal(): GeneratedSignal | null {
    return this._signalHistory.length > 0
      ? this._signalHistory[this._signalHistory.length - 1]
      : null
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): StrategyPerformanceMetrics {
    return { ...this._performanceMetrics }
  }

  /**
   * Reset strategy state
   */
  reset(): void {
    this._signalHistory = []
    this._performanceMetrics = {
      totalSignals: 0,
      buySignals: 0,
      sellSignals: 0,
      holdSignals: 0,
      averageConfidence: 0,
      lastUpdated: Date.now()
    }

    // Reset all indicators
    for (const indicator of this._indicators.values()) {
      if (typeof indicator.reset === 'function') {
        indicator.reset()
      }
    }
  }

  /**
   * Dispose of strategy resources
   */
  dispose(): void {
    this._signalHistory = []
    this._indicators.clear()
    this._isReady = false
  }

  /**
   * Analyze individual indicator
   */
  private async analyzeIndicator(
    name: string,
    indicator: BaseIndicator,
    value: number
  ): Promise<IndicatorAnalysis> {
    const signal = this.generateSignalFromIndicator(name, value)
    const confidence = this.calculateIndicatorConfidence(name, value)
    const metConditions = this.getMetConditions(name, value, signal)

    return {
      indicator: name,
      currentValue: value,
      previousValue: this.getPreviousIndicatorValue(name),
      signal,
      confidence,
      metConditions,
      notes: `${name} analysis: ${signal} signal with ${(confidence * 100).toFixed(1)}% confidence`
    }
  }

  /**
   * Generate signal from individual indicator
   */
  private generateSignalFromIndicator(indicatorName: string, value: number): TradingSignal {
    const rules = this.getSignalRulesForIndicator(indicatorName)

    // Check buy conditions
    if (this.evaluateConditions(rules.buyConditions, indicatorName, value)) {
      return TRADING_SIGNALS.BUY
    }

    // Check sell conditions
    if (this.evaluateConditions(rules.sellConditions, indicatorName, value)) {
      return TRADING_SIGNALS.SELL
    }

    // Default to hold
    return TRADING_SIGNALS.HOLD
  }

  /**
   * Calculate confidence for individual indicator
   */
  private calculateIndicatorConfidence(indicatorName: string, value: number): number {
    // Simple confidence calculation based on how far the value is from thresholds
    // This can be enhanced with more sophisticated algorithms
    const rules = this.getSignalRulesForIndicator(indicatorName)

    // For RSI-like indicators
    if (indicatorName.toLowerCase().includes('rsi')) {
      if (value <= 30) return Math.max(0.5, (30 - value) / 30 + 0.5)
      if (value >= 70) return Math.max(0.5, (value - 70) / 30 + 0.5)
      return 0.3 // Low confidence in neutral zone
    }

    // Default confidence
    return DEFAULT_CONFIDENCE_LEVELS[TRADING_SIGNALS.HOLD]
  }

  /**
   * Combine signals from multiple indicators
   */
  private combineSignals(contributions: IndicatorContribution[]): {
    signal: TradingSignal
    confidence: number
  } {
    if (contributions.length === 0) {
      return { signal: TRADING_SIGNALS.HOLD, confidence: 0 }
    }

    if (contributions.length === 1) {
      return { signal: contributions[0].signal, confidence: contributions[0].confidence }
    }

    const combinationLogic = this.getCombinationLogic()

    switch (combinationLogic) {
      case COMBINATION_LOGIC.AND:
        return this.combineSignalsAND(contributions)
      case COMBINATION_LOGIC.OR:
        return this.combineSignalsOR(contributions)
      case COMBINATION_LOGIC.MAJORITY:
        return this.combineSignalsMajority(contributions)
      case COMBINATION_LOGIC.WEIGHTED:
        return this.combineSignalsWeighted(contributions)
      default:
        return this.combineSignalsMajority(contributions)
    }
  }

  /**
   * Combine signals using AND logic
   */
  private combineSignalsAND(contributions: IndicatorContribution[]): {
    signal: TradingSignal
    confidence: number
  } {
    const buySignals = contributions.filter((c) => c.signal === TRADING_SIGNALS.BUY)
    const sellSignals = contributions.filter((c) => c.signal === TRADING_SIGNALS.SELL)

    if (buySignals.length === contributions.length) {
      const avgConfidence = buySignals.reduce((sum, c) => sum + c.confidence, 0) / buySignals.length
      return { signal: TRADING_SIGNALS.BUY, confidence: avgConfidence }
    }

    if (sellSignals.length === contributions.length) {
      const avgConfidence =
        sellSignals.reduce((sum, c) => sum + c.confidence, 0) / sellSignals.length
      return { signal: TRADING_SIGNALS.SELL, confidence: avgConfidence }
    }

    return { signal: TRADING_SIGNALS.HOLD, confidence: 0.3 }
  }

  /**
   * Combine signals using OR logic
   */
  private combineSignalsOR(contributions: IndicatorContribution[]): {
    signal: TradingSignal
    confidence: number
  } {
    const buySignals = contributions.filter((c) => c.signal === TRADING_SIGNALS.BUY)
    const sellSignals = contributions.filter((c) => c.signal === TRADING_SIGNALS.SELL)

    if (buySignals.length > 0) {
      const maxConfidence = Math.max(...buySignals.map((c) => c.confidence))
      return { signal: TRADING_SIGNALS.BUY, confidence: maxConfidence }
    }

    if (sellSignals.length > 0) {
      const maxConfidence = Math.max(...sellSignals.map((c) => c.confidence))
      return { signal: TRADING_SIGNALS.SELL, confidence: maxConfidence }
    }

    return { signal: TRADING_SIGNALS.HOLD, confidence: 0.3 }
  }

  /**
   * Combine signals using majority logic
   */
  private combineSignalsMajority(contributions: IndicatorContribution[]): {
    signal: TradingSignal
    confidence: number
  } {
    const signalCounts = {
      [TRADING_SIGNALS.BUY]: contributions.filter((c) => c.signal === TRADING_SIGNALS.BUY),
      [TRADING_SIGNALS.SELL]: contributions.filter((c) => c.signal === TRADING_SIGNALS.SELL),
      [TRADING_SIGNALS.HOLD]: contributions.filter((c) => c.signal === TRADING_SIGNALS.HOLD)
    }

    const maxCount = Math.max(
      signalCounts[TRADING_SIGNALS.BUY].length,
      signalCounts[TRADING_SIGNALS.SELL].length,
      signalCounts[TRADING_SIGNALS.HOLD].length
    )

    if (signalCounts[TRADING_SIGNALS.BUY].length === maxCount) {
      const avgConfidence =
        signalCounts[TRADING_SIGNALS.BUY].reduce((sum, c) => sum + c.confidence, 0) /
        signalCounts[TRADING_SIGNALS.BUY].length
      return { signal: TRADING_SIGNALS.BUY, confidence: avgConfidence }
    }

    if (signalCounts[TRADING_SIGNALS.SELL].length === maxCount) {
      const avgConfidence =
        signalCounts[TRADING_SIGNALS.SELL].reduce((sum, c) => sum + c.confidence, 0) /
        signalCounts[TRADING_SIGNALS.SELL].length
      return { signal: TRADING_SIGNALS.SELL, confidence: avgConfidence }
    }

    return { signal: TRADING_SIGNALS.HOLD, confidence: 0.3 }
  }

  /**
   * Combine signals using weighted logic
   */
  private combineSignalsWeighted(contributions: IndicatorContribution[]): {
    signal: TradingSignal
    confidence: number
  } {
    if (!this.isMultiIndicatorStrategy(this.config) || !this.config.weights) {
      return this.combineSignalsMajority(contributions)
    }

    let buyScore = 0
    let sellScore = 0
    let totalWeight = 0

    for (const contribution of contributions) {
      const weight = this.config.weights.find((w) => w.indicator === contribution.name)?.weight || 0
      totalWeight += weight

      if (contribution.signal === TRADING_SIGNALS.BUY) {
        buyScore += weight * contribution.confidence
      } else if (contribution.signal === TRADING_SIGNALS.SELL) {
        sellScore += weight * contribution.confidence
      }
    }

    if (buyScore > sellScore && buyScore > 0.5) {
      return { signal: TRADING_SIGNALS.BUY, confidence: buyScore / totalWeight }
    }

    if (sellScore > buyScore && sellScore > 0.5) {
      return { signal: TRADING_SIGNALS.SELL, confidence: sellScore / totalWeight }
    }

    return { signal: TRADING_SIGNALS.HOLD, confidence: 0.3 }
  }

  /**
   * Add signal to history
   */
  private addToSignalHistory(signal: GeneratedSignal): void {
    this._signalHistory.push(signal)

    // Limit history size
    const maxHistory =
      this.config.maxSignalHistory || DEFAULT_STRATEGY_CONFIG.maxSignalHistory || 1000
    if (this._signalHistory.length > maxHistory) {
      this._signalHistory = this._signalHistory.slice(-maxHistory)
    }
  }

  /**
   * Update performance metrics
   */
  private updatePerformanceMetrics(signal: GeneratedSignal): void {
    this._performanceMetrics.totalSignals++
    this._performanceMetrics.lastUpdated = Date.now()

    switch (signal.signal) {
      case TRADING_SIGNALS.BUY:
        this._performanceMetrics.buySignals++
        break
      case TRADING_SIGNALS.SELL:
        this._performanceMetrics.sellSignals++
        break
      case TRADING_SIGNALS.HOLD:
        this._performanceMetrics.holdSignals++
        break
    }

    // Update average confidence
    const totalConfidence = this._signalHistory.reduce((sum, s) => sum + s.confidence, 0)
    this._performanceMetrics.averageConfidence = totalConfidence / this._signalHistory.length
  }

  /**
   * Get combination logic for the strategy
   */
  private getCombinationLogic(): StrategyCombinationLogic {
    if (this.isMultiIndicatorStrategy(this.config)) {
      return this.config.combinationLogic
    }
    return COMBINATION_LOGIC.AND // Default for single indicator
  }

  /**
   * Get signal rules for an indicator
   */
  private getSignalRulesForIndicator(indicatorName: string) {
    if (this.isSingleIndicatorStrategy(this.config)) {
      return this.config.signalRules
    } else if (this.isMultiIndicatorStrategy(this.config)) {
      return (
        this.config.signalRules[indicatorName] ||
        DEFAULT_SIGNAL_RULES_BY_INDICATOR[indicatorName.toLowerCase()]
      )
    }
    return DEFAULT_SIGNAL_RULES_BY_INDICATOR[indicatorName.toLowerCase()]
  }

  /**
   * Evaluate signal conditions
   */
  private evaluateConditions(conditions: any[], indicatorName: string, value: number): boolean {
    return conditions.some((condition) => this.evaluateCondition(condition, indicatorName, value))
  }

  /**
   * Evaluate single condition
   */
  private evaluateCondition(condition: any, indicatorName: string, value: number): boolean {
    switch (condition.type) {
      case 'threshold':
        return this.evaluateThresholdCondition(condition.params, value)
      case 'crossover':
        return this.evaluateCrossoverCondition(condition.params, indicatorName, value)
      default:
        return false
    }
  }

  /**
   * Evaluate threshold condition
   */
  private evaluateThresholdCondition(params: any, value: number): boolean {
    const { threshold, operator = 'gte' } = params

    switch (operator) {
      case 'gt':
        return value > threshold
      case 'lt':
        return value < threshold
      case 'gte':
        return value >= threshold
      case 'lte':
        return value <= threshold
      case 'eq':
        return Math.abs(value - threshold) < 0.001
      case 'ne':
        return Math.abs(value - threshold) >= 0.001
      default:
        return false
    }
  }

  /**
   * Evaluate crossover condition
   */
  private evaluateCrossoverCondition(params: any, indicatorName: string, value: number): boolean {
    // Simplified crossover evaluation - can be enhanced
    const { reference, direction } = params

    if (reference === 'price') {
      // This would need actual price data - simplified for now
      return direction === 'cross_above' ? value > 50 : value < 50
    }

    return false
  }

  /**
   * Get conditions that were met for signal generation
   */
  private getMetConditions(indicatorName: string, value: number, signal: TradingSignal): string[] {
    const rules = this.getSignalRulesForIndicator(indicatorName)
    const metConditions: string[] = []

    if (signal === TRADING_SIGNALS.BUY) {
      rules.buyConditions.forEach((condition, index) => {
        if (this.evaluateCondition(condition, indicatorName, value)) {
          metConditions.push(`Buy condition ${index + 1}: ${condition.type}`)
        }
      })
    } else if (signal === TRADING_SIGNALS.SELL) {
      rules.sellConditions.forEach((condition, index) => {
        if (this.evaluateCondition(condition, indicatorName, value)) {
          metConditions.push(`Sell condition ${index + 1}: ${condition.type}`)
        }
      })
    }

    return metConditions
  }

  /**
   * Get previous indicator value
   */
  private getPreviousIndicatorValue(indicatorName: string): number | undefined {
    const indicator = this._indicators.get(indicatorName)
    if (!indicator || !indicator.latestValue) {
      return undefined
    }

    // This is simplified - in a real implementation, you'd track historical values
    return indicator.latestValue.value
  }

  /**
   * Generate reasoning for signal decision
   */
  private generateReasoning(
    analyses: IndicatorAnalysis[],
    combinedSignal: { signal: TradingSignal; confidence: number }
  ): string {
    const signalCounts = {
      [TRADING_SIGNALS.BUY]: analyses.filter((a) => a.signal === TRADING_SIGNALS.BUY).length,
      [TRADING_SIGNALS.SELL]: analyses.filter((a) => a.signal === TRADING_SIGNALS.SELL).length,
      [TRADING_SIGNALS.HOLD]: analyses.filter((a) => a.signal === TRADING_SIGNALS.HOLD).length
    }

    const logic = this.getCombinationLogic()
    const totalIndicators = analyses.length

    let reasoning = `Strategy '${this.config.name}' generated ${combinedSignal.signal} signal with ${(combinedSignal.confidence * 100).toFixed(1)}% confidence. `
    reasoning += `Using ${logic} combination logic with ${totalIndicators} indicator(s): `
    reasoning += `${signalCounts[TRADING_SIGNALS.BUY]} BUY, ${signalCounts[TRADING_SIGNALS.SELL]} SELL, ${signalCounts[TRADING_SIGNALS.HOLD]} HOLD signals.`

    return reasoning
  }
}

/**
 * Strategy Factory Class
 * Singleton factory for creating and managing trading strategies
 * Provides comprehensive strategy creation with validation and error handling
 */
export class StrategyFactory implements IStrategyFactory {
  private static instance: StrategyFactory | null = null
  private readonly config: StrategyFactoryConfig

  /**
   * Private constructor for Singleton pattern
   */
  private constructor(config?: Partial<StrategyFactoryConfig>) {
    this.config = {
      ...DEFAULT_STRATEGY_FACTORY_CONFIG,
      ...config
    }

    logger.info(
      `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} StrategyFactory initialized with config:`,
      this.config
    )
  }

  /**
   * Get singleton instance
   * @param config - Optional configuration override
   * @returns StrategyFactory instance
   */
  public static getInstance(config?: Partial<StrategyFactoryConfig>): StrategyFactory {
    if (!StrategyFactory.instance) {
      StrategyFactory.instance = new StrategyFactory(config)
    }
    return StrategyFactory.instance
  }

  /**
   * Reset singleton instance (useful for testing)
   */
  public static resetInstance(): void {
    StrategyFactory.instance = null
  }

  /**
   * Create a single indicator strategy
   */
  public createSingleIndicatorStrategy(
    indicator: string,
    config: Partial<SingleIndicatorStrategyConfig>
  ): Strategy {
    const methodName = 'createSingleIndicatorStrategy'
    logger.info(
      `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} ${methodName} called for indicator: ${indicator}`
    )

    try {
      // Validate indicator availability
      if (!this.isIndicatorAvailable(indicator)) {
        throw new SignalValidationError(
          `${SIGNAL_ENGINE_ERROR_MESSAGES.INVALID_INDICATOR}: ${indicator}`,
          'indicator_availability'
        )
      }

      // Build complete configuration
      const completeConfig: SingleIndicatorStrategyConfig = {
        ...this.config.defaultStrategyConfig,
        ...config,
        name: config.name || `${indicator}_strategy_${Date.now()}`,
        indicator,
        indicatorConfig: config.indicatorConfig || { period: 14 },
        signalRules: config.signalRules || this.getDefaultSignalRules(indicator)
      }

      // Validate configuration
      if (this.config.enableValidation) {
        const validation = validateStrategyConfig(completeConfig)
        if (!validation.isValid) {
          throw new SignalValidationError(
            `${SIGNAL_ENGINE_ERROR_MESSAGES.VALIDATION_FAILED}: ${validation.errors.join(', ')}`,
            'strategy_validation'
          )
        }

        if (validation.warnings.length > 0) {
          logger.warn(
            `${SIGNAL_ENGINE_LOG_CATEGORIES.VALIDATION} Strategy validation warnings:`,
            validation.warnings
          )
        }
      }

      // Create strategy instance
      const strategy = new StrategyImpl(completeConfig)

      logger.info(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} ${methodName} completed successfully for strategy: ${completeConfig.name}`
      )

      return strategy
    } catch (error) {
      const errorMessage = sanitizeSignalError(error)
      logger.error(`${SIGNAL_ENGINE_LOG_CATEGORIES.ERROR} ${methodName} failed: ${errorMessage}`)
      throw error instanceof SignalValidationError
        ? error
        : new SignalValidationError(errorMessage, 'strategy_creation')
    }
  }

  /**
   * Create a multi-indicator strategy
   */
  public createMultiIndicatorStrategy(
    indicators: string[],
    config: Partial<MultiIndicatorStrategyConfig>
  ): Strategy {
    const methodName = 'createMultiIndicatorStrategy'
    logger.info(
      `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} ${methodName} called for indicators: ${indicators.join(', ')}`
    )

    try {
      // Validate indicators
      this.validateIndicatorList(indicators)

      // Build complete configuration
      const completeConfig: MultiIndicatorStrategyConfig = {
        ...this.config.defaultStrategyConfig,
        ...config,
        name: config.name || `${indicators.join('_')}_strategy_${Date.now()}`,
        indicators,
        indicatorConfigs: config.indicatorConfigs || this.buildDefaultIndicatorConfigs(indicators),
        signalRules: config.signalRules || this.buildDefaultSignalRules(indicators),
        combinationLogic: config.combinationLogic || COMBINATION_LOGIC.MAJORITY
      }

      // Validate weights for weighted strategies
      if (
        completeConfig.combinationLogic === COMBINATION_LOGIC.WEIGHTED &&
        !completeConfig.weights
      ) {
        completeConfig.weights = this.buildDefaultWeights(indicators)
      }

      // Validate configuration
      if (this.config.enableValidation) {
        const validation = validateStrategyConfig(completeConfig)
        if (!validation.isValid) {
          throw new SignalValidationError(
            `${SIGNAL_ENGINE_ERROR_MESSAGES.VALIDATION_FAILED}: ${validation.errors.join(', ')}`,
            'strategy_validation'
          )
        }

        if (validation.warnings.length > 0) {
          logger.warn(
            `${SIGNAL_ENGINE_LOG_CATEGORIES.VALIDATION} Strategy validation warnings:`,
            validation.warnings
          )
        }
      }

      // Create strategy instance
      const strategy = new StrategyImpl(completeConfig)

      logger.info(
        `${SIGNAL_ENGINE_LOG_CATEGORIES.STRATEGY_CREATION} ${methodName} completed successfully for strategy: ${completeConfig.name}`
      )

      return strategy
    } catch (error) {
      const errorMessage = sanitizeSignalError(error)
      logger.error(`${SIGNAL_ENGINE_LOG_CATEGORIES.ERROR} ${methodName} failed: ${errorMessage}`)
      throw error instanceof SignalValidationError
        ? error
        : new SignalValidationError(errorMessage, 'strategy_creation')
    }
  }

  /**
   * Get available indicators
   */
  public getAvailableIndicators(): string[] {
    return indicatorRegistry.getAvailableIndicators()
  }

  /**
   * Validate strategy configuration
   */
  public validateStrategyConfig(config: StrategyConfig): StrategyValidationResult {
    return validateStrategyConfig(config)
  }

  /**
   * Check if indicator is available
   */
  private isIndicatorAvailable(indicator: string): boolean {
    return indicatorRegistry.isRegistered(indicator)
  }

  /**
   * Validate indicator list for multi-indicator strategy
   */
  private validateIndicatorList(indicators: string[]): void {
    if (!Array.isArray(indicators) || indicators.length === 0) {
      throw new SignalValidationError(
        'Indicators array is required and must not be empty',
        'indicators_validation'
      )
    }

    if (indicators.length > this.config.maxIndicatorsPerStrategy) {
      throw new SignalValidationError(
        `Cannot exceed maximum of ${this.config.maxIndicatorsPerStrategy} indicators per strategy`,
        'indicators_limit'
      )
    }

    // Check if all indicators are available
    for (const indicator of indicators) {
      if (!this.isIndicatorAvailable(indicator)) {
        throw new SignalValidationError(
          `${SIGNAL_ENGINE_ERROR_MESSAGES.INVALID_INDICATOR}: ${indicator}`,
          'indicator_availability'
        )
      }
    }

    // Check for duplicates
    const uniqueIndicators = new Set(indicators)
    if (uniqueIndicators.size !== indicators.length) {
      throw new SignalValidationError(
        'Duplicate indicators are not allowed',
        'duplicate_indicators'
      )
    }
  }

  /**
   * Get default signal rules for an indicator
   */
  private getDefaultSignalRules(indicator: string): IndicatorSignalRules {
    const rules = DEFAULT_SIGNAL_RULES_BY_INDICATOR[indicator.toLowerCase()]
    if (!rules) {
      throw new SignalValidationError(
        `No default signal rules found for indicator: ${indicator}`,
        'default_rules'
      )
    }
    return rules
  }

  /**
   * Build default indicator configurations
   */
  private buildDefaultIndicatorConfigs(indicators: string[]): Record<string, BaseIndicatorConfig> {
    const configs: Record<string, BaseIndicatorConfig> = {}

    for (const indicator of indicators) {
      configs[indicator] = { period: 14 } // Default configuration
    }

    return configs
  }

  /**
   * Build default signal rules for multiple indicators
   */
  private buildDefaultSignalRules(indicators: string[]): Record<string, IndicatorSignalRules> {
    const rules: Record<string, IndicatorSignalRules> = {}

    for (const indicator of indicators) {
      rules[indicator] = this.getDefaultSignalRules(indicator)
    }

    return rules
  }

  /**
   * Build default weights for indicators (equal weighting)
   */
  private buildDefaultWeights(indicators: string[]): IndicatorWeight[] {
    const weight = 1.0 / indicators.length

    return indicators.map((indicator) => ({
      indicator,
      weight
    }))
  }
}
