/**
 * TypeScript type definitions for Signal Engine and Strategy Factory
 * Contains all types and interfaces used by the signal generation system
 */

import type { BaseIndicator, BaseIndicatorConfig, IndicatorDataPoint } from './indicators'

/**
 * Trading signal types
 */
export type TradingSignal = 'BUY' | 'SELL' | 'HOLD'

/**
 * Strategy combination logic types
 */
export type StrategyCombinationLogic = 'AND' | 'OR' | 'MAJORITY' | 'WEIGHTED'

/**
 * Indicator weight for weighted strategies
 */
export interface IndicatorWeight {
  /** Indicator name */
  indicator: string
  /** Weight value (0-1) */
  weight: number
}

/**
 * Base configuration for all strategies
 */
export interface BaseStrategyConfig {
  /** Strategy name/identifier */
  name: string
  /** Strategy description */
  description?: string
  /** Whether to enable real-time signal generation */
  enableRealTime?: boolean
  /** Maximum number of signals to keep in history */
  maxSignalHistory?: number
  /** Whether to validate input data */
  validateInputs?: boolean
  /** Minimum confidence threshold for signals (0-1) */
  minConfidence?: number
}

/**
 * Single indicator strategy configuration
 */
export interface SingleIndicatorStrategyConfig extends BaseStrategyConfig {
  /** Indicator name */
  indicator: string
  /** Indicator configuration */
  indicatorConfig: BaseIndicatorConfig
  /** Signal generation rules */
  signalRules: IndicatorSignalRules
}

/**
 * Multi-indicator strategy configuration
 */
export interface MultiIndicatorStrategyConfig extends BaseStrategyConfig {
  /** Array of indicator names */
  indicators: string[]
  /** Configuration for each indicator */
  indicatorConfigs: Record<string, BaseIndicatorConfig>
  /** Signal generation rules for each indicator */
  signalRules: Record<string, IndicatorSignalRules>
  /** Logic for combining signals */
  combinationLogic: StrategyCombinationLogic
  /** Weights for weighted combination (optional) */
  weights?: IndicatorWeight[]
}

/**
 * Union type for strategy configurations
 */
export type StrategyConfig = SingleIndicatorStrategyConfig | MultiIndicatorStrategyConfig

/**
 * Signal generation rules for indicators
 */
export interface IndicatorSignalRules {
  /** Buy signal conditions */
  buyConditions: SignalCondition[]
  /** Sell signal conditions */
  sellConditions: SignalCondition[]
  /** Hold signal conditions (optional) */
  holdConditions?: SignalCondition[]
}

/**
 * Signal condition definition
 */
export interface SignalCondition {
  /** Condition type */
  type: 'threshold' | 'crossover' | 'divergence' | 'pattern'
  /** Condition parameters */
  params: SignalConditionParams
}

/**
 * Parameters for signal conditions
 */
export interface SignalConditionParams {
  /** Threshold value for threshold conditions */
  threshold?: number
  /** Comparison operator */
  operator?: 'gt' | 'lt' | 'gte' | 'lte' | 'eq' | 'ne'
  /** Reference value for crossover conditions */
  reference?: number | 'sma' | 'ema' | 'price' | 'upper_band' | 'lower_band' | 'middle_band'
  /** Direction for crossover conditions */
  direction?: 'above' | 'below' | 'cross_above' | 'cross_below'
  /** Lookback period for pattern conditions */
  lookback?: number
  /** Additional parameters */
  [key: string]: unknown
}

/**
 * Generated trading signal with metadata
 */
export interface GeneratedSignal {
  /** Signal type */
  signal: TradingSignal
  /** Signal confidence (0-1) */
  confidence: number
  /** Timestamp when signal was generated */
  timestamp: number
  /** Strategy that generated the signal */
  strategy: string
  /** Contributing indicators and their values */
  indicators: IndicatorContribution[]
  /** Additional metadata */
  metadata?: SignalMetadata
}

/**
 * Indicator contribution to signal generation
 */
export interface IndicatorContribution {
  /** Indicator name */
  name: string
  /** Indicator value at signal generation */
  value: number
  /** Individual signal from this indicator */
  signal: TradingSignal
  /** Confidence from this indicator */
  confidence: number
  /** Weight applied to this indicator */
  weight?: number
}

/**
 * Additional signal metadata
 */
export interface SignalMetadata {
  /** Market conditions at signal generation */
  marketConditions?: string
  /** Risk assessment */
  riskLevel?: 'low' | 'medium' | 'high'
  /** Expected duration */
  expectedDuration?: number
  /** Additional notes */
  notes?: string
  /** Custom properties */
  [key: string]: unknown
}

/**
 * Strategy analysis result
 */
export interface StrategyAnalysisResult {
  /** Generated signal */
  signal: GeneratedSignal
  /** Analysis details */
  analysis: StrategyAnalysis
  /** Performance metrics */
  performance?: StrategyPerformanceMetrics
}

/**
 * Strategy analysis details
 */
export interface StrategyAnalysis {
  /** Individual indicator analyses */
  indicatorAnalyses: IndicatorAnalysis[]
  /** Combination logic applied */
  combinationLogic: StrategyCombinationLogic
  /** Final decision reasoning */
  reasoning: string
  /** Warnings or notes */
  warnings?: string[]
}

/**
 * Individual indicator analysis
 */
export interface IndicatorAnalysis {
  /** Indicator name */
  indicator: string
  /** Current value */
  currentValue: number
  /** Previous value */
  previousValue?: number
  /** Signal generated */
  signal: TradingSignal
  /** Confidence level */
  confidence: number
  /** Conditions that were met */
  metConditions: string[]
  /** Analysis notes */
  notes?: string
}

/**
 * Strategy performance metrics
 */
export interface StrategyPerformanceMetrics {
  /** Total signals generated */
  totalSignals: number
  /** Buy signals count */
  buySignals: number
  /** Sell signals count */
  sellSignals: number
  /** Hold signals count */
  holdSignals: number
  /** Average confidence */
  averageConfidence: number
  /** Success rate (if backtesting data available) */
  successRate?: number
  /** Last updated timestamp */
  lastUpdated: number
}

/**
 * Strategy factory configuration
 */
export interface StrategyFactoryConfig {
  /** Default strategy configuration */
  defaultStrategyConfig: Partial<BaseStrategyConfig>
  /** Available indicators */
  availableIndicators: string[]
  /** Maximum number of indicators per strategy */
  maxIndicatorsPerStrategy: number
  /** Enable performance tracking */
  enablePerformanceTracking: boolean
  /** Enable validation */
  enableValidation: boolean
}

/**
 * Signal engine configuration
 */
export interface SignalEngineConfig {
  /** Default signal generation settings */
  defaultSignalConfig: Partial<BaseStrategyConfig>
  /** Maximum number of active strategies */
  maxActiveStrategies: number
  /** Signal history retention period (ms) */
  signalHistoryRetention: number
  /** Enable real-time processing */
  enableRealTimeProcessing: boolean
  /** Performance monitoring settings */
  performanceMonitoring: boolean
}

/**
 * Strategy instance interface
 */
export interface Strategy {
  /** Strategy configuration */
  readonly config: StrategyConfig
  /** Strategy name */
  readonly name: string
  /** Whether strategy is ready for analysis */
  readonly isReady: boolean
  /** Associated indicators */
  readonly indicators: Map<string, BaseIndicator>
  /** Signal history */
  readonly signalHistory: GeneratedSignal[]

  /**
   * Analyze market data and generate signal
   * @param data - Market data point
   * @returns Strategy analysis result
   */
  analyze(data: IndicatorDataPoint): Promise<StrategyAnalysisResult>

  /**
   * Add market data for analysis
   * @param data - Market data point
   * @returns Generated signal or null if not ready
   */
  addData(data: IndicatorDataPoint): GeneratedSignal | null

  /**
   * Get latest signal
   * @returns Latest generated signal or null
   */
  getLatestSignal(): GeneratedSignal | null

  /**
   * Get performance metrics
   * @returns Strategy performance metrics
   */
  getPerformanceMetrics(): StrategyPerformanceMetrics

  /**
   * Reset strategy state
   */
  reset(): void

  /**
   * Dispose of strategy resources
   */
  dispose(): void
}

/**
 * Strategy factory interface
 */
export interface StrategyFactory {
  /**
   * Create a single indicator strategy
   * @param indicator - Indicator name
   * @param config - Strategy configuration
   * @returns Created strategy instance
   */
  createSingleIndicatorStrategy(
    indicator: string,
    config: Partial<SingleIndicatorStrategyConfig>
  ): Strategy

  /**
   * Create a multi-indicator strategy
   * @param indicators - Array of indicator names
   * @param config - Strategy configuration
   * @returns Created strategy instance
   */
  createMultiIndicatorStrategy(
    indicators: string[],
    config: Partial<MultiIndicatorStrategyConfig>
  ): Strategy

  /**
   * Get available indicators
   * @returns Array of available indicator names
   */
  getAvailableIndicators(): string[]

  /**
   * Validate strategy configuration
   * @param config - Strategy configuration to validate
   * @returns Validation result
   */
  validateStrategyConfig(config: StrategyConfig): StrategyValidationResult
}

/**
 * Strategy validation result
 */
export interface StrategyValidationResult {
  /** Whether configuration is valid */
  isValid: boolean
  /** Validation errors */
  errors: string[]
  /** Validation warnings */
  warnings: string[]
}

/**
 * Signal engine interface
 */
export interface SignalEngine {
  /**
   * Create a strategy using flexible API
   * @param indicators - Single indicator name or array of indicator names
   * @param config - Strategy configuration
   * @returns Created strategy instance
   */
  createStrategy(indicators: string | string[], config: Record<string, unknown>): Strategy

  /**
   * Get active strategies
   * @returns Array of active strategy instances
   */
  getActiveStrategies(): Strategy[]

  /**
   * Remove a strategy
   * @param strategyName - Name of strategy to remove
   * @returns Whether strategy was removed
   */
  removeStrategy(strategyName: string): boolean

  /**
   * Process market data through all active strategies
   * @param data - Market data point
   * @returns Array of generated signals
   */
  processMarketData(data: IndicatorDataPoint): GeneratedSignal[]

  /**
   * Get signal history
   * @param strategyName - Optional strategy name filter
   * @returns Array of historical signals
   */
  getSignalHistory(strategyName?: string): GeneratedSignal[]

  /**
   * Clear signal history
   * @param strategyName - Optional strategy name filter
   */
  clearSignalHistory(strategyName?: string): void

  /**
   * Get engine performance metrics
   * @returns Engine performance metrics
   */
  getPerformanceMetrics(): SignalEnginePerformanceMetrics
}

/**
 * Signal engine performance metrics
 */
export interface SignalEnginePerformanceMetrics {
  /** Total strategies created */
  totalStrategies: number
  /** Active strategies count */
  activeStrategies: number
  /** Total signals generated */
  totalSignals: number
  /** Signals per strategy */
  signalsPerStrategy: Record<string, number>
  /** Average processing time (ms) */
  averageProcessingTime: number
  /** Last processing timestamp */
  lastProcessingTime: number
  /** Memory usage statistics */
  memoryUsage: {
    signalHistory: number
    strategies: number
    indicators: number
  }
}

/**
 * Market data processing types for signal generation
 */
export interface MarketDataPoint {
  symbol: string
  timestamp: number
  price: number
  volume?: number
  source: 'stream' | 'history' | 'period'
}

export interface OHLCMarketData {
  symbol: string
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume?: number
  source: 'candle' | 'period'
}

/**
 * Market data processor configuration
 */
export interface MarketDataProcessorConfig {
  /** Minimum confidence threshold for signal generation (0-1) */
  minConfidenceThreshold: number
  /** Maximum number of data points to keep in memory */
  maxHistorySize: number
  /** Whether to enable real-time signal generation */
  enableRealTimeSignals: boolean
  /** Symbols to process for signal generation */
  watchedSymbols: string[]
  /** Signal generation interval in milliseconds */
  signalGenerationInterval: number
}

/**
 * Signal generation result with comprehensive metadata
 */
export interface SignalGenerationResult {
  signal: GeneratedSignal
  marketData: MarketDataPoint | OHLCMarketData
  processingTime: number
  confidence: number
  indicators: IndicatorContribution[]
  reasoning: string
}

/**
 * Market data processor interface
 */
export interface MarketDataProcessor {
  /** Initialize the processor with configuration */
  initialize(config: MarketDataProcessorConfig): Promise<void>
  /** Process streaming market data */
  processStreamData(data: StreamData): Promise<SignalGenerationResult[]>
  /** Process historical market data */
  processHistoryData(data: HistoryNewFastData): Promise<void>
  /** Process period market data */
  processPeriodData(data: HistoryPeriodData): Promise<void>
  /** Get current processor status */
  getStatus(): MarketDataProcessorStatus
  /** Cleanup and shutdown */
  shutdown(): Promise<void>
}

/**
 * Market data processor status
 */
export interface MarketDataProcessorStatus {
  isInitialized: boolean
  isProcessing: boolean
  dataPointsProcessed: number
  signalsGenerated: number
  lastProcessingTime: number
  activeSymbols: string[]
  memoryUsage: number
}
