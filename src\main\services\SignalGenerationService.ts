/**
 * Signal Generation Service
 * Comprehensive service that orchestrates real-time signal generation
 * using multiple strategies and market data processing
 */

import { logger } from '../../shared/utils/logger'
import { MarketDataProcessor } from './MarketDataProcessor'
import { SignalEngine } from '../core/SignalEngine'
import {
  AdvancedMultiIndicatorStrategy,
  type MarketAnalysisData
} from '../strategies/MultiIndicatorStrategy'
import { SignalEngineEvents } from '../events/SignalEngineEventEmitter'
import { RelativeStrengthIndex, SimpleMovingAverage, BollingerBands } from '../indicators'
import type {
  GeneratedSignal,
  MarketDataPoint,
  OHLCMarketData,
  SignalGenerationResult
} from '../../shared/types/signals'
import type { IndicatorDataPoint } from '../../shared/types/indicators'

/**
 * Signal generation configuration
 */
export interface SignalGenerationConfig {
  /** Minimum confidence threshold for signal output */
  minConfidenceThreshold: number
  /** Enable advanced multi-indicator strategy */
  enableAdvancedStrategy: boolean
  /** Enable console logging of signals */
  enableSignalLogging: boolean
  /** Maximum signals per symbol per hour */
  maxSignalsPerSymbolPerHour: number
  /** Symbols to generate signals for */
  watchedSymbols: string[]
  /** Indicator configurations */
  indicators: {
    rsi: { period: number; overboughtThreshold: number; oversoldThreshold: number }
    sma: { fastPeriod: number; slowPeriod: number }
    bb: { period: number; standardDeviations: number }
  }
}

/**
 * Signal generation statistics
 */
export interface SignalGenerationStats {
  totalSignalsGenerated: number
  signalsByType: Record<'BUY' | 'SELL' | 'HOLD', number>
  signalsBySymbol: Record<string, number>
  averageConfidence: number
  lastSignalTime: number
  processingTimeMs: number
}

/**
 * Default configuration
 */
const DEFAULT_CONFIG: SignalGenerationConfig = {
  minConfidenceThreshold: 0.7,
  enableAdvancedStrategy: true,
  enableSignalLogging: true,
  maxSignalsPerSymbolPerHour: 10,
  watchedSymbols: [],
  indicators: {
    rsi: { period: 14, overboughtThreshold: 70, oversoldThreshold: 30 },
    sma: { fastPeriod: 10, slowPeriod: 20 },
    bb: { period: 20, standardDeviations: 2 }
  }
}

/**
 * Signal Generation Service - Singleton
 */
export class SignalGenerationService {
  private static instance: SignalGenerationService | null = null
  private config: SignalGenerationConfig = DEFAULT_CONFIG
  private marketDataProcessor: MarketDataProcessor | null = null
  private signalEngine: SignalEngine | null = null
  private advancedStrategy: AdvancedMultiIndicatorStrategy | null = null
  private isInitialized: boolean = false
  private isRunning: boolean = false

  // Indicators for real-time analysis
  private indicators: Map<
    string,
    {
      rsi: RelativeStrengthIndex
      smaFast: SimpleMovingAverage
      smaSlow: SimpleMovingAverage
      bb: BollingerBands
    }
  > = new Map()

  // Statistics tracking
  private stats: SignalGenerationStats = {
    totalSignalsGenerated: 0,
    signalsByType: { BUY: 0, SELL: 0, HOLD: 0 },
    signalsBySymbol: {},
    averageConfidence: 0,
    lastSignalTime: 0,
    processingTimeMs: 0
  }

  // Signal history for filtering
  private signalHistory: Map<string, Array<{ timestamp: number; signal: string }>> = new Map()

  /**
   * Get singleton instance
   */
  public static getInstance(): SignalGenerationService {
    if (!SignalGenerationService.instance) {
      SignalGenerationService.instance = new SignalGenerationService()
    }
    return SignalGenerationService.instance
  }

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    logger.info('SignalGenerationService', 'SignalGenerationService instance created')
  }

  /**
   * Initialize the signal generation service
   */
  public async initialize(config: Partial<SignalGenerationConfig> = {}): Promise<void> {
    try {
      this.config = { ...DEFAULT_CONFIG, ...config }

      // Initialize MarketDataProcessor
      this.marketDataProcessor = MarketDataProcessor.getInstance()
      if (!this.marketDataProcessor.getStatus().isInitialized) {
        await this.marketDataProcessor.initialize({
          minConfidenceThreshold: this.config.minConfidenceThreshold,
          maxHistorySize: 1000,
          enableRealTimeSignals: true,
          watchedSymbols: this.config.watchedSymbols,
          signalGenerationInterval: 2000
        })
      }

      // Initialize SignalEngine
      this.signalEngine = SignalEngine.getInstance()
      if (!this.signalEngine.isInitialized) {
        await this.signalEngine.initialize()
      }

      // Initialize Advanced Multi-Indicator Strategy
      if (this.config.enableAdvancedStrategy) {
        this.advancedStrategy = new AdvancedMultiIndicatorStrategy({
          rsiConfig: this.config.indicators.rsi,
          bbConfig: this.config.indicators.bb,
          smaConfig: this.config.indicators.sma,
          combinationLogic: 'WEIGHTED',
          minConfidenceThreshold: this.config.minConfidenceThreshold
        })
      }

      this.isInitialized = true
      logger.success(
        'SignalGenerationService',
        'SignalGenerationService initialized successfully',
        {
          config: this.config,
          advancedStrategyEnabled: !!this.advancedStrategy
        }
      )
    } catch (error) {
      logger.error(
        'SignalGenerationService',
        'Failed to initialize SignalGenerationService:',
        error
      )
      throw error
    }
  }

  /**
   * Start signal generation
   */
  public async start(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('SignalGenerationService must be initialized before starting')
    }

    this.isRunning = true
    logger.info('SignalGenerationService', 'Signal generation started')
  }

  /**
   * Stop signal generation
   */
  public async stop(): Promise<void> {
    this.isRunning = false
    logger.info('SignalGenerationService', 'Signal generation stopped')
  }

  /**
   * Process market data and generate signals
   */
  public async processMarketData(
    data: MarketDataPoint | OHLCMarketData
  ): Promise<GeneratedSignal[]> {
    if (!this.isInitialized || !this.isRunning) {
      return []
    }

    const startTime = Date.now()
    const signals: GeneratedSignal[] = []

    try {
      const symbol = data.symbol

      // Initialize indicators for this symbol if not exists
      if (!this.indicators.has(symbol)) {
        this.initializeIndicatorsForSymbol(symbol)
      }

      const symbolIndicators = this.indicators.get(symbol)!

      // Convert market data to indicator data point
      const indicatorData: IndicatorDataPoint = {
        value: 'price' in data ? data.price : data.close,
        timestamp: data.timestamp,
        volume: data.volume
      }

      // Update indicators
      const rsiResult = symbolIndicators.rsi.addData(indicatorData)
      const smaFastResult = symbolIndicators.smaFast.addData(indicatorData)
      const smaSlowResult = symbolIndicators.smaSlow.addData(indicatorData)
      const bbResult = symbolIndicators.bb.addData(indicatorData)

      // Check if all indicators are ready
      if (!rsiResult || !smaFastResult || !smaSlowResult || !bbResult) {
        return []
      }

      // Prepare market analysis data for advanced strategy
      if (this.advancedStrategy) {
        const analysisData: MarketAnalysisData = {
          currentPrice: indicatorData.value,
          rsi: rsiResult.value,
          previousRSI: symbolIndicators.rsi
            .getValues()
            .slice(-5)
            .map((v) => v.value),
          smaFast: smaFastResult.value,
          smaSlow: smaSlowResult.value,
          previousSMAFast:
            symbolIndicators.smaFast.getValues().slice(-2, -1)[0]?.value || smaFastResult.value,
          previousSMASlow:
            symbolIndicators.smaSlow.getValues().slice(-2, -1)[0]?.value || smaSlowResult.value,
          bbUpper: bbResult.upperBand,
          bbLower: bbResult.lowerBand,
          bbMiddle: bbResult.middleBand,
          bbWidth: bbResult.upperBand - bbResult.lowerBand,
          volume: data.volume,
          volatility: this.calculateVolatility(symbol)
        }

        // Generate signal using advanced strategy
        const advancedSignal = this.advancedStrategy.generateSignal(analysisData)
        if (advancedSignal && this.passesSignalFiltering(symbol, advancedSignal)) {
          signals.push(advancedSignal)
          this.updateStatistics(advancedSignal, symbol)
          this.logSignal(advancedSignal, data)
          this.addToSignalHistory(symbol, advancedSignal)
        }
      }

      // Also process through SignalEngine for additional strategies
      const engineSignals = this.signalEngine!.processMarketData(indicatorData)
      for (const signal of engineSignals) {
        if (
          signal.confidence >= this.config.minConfidenceThreshold &&
          this.passesSignalFiltering(symbol, signal)
        ) {
          signals.push(signal)
          this.updateStatistics(signal, symbol)
          this.logSignal(signal, data)
          this.addToSignalHistory(symbol, signal)
        }
      }

      // Update processing time
      this.stats.processingTimeMs = Date.now() - startTime
    } catch (error) {
      logger.error('SignalGenerationService', 'Error processing market data:', error)
    }

    return signals
  }

  /**
   * Get current statistics
   */
  public getStatistics(): SignalGenerationStats {
    return { ...this.stats }
  }

  /**
   * Get service status
   */
  public getStatus(): {
    isInitialized: boolean
    isRunning: boolean
    activeSymbols: string[]
    totalIndicators: number
    advancedStrategyEnabled: boolean
  } {
    return {
      isInitialized: this.isInitialized,
      isRunning: this.isRunning,
      activeSymbols: Array.from(this.indicators.keys()),
      totalIndicators: this.indicators.size * 4, // 4 indicators per symbol
      advancedStrategyEnabled: !!this.advancedStrategy
    }
  }

  /**
   * Shutdown the service
   */
  public async shutdown(): Promise<void> {
    try {
      this.isRunning = false
      this.isInitialized = false
      this.indicators.clear()
      this.signalHistory.clear()

      if (this.marketDataProcessor) {
        await this.marketDataProcessor.shutdown()
      }

      logger.info('SignalGenerationService', 'SignalGenerationService shutdown completed')
    } catch (error) {
      logger.error('SignalGenerationService', 'Error during shutdown:', error)
    }
  }

  /**
   * Initialize indicators for a new symbol
   */
  private initializeIndicatorsForSymbol(symbol: string): void {
    const rsi = new RelativeStrengthIndex({
      period: this.config.indicators.rsi.period,
      overboughtThreshold: this.config.indicators.rsi.overboughtThreshold,
      oversoldThreshold: this.config.indicators.rsi.oversoldThreshold
    })

    const smaFast = new SimpleMovingAverage({
      period: this.config.indicators.sma.fastPeriod
    })

    const smaSlow = new SimpleMovingAverage({
      period: this.config.indicators.sma.slowPeriod
    })

    const bb = new BollingerBands({
      period: this.config.indicators.bb.period,
      standardDeviations: this.config.indicators.bb.standardDeviations
    })

    this.indicators.set(symbol, { rsi, smaFast, smaSlow, bb })
    logger.debug('SignalGenerationService', `Initialized indicators for symbol: ${symbol}`)
  }

  /**
   * Check if signal passes filtering rules
   */
  private passesSignalFiltering(symbol: string, signal: GeneratedSignal): boolean {
    // Check maximum signals per symbol per hour
    const oneHourAgo = Date.now() - 60 * 60 * 1000
    const symbolHistory = this.signalHistory.get(symbol) || []
    const recentSignals = symbolHistory.filter((s) => s.timestamp > oneHourAgo)

    if (recentSignals.length >= this.config.maxSignalsPerSymbolPerHour) {
      logger.debug(
        'SignalGenerationService',
        `Signal filtered for ${symbol}: Max signals per hour reached`
      )
      return false
    }

    // Check if we should process this symbol
    if (this.config.watchedSymbols.length > 0 && !this.config.watchedSymbols.includes(symbol)) {
      return false
    }

    return true
  }

  /**
   * Add signal to history for filtering
   */
  private addToSignalHistory(symbol: string, signal: GeneratedSignal): void {
    if (!this.signalHistory.has(symbol)) {
      this.signalHistory.set(symbol, [])
    }

    const history = this.signalHistory.get(symbol)!
    history.push({
      timestamp: signal.timestamp,
      signal: signal.signal
    })

    // Keep only last 24 hours of history
    const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000
    this.signalHistory.set(
      symbol,
      history.filter((s) => s.timestamp > oneDayAgo)
    )
  }

  /**
   * Update statistics
   */
  private updateStatistics(signal: GeneratedSignal, symbol: string): void {
    this.stats.totalSignalsGenerated++
    this.stats.signalsByType[signal.signal]++
    this.stats.signalsBySymbol[symbol] = (this.stats.signalsBySymbol[symbol] || 0) + 1
    this.stats.lastSignalTime = signal.timestamp

    // Update average confidence
    const totalConfidence =
      this.stats.averageConfidence * (this.stats.totalSignalsGenerated - 1) + signal.confidence
    this.stats.averageConfidence = totalConfidence / this.stats.totalSignalsGenerated
  }

  /**
   * Log signal with specified format
   */
  private logSignal(signal: GeneratedSignal, marketData: MarketDataPoint | OHLCMarketData): void {
    if (!this.config.enableSignalLogging) {
      return
    }

    const price = 'price' in marketData ? marketData.price : marketData.close
    const timestamp = new Date(marketData.timestamp).toISOString()
    const indicators = signal.indicators?.map((ind) => ind.indicator).join(', ') || 'N/A'

    logger.trade(
      `🎯 SIGNAL: ${signal.signal} | ` +
        `Confidence: ${(signal.confidence * 100).toFixed(1)}% | ` +
        `Symbol: ${marketData.symbol} | ` +
        `Price: ${price.toFixed(4)} | ` +
        `Strategy: ${signal.strategy} | ` +
        `Indicators: ${indicators} | ` +
        `Timestamp: ${timestamp}`
    )

    // Broadcast signal event
    SignalEngineEvents.signalGenerated(signal, {
      marketData,
      processingTime: this.stats.processingTimeMs,
      source: 'SignalGenerationService'
    })
  }

  /**
   * Calculate volatility for a symbol (simplified)
   */
  private calculateVolatility(symbol: string): number {
    const symbolIndicators = this.indicators.get(symbol)
    if (!symbolIndicators) {
      return 0
    }

    const bbValues = symbolIndicators.bb.getValues()
    if (bbValues.length < 2) {
      return 0
    }

    const latestBB = bbValues[bbValues.length - 1]
    return (latestBB.upperBand - latestBB.lowerBand) / latestBB.middleBand
  }
}
