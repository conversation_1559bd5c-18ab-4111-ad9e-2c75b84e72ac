/**
 * Broker Service Constants
 * Contains all constants, error messages, and configuration values for the broker service
 */

import type { BrokerServiceConfig } from '../interfaces/broker'

/**
 * IPC channel names for broker operations
 */
export const IPC_CHANNELS = {
  /** Trading bot start command */
  TRADING_BOT_START: 'trading-bot:start',
  /** Trading bot stop command */
  TRADING_BOT_STOP: 'trading-bot:stop',
  /** Trading bot status query */
  TRADING_BOT_STATUS: 'trading-bot:status',
  /** Broker connect command */
  BROKER_CONNECT: 'broker:connect',
  /** Broker disconnect command */
  BROKER_DISCONNECT: 'broker:disconnect',
  /** Broker event channel */
  BROKER_EVENT: 'broker:event'
} as const

/**
 * Broker event names
 */
export const BROKER_EVENTS = {
  /** Broker connected event */
  CONNECTED: 'connected',
  /** Broker disconnected event */
  DISCONNECTED: 'disconnected',
  /** Error event */
  ERROR: 'error',
  /** Balance update event */
  BALANCE: 'balance',
  /** Bot started event */
  BOT_STARTED: 'bot_started',
  /** Bot stopped event */
  BOT_STOPPED: 'bot_stopped',
  /** Connection state change event */
  STATE_CHANGE: 'state_change',
  /** Heartbeat health change event */
  HEARTBEAT_HEALTH_CHANGE: 'heartbeat_health_change',
  /** Heartbeat sent event */
  HEARTBEAT_SENT: 'heartbeat_sent',
  /** Heartbeat received event */
  HEARTBEAT_RECEIVED: 'heartbeat_received',
  /** Heartbeat failed event */
  HEARTBEAT_FAILED: 'heartbeat_failed'
} as const

/**
 * Error messages for broker service operations
 */
export const ERROR_MESSAGES = {
  /** Invalid configuration error */
  INVALID_CONFIG: 'Invalid trading bot configuration',
  /** Invalid response format error */
  INVALID_RESPONSE: 'Invalid response format from main process',
  /** Generic start failure */
  START_FAILED: 'Failed to start trading bot',
  /** Generic stop failure */
  STOP_FAILED: 'Failed to stop trading bot',
  /** Status query failure */
  STATUS_FAILED: 'Failed to get broker status',
  /** Connect failure */
  CONNECT_FAILED: 'Failed to connect broker',
  /** Disconnect failure */
  DISCONNECT_FAILED: 'Failed to disconnect broker',
  /** Invalid status data */
  INVALID_STATUS_DATA: 'Invalid broker status data format',
  /** Unknown error fallback */
  UNKNOWN_ERROR: 'Unknown error',
  /** Event handler registration failure */
  EVENT_HANDLER_FAILED: 'Failed to register event handler',
  /** Heartbeat initialization failure */
  HEARTBEAT_INIT_FAILED: 'Failed to initialize heartbeat mechanism',
  /** Reconnection failure */
  RECONNECT_FAILED: 'Failed to reconnect after heartbeat failure'
} as const

/**
 * Default configuration for broker service
 */
export const DEFAULT_BROKER_CONFIG: BrokerServiceConfig = {
  heartbeat: {
    interval: 30000, // 30 seconds
    timeout: 10000, // 10 seconds
    maxMissedBeats: 3,
    enabled: true
  },
  autoReconnect: true,
  maxReconnectAttempts: 5,
  reconnectDelay: 2000 // 2 seconds
} as const

/**
 * Validation constants
 */
export const VALIDATION = {
  /** Maximum allowed balance value */
  MAX_BALANCE: 1000000,
  /** Minimum allowed balance value */
  MIN_BALANCE: 0,
  /** Maximum event data size in bytes */
  MAX_EVENT_SIZE: 1024 * 1024, // 1MB
  /** Timeout for operations in milliseconds */
  OPERATION_TIMEOUT: 30000, // 30 seconds
  /** Maximum number of event listeners per type */
  MAX_EVENT_LISTENERS: 10
} as const

/**
 * Heartbeat status constants
 */
export const HEARTBEAT_STATUS = {
  /** Healthy heartbeat status */
  HEALTHY: 'healthy',
  /** Degraded heartbeat status */
  DEGRADED: 'degraded',
  /** Unhealthy heartbeat status */
  UNHEALTHY: 'unhealthy',
  /** Stopped heartbeat status */
  STOPPED: 'stopped'
} as const

/**
 * Connection state constants
 */
export const CONNECTION_STATES = {
  /** Disconnected state */
  DISCONNECTED: 'disconnected',
  /** Connecting state */
  CONNECTING: 'connecting',
  /** Connected state */
  CONNECTED: 'connected',
  /** Reconnecting state */
  RECONNECTING: 'reconnecting',
  /** Error state */
  ERROR: 'error'
} as const

/**
 * Logging prefixes for consistent log formatting
 */
export const LOG_PREFIXES = {
  /** General broker service prefix */
  BROKER_SERVICE: 'BrokerService:',
  /** Heartbeat mechanism prefix */
  HEARTBEAT: 'BrokerService:Heartbeat:',
  /** Event handling prefix */
  EVENTS: 'BrokerService:Events:',
  /** Reconnection prefix */
  RECONNECT: 'BrokerService:Reconnect:'
} as const

/**
 * Performance monitoring constants
 */
export const PERFORMANCE = {
  /** Debounce delay for rapid events in milliseconds */
  EVENT_DEBOUNCE_DELAY: 100,
  /** Maximum time to wait for IPC response */
  IPC_RESPONSE_TIMEOUT: 15000, // 15 seconds
  /** Batch size for event processing */
  EVENT_BATCH_SIZE: 10,
  /** Memory cleanup interval */
  CLEANUP_INTERVAL: 60000 // 1 minute
} as const
