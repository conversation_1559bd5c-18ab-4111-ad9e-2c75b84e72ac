# Technical Indicators System

A comprehensive, high-performance technical indicators library designed for real-time trading applications with TypeScript support, streaming capabilities, and O(n) optimized calculations.

## Features

- **High Performance**: O(n) complexity algorithms with rolling window optimization
- **Real-time Streaming**: Live data updates with event-driven architecture
- **Type Safety**: Full TypeScript support with comprehensive type definitions
- **Memory Efficient**: Automatic memory management and configurable history limits
- **Extensible**: Plugin-based architecture for custom indicators
- **Error Handling**: Comprehensive validation and error recovery
- **Event System**: Real-time notifications for data updates and calculations

## Quick Start

### Basic Usage

```typescript
import { SimpleMovingAverage, createSMA } from './indicators'

// Create SMA with default configuration (period: 20)
const sma = createSMA({ period: 20 })

// Add data points
const dataPoints = [
  { value: 100, timestamp: Date.now() },
  { value: 102, timestamp: Date.now() + 1000 },
  { value: 98, timestamp: Date.now() + 2000 }
]

// Add data and get results
dataPoints.forEach((point) => {
  const result = sma.addData(point)
  if (result) {
    console.log(`SMA: ${result.value}`)
  }
})
```

### Advanced Configuration

```typescript
import { SimpleMovingAverage } from './indicators'

const sma = new SimpleMovingAverage({
  period: 50,
  enableStreaming: true,
  maxHistorySize: 1000,
  priceType: 'typical', // (high + low + close) / 3
  useSmoothing: true,
  smoothingFactor: 0.1
})

// Set up streaming callbacks
sma.onStreamUpdate((update) => {
  console.log('New SMA value:', update.indicatorValue.value)
})

// Event listeners
sma.on('value-calculated', (event) => {
  console.log('SMA calculated:', event.payload)
})
```

### Batch Processing

```typescript
import { IndicatorUtils } from './indicators'

// Convert price array to data points
const prices = [100, 102, 98, 105, 103, 99, 101]
const dataPoints = IndicatorUtils.pricesToDataPoints(prices)

// Process in batch
const sma = createSMA({ period: 5 })
const results = sma.addDataBatch(dataPoints)

// Extract values
const values = IndicatorUtils.extractValues(results)
console.log('SMA values:', values)
```

### RSI (Relative Strength Index) Usage

```typescript
import { RelativeStrengthIndex, createRSI } from './indicators'

// Create RSI with default configuration (period: 14)
const rsi = createRSI({ period: 14 })

// Add data points
const dataPoints = [
  { value: 44.34, timestamp: Date.now() },
  { value: 44.09, timestamp: Date.now() + 1000 },
  { value: 44.15, timestamp: Date.now() + 2000 }
]

// Add data and get results
dataPoints.forEach((point) => {
  const result = rsi.addData(point)
  if (result) {
    console.log(`RSI: ${result.value.toFixed(2)}`)

    // Check for trading signals
    if (rsi.isOverbought()) {
      console.log('🔴 OVERBOUGHT - Consider selling')
    } else if (rsi.isOversold()) {
      console.log('🟢 OVERSOLD - Consider buying')
    }
  }
})
```

### Advanced RSI Configuration

```typescript
import { RelativeStrengthIndex } from './indicators'

const rsi = new RelativeStrengthIndex({
  period: 21,
  enableStreaming: true,
  maxHistorySize: 1000,
  priceType: 'close',
  smoothingMethod: 'ema', // 'sma' or 'ema'
  overboughtThreshold: 75,
  oversoldThreshold: 25
})

// Set up streaming callbacks
rsi.onStreamUpdate((update) => {
  console.log('New RSI value:', update.indicatorValue.value.toFixed(2))

  // Real-time signal detection
  if (update.indicatorValue.value > 75) {
    console.log('🚨 Overbought alert!')
  } else if (update.indicatorValue.value < 25) {
    console.log('🚨 Oversold alert!')
  }
})

// Event listeners
rsi.on('value-calculated', (event) => {
  console.log('RSI calculated:', event.payload)
})
```

### RSI Trading Strategy Example

```typescript
import { createRSI, IndicatorUtils } from './indicators'

// Create RSI for different timeframes
const rsi14 = createRSI({ period: 14 })
const rsi21 = createRSI({ period: 21 })

// Price data
const prices = [44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89]
const dataPoints = IndicatorUtils.pricesToDataPoints(prices)

// Process data
dataPoints.forEach((point) => {
  const rsi14Result = rsi14.addData(point)
  const rsi21Result = rsi21.addData(point)

  if (rsi14Result && rsi21Result) {
    const rsi14Value = rsi14Result.value
    const rsi21Value = rsi21Result.value

    // Multi-timeframe RSI strategy
    if (rsi14Value < 30 && rsi21Value < 30) {
      console.log('🟢 STRONG BUY signal - Both RSI oversold')
    } else if (rsi14Value > 70 && rsi21Value > 70) {
      console.log('🔴 STRONG SELL signal - Both RSI overbought')
    } else if (rsi14Value > rsi21Value && rsi14Value > 50) {
      console.log('📈 Bullish momentum - RSI14 above RSI21')
    } else if (rsi14Value < rsi21Value && rsi14Value < 50) {
      console.log('📉 Bearish momentum - RSI14 below RSI21')
    }
  }
})
```

## API Reference

### SimpleMovingAverage Class

#### Constructor

```typescript
new SimpleMovingAverage(config?: Partial<SMAConfig>)
```

**Parameters:**

- `config.period` (number): Period for SMA calculation (default: 20)
- `config.enableStreaming` (boolean): Enable real-time streaming (default: true)
- `config.maxHistorySize` (number): Maximum data points to keep (default: 1000)
- `config.priceType` (string): Price type to use ('close', 'open', 'high', 'low', 'typical', 'weighted')
- `config.useSmoothing` (boolean): Apply exponential smoothing (default: false)
- `config.smoothingFactor` (number): Smoothing factor 0-1 (default: 0.1)

#### Methods

##### addData(dataPoint)

Add a single data point and calculate SMA value.

```typescript
const result = sma.addData({ value: 100, timestamp: Date.now() })
if (result) {
  console.log(`SMA: ${result.value} at ${result.timestamp}`)
}
```

##### addDataBatch(dataPoints)

Add multiple data points in batch for better performance.

```typescript
const results = sma.addDataBatch([{ value: 100 }, { value: 102 }, { value: 98 }])
```

##### calculate()

Recalculate SMA for all current data points.

```typescript
const result = sma.calculate()
console.log(`Calculated ${result.values.length} SMA values`)
console.log(`Performance: ${result.metadata.performance?.calculationTime}ms`)
```

##### getCurrentAverage()

Get current SMA value without adding new data.

```typescript
const currentSMA = sma.getCurrentAverage()
if (currentSMA !== null) {
  console.log(`Current SMA: ${currentSMA}`)
}
```

##### reset()

Reset indicator to initial state.

```typescript
sma.reset()
console.log(`Data count after reset: ${sma.dataCount}`) // 0
```

#### Properties

- `name` (string): Indicator name
- `config` (SMAConfig): Current configuration
- `isReady` (boolean): Whether indicator has enough data
- `dataCount` (number): Number of data points
- `latestValue` (IndicatorOutputPoint | null): Latest calculated value

#### Events

```typescript
// Data added
sma.on('data-added', (event) => {
  console.log('Data added:', event.payload)
})

// Value calculated
sma.on('value-calculated', (event) => {
  console.log('New value:', event.payload.outputValue)
})

// Error occurred
sma.on('error-occurred', (event) => {
  console.error('Indicator error:', event.payload.error)
})

// Configuration changed
sma.on('config-changed', (event) => {
  console.log('Config updated:', event.payload.newConfig)
})
```

### RelativeStrengthIndex Class

#### Constructor

```typescript
new RelativeStrengthIndex(config?: Partial<RSIConfig>)
```

**Parameters:**

- `config.period` (number): Period for RSI calculation (default: 14)
- `config.enableStreaming` (boolean): Enable real-time streaming (default: true)
- `config.maxHistorySize` (number): Maximum data points to keep (default: 1000)
- `config.priceType` (string): Price type to use ('close', 'open', 'high', 'low', 'typical', 'weighted')
- `config.smoothingMethod` (string): Smoothing method for averages ('sma', 'ema', default: 'sma')
- `config.overboughtThreshold` (number): Overbought threshold (default: 70)
- `config.oversoldThreshold` (number): Oversold threshold (default: 30)

#### Methods

##### addData(dataPoint)

Add a single data point and calculate RSI value.

```typescript
const result = rsi.addData({ value: 44.34, timestamp: Date.now() })
if (result) {
  console.log(`RSI: ${result.value.toFixed(2)} at ${result.timestamp}`)
}
```

##### getCurrentRSI()

Get current RSI value without adding new data.

```typescript
const currentRSI = rsi.getCurrentRSI()
console.log(`Current RSI: ${currentRSI?.toFixed(2)}`)
```

##### isOverbought() / isOversold()

Check for overbought/oversold conditions.

```typescript
if (rsi.isOverbought()) {
  console.log('RSI indicates overbought condition')
}

if (rsi.isOversold()) {
  console.log('RSI indicates oversold condition')
}
```

##### getCurrentAverageGain() / getCurrentAverageLoss()

Get current average gain and loss values.

```typescript
const avgGain = rsi.getCurrentAverageGain()
const avgLoss = rsi.getCurrentAverageLoss()
console.log(`Avg Gain: ${avgGain?.toFixed(4)}, Avg Loss: ${avgLoss?.toFixed(4)}`)
```

##### getCurrentRS()

Get current Relative Strength (RS) value.

```typescript
const rs = rsi.getCurrentRS()
console.log(`RS: ${rs?.toFixed(4)}`)
```

##### onStreamUpdate(callback)

Enable streaming with callback function.

```typescript
rsi.onStreamUpdate((update) => {
  console.log('RSI Update:', update.indicatorValue.value.toFixed(2))

  if (update.indicatorValue.value > 70) {
    console.log('Overbought signal detected!')
  }
})
```

##### Event Listeners

```typescript
rsi.on('value-calculated', (event) => {
  console.log('RSI calculated:', event.payload.outputValue.value)
})

rsi.on('data-added', (event) => {
  console.log('Data added:', event.payload.dataPoint.value)
})

rsi.on('error-occurred', (event) => {
  console.error('RSI error:', event.payload.error)
})
```

### Factory Functions

#### createSMA(config)

Factory function to create SMA indicator.

```typescript
const sma = createSMA({ period: 30 })
```

#### calculateSMA(prices, period) [Deprecated]

Legacy function for backward compatibility.

```typescript
const values = calculateSMA([100, 102, 98, 105], 3)
// Returns: [100, 101.67, 103]
```

#### createRSI(config)

Factory function to create RSI indicator.

```typescript
const rsi = createRSI({ period: 14, smoothingMethod: 'ema' })
```

#### calculateRSI(prices, period, smoothingMethod) [Deprecated]

Legacy function for backward compatibility.

```typescript
const values = calculateRSI([44, 44.34, 44.09, 44.15, 43.61], 14, 'sma')
// Returns: RSI values array
```

### Utility Functions

#### IndicatorUtils.validateConfig(config)

Validate indicator configuration.

```typescript
const validation = IndicatorUtils.validateConfig({ period: 20 })
if (!validation.isValid) {
  console.error('Config errors:', validation.errors)
}
```

#### IndicatorUtils.pricesToDataPoints(prices, startTimestamp?)

Convert price array to data points.

```typescript
const dataPoints = IndicatorUtils.pricesToDataPoints([100, 102, 98])
```

#### IndicatorUtils.extractValues(outputPoints)

Extract values from output points.

```typescript
const values = IndicatorUtils.extractValues(sma.getValues())
```

#### IndicatorUtils.createDefaultSMA(period)

Create SMA with default configuration.

```typescript
const sma = IndicatorUtils.createDefaultSMA(20)
```

#### IndicatorUtils.createDefaultRSI(period)

Create RSI with default configuration.

```typescript
const rsi = IndicatorUtils.createDefaultRSI(14)
```

### Indicator Registry

```typescript
import { indicatorRegistry } from './indicators'

// Create indicators by name
const sma = indicatorRegistry.create('sma', { period: 20 })
const rsi = indicatorRegistry.create('rsi', { period: 14, smoothingMethod: 'ema' })

// Alternative names also work
const rsi2 = indicatorRegistry.create('relativestrengthindex', { period: 21 })

// Check available indicators
const available = indicatorRegistry.getAvailableIndicators()
console.log('Available indicators:', available)
// Output: ['sma', 'simplemovingaverage', 'rsi', 'relativestrengthindex']

// Register custom indicator
indicatorRegistry.register('custom', (config) => new CustomIndicator(config))
```

## Performance Considerations

### Memory Management

- Automatic cleanup when `maxHistorySize` is exceeded
- Configurable memory limits and garbage collection
- Circular buffer optimization for large datasets

### Calculation Optimization

- O(n) complexity using rolling window technique
- Incremental calculations for real-time updates
- Batch processing for large datasets

### Best Practices

1. **Use appropriate period sizes**: Larger periods require more memory
2. **Enable streaming for real-time applications**: Better performance than polling
3. **Set reasonable history limits**: Prevent memory leaks in long-running applications
4. **Use batch processing**: More efficient for historical data analysis
5. **Monitor performance metrics**: Available in calculation results

## Error Handling

The indicator system provides comprehensive error handling:

```typescript
try {
  const sma = createSMA({ period: -1 }) // Invalid period
} catch (error) {
  console.error('Configuration error:', error.message)
}

// Validation before adding data
const validation = sma.validateInput([{ value: NaN }])
if (!validation.isValid) {
  console.error('Invalid data:', validation.errors)
}
```

## Integration with Trading Bot

The indicator system is designed to integrate seamlessly with the trading bot:

```typescript
import { SimpleMovingAverage } from '../indicators'

class TradingStrategy {
  private sma20: SimpleMovingAverage
  private sma50: SimpleMovingAverage

  constructor() {
    this.sma20 = createSMA({ period: 20, enableStreaming: true })
    this.sma50 = createSMA({ period: 50, enableStreaming: true })

    // Set up crossover detection
    this.sma20.on('value-calculated', () => this.checkCrossover())
  }

  private checkCrossover() {
    const sma20Value = this.sma20.getCurrentAverage()
    const sma50Value = this.sma50.getCurrentAverage()

    if (sma20Value && sma50Value) {
      if (sma20Value > sma50Value) {
        console.log('Golden cross detected - potential buy signal')
      }
    }
  }

  public addPriceData(price: number) {
    const dataPoint = { value: price, timestamp: Date.now() }
    this.sma20.addData(dataPoint)
    this.sma50.addData(dataPoint)
  }
}
```

## Type Definitions

All types are fully documented and exported for use in your applications:

```typescript
import type {
  BaseIndicator,
  SMAIndicator,
  RSIIndicator,
  IndicatorDataPoint,
  IndicatorOutputPoint,
  IndicatorResult,
  SMAConfig,
  RSIConfig
} from './indicators'
```

## Contributing

When adding new indicators:

1. Extend `BaseIndicatorClass`
2. Implement required abstract methods
3. Add comprehensive JSDoc documentation
4. Include unit tests
5. Register in the indicator registry
6. Update this documentation
