/**
 * Backtesting Module Type Definitions
 * Comprehensive types for strategy backtesting, performance analysis, and reporting
 */

import type { GeneratedSignal, Strategy, IndicatorDataPoint } from './signals'

/**
 * Backtesting configuration options
 */
export interface BacktestConfig {
  /** Initial capital for backtesting */
  initialCapital: number
  /** Commission per trade (percentage) */
  commission: number
  /** Slippage per trade (percentage) */
  slippage: number
  /** Maximum position size (percentage of capital) */
  maxPositionSize: number
  /** Risk management settings */
  riskManagement: RiskManagementConfig
  /** Execution settings */
  execution: ExecutionConfig
}

/**
 * Risk management configuration
 */
export interface RiskManagementConfig {
  /** Stop loss percentage */
  stopLoss?: number
  /** Take profit percentage */
  takeProfit?: number
  /** Maximum drawdown percentage */
  maxDrawdown?: number
  /** Position sizing method */
  positionSizing: 'fixed' | 'percentage' | 'kelly' | 'volatility'
  /** Risk per trade (percentage of capital) */
  riskPerTrade: number
}

/**
 * Execution configuration
 */
export interface ExecutionConfig {
  /** Order execution delay (milliseconds) */
  executionDelay: number
  /** Market impact model */
  marketImpact: 'none' | 'linear' | 'square_root'
  /** Fill probability model */
  fillProbability: number
  /** Partial fill handling */
  allowPartialFills: boolean
}

/**
 * Individual trade record
 */
export interface TradeRecord {
  /** Unique trade identifier */
  id: string
  /** Entry signal that triggered the trade */
  entrySignal: GeneratedSignal
  /** Exit signal that closed the trade */
  exitSignal?: GeneratedSignal
  /** Entry timestamp */
  entryTime: number
  /** Exit timestamp */
  exitTime?: number
  /** Entry price */
  entryPrice: number
  /** Exit price */
  exitPrice?: number
  /** Trade direction */
  direction: 'LONG' | 'SHORT'
  /** Position size */
  size: number
  /** Trade status */
  status: 'OPEN' | 'CLOSED' | 'CANCELLED'
  /** Profit/Loss (absolute) */
  pnl?: number
  /** Profit/Loss (percentage) */
  pnlPercentage?: number
  /** Commission paid */
  commission: number
  /** Slippage cost */
  slippage: number
  /** Trade duration (milliseconds) */
  duration?: number
  /** Maximum favorable excursion */
  mfe?: number
  /** Maximum adverse excursion */
  mae?: number
  /** Trade metadata */
  metadata?: Record<string, unknown>
}

/**
 * Portfolio state at a point in time
 */
export interface PortfolioSnapshot {
  /** Timestamp of snapshot */
  timestamp: number
  /** Total portfolio value */
  totalValue: number
  /** Cash available */
  cash: number
  /** Value of open positions */
  positionValue: number
  /** Number of open positions */
  openPositions: number
  /** Current drawdown */
  drawdown: number
  /** Maximum drawdown so far */
  maxDrawdown: number
  /** Total return percentage */
  totalReturn: number
  /** Sharpe ratio */
  sharpeRatio?: number
  /** Volatility */
  volatility?: number
}

/**
 * Comprehensive backtesting performance metrics
 */
export interface BacktestPerformanceMetrics {
  /** Total return percentage */
  totalReturn: number
  /** Annualized return percentage */
  annualizedReturn: number
  /** Maximum drawdown percentage */
  maxDrawdown: number
  /** Sharpe ratio */
  sharpeRatio: number
  /** Sortino ratio */
  sortinoRatio: number
  /** Calmar ratio */
  calmarRatio: number
  /** Win rate percentage */
  winRate: number
  /** Profit factor */
  profitFactor: number
  /** Average win percentage */
  averageWin: number
  /** Average loss percentage */
  averageLoss: number
  /** Largest win percentage */
  largestWin: number
  /** Largest loss percentage */
  largestLoss: number
  /** Total number of trades */
  totalTrades: number
  /** Number of winning trades */
  winningTrades: number
  /** Number of losing trades */
  losingTrades: number
  /** Average trade duration (hours) */
  averageTradeDuration: number
  /** Volatility (annualized) */
  volatility: number
  /** Beta (if benchmark provided) */
  beta?: number
  /** Alpha (if benchmark provided) */
  alpha?: number
  /** Information ratio (if benchmark provided) */
  informationRatio?: number
  /** Value at Risk (95%) */
  valueAtRisk: number
  /** Expected shortfall */
  expectedShortfall: number
  /** Recovery factor */
  recoveryFactor: number
  /** Ulcer index */
  ulcerIndex: number
}

/**
 * Backtesting result summary
 */
export interface BacktestResult {
  /** Backtest configuration used */
  config: BacktestConfig
  /** Strategy that was tested */
  strategy: Strategy
  /** Performance metrics */
  metrics: BacktestPerformanceMetrics
  /** All trade records */
  trades: TradeRecord[]
  /** Portfolio snapshots over time */
  portfolioHistory: PortfolioSnapshot[]
  /** Equity curve data points */
  equityCurve: { timestamp: number; value: number }[]
  /** Drawdown curve data points */
  drawdownCurve: { timestamp: number; drawdown: number }[]
  /** Monthly returns */
  monthlyReturns: { month: string; return: number }[]
  /** Backtest execution time */
  executionTime: number
  /** Start and end dates */
  startDate: number
  endDate: number
  /** Benchmark comparison (if provided) */
  benchmark?: {
    totalReturn: number
    volatility: number
    sharpeRatio: number
  }
  /** Additional metadata */
  metadata?: Record<string, unknown>
}

/**
 * Backtesting engine interface
 */
export interface BacktestEngine {
  /** Run backtest with given strategy and data */
  runBacktest(
    strategy: Strategy,
    historicalData: IndicatorDataPoint[],
    config: BacktestConfig,
    benchmark?: IndicatorDataPoint[]
  ): Promise<BacktestResult>

  /** Get current portfolio state */
  getPortfolioState(): PortfolioSnapshot

  /** Get all trade records */
  getTrades(): TradeRecord[]

  /** Get performance metrics */
  getMetrics(): BacktestPerformanceMetrics

  /** Reset engine state */
  reset(): void

  /** Add custom metric calculator */
  addCustomMetric(name: string, calculator: (trades: TradeRecord[], portfolio: PortfolioSnapshot[]) => number): void
}

/**
 * Position sizing calculator interface
 */
export interface PositionSizer {
  /** Calculate position size for a trade */
  calculateSize(
    signal: GeneratedSignal,
    currentPrice: number,
    portfolioValue: number,
    config: BacktestConfig
  ): number
}

/**
 * Risk manager interface
 */
export interface RiskManager {
  /** Check if trade should be executed based on risk rules */
  shouldExecuteTrade(
    signal: GeneratedSignal,
    currentPrice: number,
    portfolio: PortfolioSnapshot,
    config: BacktestConfig
  ): boolean

  /** Calculate stop loss and take profit levels */
  calculateExitLevels(
    entryPrice: number,
    direction: 'LONG' | 'SHORT',
    config: RiskManagementConfig
  ): { stopLoss?: number; takeProfit?: number }

  /** Check if position should be closed due to risk management */
  shouldClosePosition(
    trade: TradeRecord,
    currentPrice: number,
    config: RiskManagementConfig
  ): boolean
}

/**
 * Market simulator interface for realistic backtesting
 */
export interface MarketSimulator {
  /** Simulate order execution */
  executeOrder(
    signal: GeneratedSignal,
    currentPrice: number,
    size: number,
    config: ExecutionConfig
  ): {
    executedPrice: number
    executedSize: number
    commission: number
    slippage: number
    executionTime: number
  }

  /** Calculate market impact */
  calculateMarketImpact(
    orderSize: number,
    marketVolume: number,
    config: ExecutionConfig
  ): number
}

/**
 * Backtesting event types
 */
export type BacktestEventType = 
  | 'backtest-started'
  | 'backtest-completed'
  | 'trade-opened'
  | 'trade-closed'
  | 'portfolio-updated'
  | 'risk-limit-hit'
  | 'error-occurred'

/**
 * Backtesting event data
 */
export interface BacktestEvent {
  type: BacktestEventType
  timestamp: number
  data: Record<string, unknown>
}

/**
 * Backtesting event callback
 */
export type BacktestEventCallback = (event: BacktestEvent) => void

/**
 * Default backtesting configuration
 */
export interface DefaultBacktestConfig {
  INITIAL_CAPITAL: number
  COMMISSION: number
  SLIPPAGE: number
  MAX_POSITION_SIZE: number
  RISK_PER_TRADE: number
  EXECUTION_DELAY: number
  FILL_PROBABILITY: number
}

/**
 * Backtesting constants
 */
export interface BacktestConstants {
  DEFAULT_CONFIG: DefaultBacktestConfig
  TRADING_DAYS_PER_YEAR: number
  HOURS_PER_TRADING_DAY: number
  RISK_FREE_RATE: number
  MIN_TRADES_FOR_STATISTICS: number
  PERFORMANCE_CALCULATION: {
    SHARPE_RATIO_PERIODS: number
    VOLATILITY_PERIODS: number
    VAR_CONFIDENCE_LEVEL: number
  }
}

/**
 * Export all types for external use
 */
export type {
  BacktestConfig,
  RiskManagementConfig,
  ExecutionConfig,
  TradeRecord,
  PortfolioSnapshot,
  BacktestPerformanceMetrics,
  BacktestResult,
  BacktestEngine,
  PositionSizer,
  RiskManager,
  MarketSimulator,
  BacktestEvent,
  BacktestEventCallback,
  BacktestEventType,
  DefaultBacktestConfig,
  BacktestConstants
}
