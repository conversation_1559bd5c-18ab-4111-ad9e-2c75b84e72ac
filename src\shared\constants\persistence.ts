/**
 * Signal Persistence System Constants
 * Default configurations, validation constraints, and system settings
 */

import type {
  PersistenceConfig,
  CompressionConfig,
  RetentionConfig,
  BatchConfig
} from '../types/persistence'

/**
 * Default persistence configuration
 */
export const DEFAULT_PERSISTENCE_CONFIG: PersistenceConfig = {
  backend: 'file',
  location: './data/signals',
  compression: {
    enabled: true,
    algorithm: 'gzip',
    level: 6
  },
  retention: {
    maxAge: 365, // 1 year
    maxRecords: 1000000, // 1 million records
    cleanupInterval: 24, // 24 hours
    archiveOldData: true
  },
  backup: {
    enabled: true,
    interval: 168, // 1 week
    maxBackups: 12, // 3 months
    location: './data/backups'
  }
} as const

/**
 * Default compression configuration
 */
export const DEFAULT_COMPRESSION_CONFIG: CompressionConfig = {
  enabled: true,
  algorithm: 'gzip',
  level: 6
} as const

/**
 * Default retention configuration
 */
export const DEFAULT_RETENTION_CONFIG: RetentionConfig = {
  maxAge: 365, // 1 year
  maxRecords: 1000000,
  cleanupInterval: 24, // 24 hours
  archiveOldData: true
} as const

/**
 * Default batch configuration
 */
export const DEFAULT_BATCH_CONFIG: BatchConfig = {
  batchSize: 1000,
  maxConcurrency: 5,
  retry: {
    maxAttempts: 3,
    delay: 1000, // 1 second
    backoffMultiplier: 2
  }
} as const

/**
 * Validation constraints for persistence parameters
 */
export const PERSISTENCE_VALIDATION_CONSTRAINTS = {
  maxAge: {
    min: 1, // 1 day minimum
    max: 3650, // 10 years maximum
    default: 365
  },
  maxRecords: {
    min: 1000,
    max: 100000000, // 100 million
    default: 1000000
  },
  cleanupInterval: {
    min: 1, // 1 hour minimum
    max: 168, // 1 week maximum
    default: 24
  },
  compressionLevel: {
    min: 1,
    max: 9,
    default: 6
  },
  batchSize: {
    min: 10,
    max: 10000,
    default: 1000
  },
  maxConcurrency: {
    min: 1,
    max: 20,
    default: 5
  },
  retryAttempts: {
    min: 1,
    max: 10,
    default: 3
  },
  retryDelay: {
    min: 100, // 100ms minimum
    max: 30000, // 30 seconds maximum
    default: 1000
  }
} as const

/**
 * Storage backend configurations
 */
export const STORAGE_BACKENDS = {
  file: {
    name: 'File System',
    description: 'Store data in local files',
    extensions: ['.json', '.jsonl', '.gz'],
    maxFileSize: 100 * 1024 * 1024, // 100MB
    indexing: true
  },
  database: {
    name: 'Database',
    description: 'Store data in SQL/NoSQL database',
    supportedDatabases: ['sqlite', 'postgresql', 'mongodb'],
    indexing: true,
    transactions: true
  },
  memory: {
    name: 'In-Memory',
    description: 'Store data in memory (volatile)',
    maxSize: 1024 * 1024 * 1024, // 1GB
    indexing: true,
    persistence: false
  },
  cloud: {
    name: 'Cloud Storage',
    description: 'Store data in cloud storage',
    providers: ['aws-s3', 'gcp-storage', 'azure-blob'],
    encryption: true,
    versioning: true
  }
} as const

/**
 * Compression algorithms and their characteristics
 */
export const COMPRESSION_ALGORITHMS = {
  gzip: {
    name: 'GZIP',
    description: 'Standard compression with good balance',
    compressionRatio: 0.3, // Typical compression ratio
    speed: 'medium',
    cpuUsage: 'medium'
  },
  lz4: {
    name: 'LZ4',
    description: 'Fast compression with lower ratio',
    compressionRatio: 0.5,
    speed: 'fast',
    cpuUsage: 'low'
  },
  zstd: {
    name: 'Zstandard',
    description: 'Modern compression with excellent ratio',
    compressionRatio: 0.25,
    speed: 'fast',
    cpuUsage: 'medium'
  }
} as const

/**
 * File format specifications
 */
export const FILE_FORMATS = {
  json: {
    name: 'JSON',
    extension: '.json',
    mimeType: 'application/json',
    humanReadable: true,
    compression: true,
    streaming: false
  },
  jsonl: {
    name: 'JSON Lines',
    extension: '.jsonl',
    mimeType: 'application/x-jsonlines',
    humanReadable: true,
    compression: true,
    streaming: true
  },
  csv: {
    name: 'CSV',
    extension: '.csv',
    mimeType: 'text/csv',
    humanReadable: true,
    compression: true,
    streaming: true
  },
  parquet: {
    name: 'Parquet',
    extension: '.parquet',
    mimeType: 'application/octet-stream',
    humanReadable: false,
    compression: true,
    streaming: false,
    columnar: true
  }
} as const

/**
 * Error messages for persistence operations
 */
export const PERSISTENCE_ERROR_MESSAGES = {
  INVALID_CONFIG: 'Invalid persistence configuration',
  BACKEND_NOT_SUPPORTED: 'Storage backend not supported',
  CONNECTION_FAILED: 'Failed to connect to storage backend',
  SAVE_FAILED: 'Failed to save data',
  LOAD_FAILED: 'Failed to load data',
  DELETE_FAILED: 'Failed to delete data',
  QUERY_INVALID: 'Invalid query parameters',
  COMPRESSION_FAILED: 'Compression operation failed',
  ENCRYPTION_FAILED: 'Encryption operation failed',
  BACKUP_FAILED: 'Backup operation failed',
  RESTORE_FAILED: 'Restore operation failed',
  CLEANUP_FAILED: 'Cleanup operation failed',
  EXPORT_FAILED: 'Export operation failed',
  IMPORT_FAILED: 'Import operation failed',
  INSUFFICIENT_SPACE: 'Insufficient storage space',
  PERMISSION_DENIED: 'Permission denied for storage operation',
  RECORD_NOT_FOUND: 'Record not found',
  DUPLICATE_RECORD: 'Duplicate record exists',
  VALIDATION_FAILED: 'Data validation failed',
  TIMEOUT: 'Operation timed out',
  CONCURRENT_MODIFICATION: 'Concurrent modification detected'
} as const

/**
 * Persistence event types and descriptions
 */
export const PERSISTENCE_EVENTS = {
  'signal-saved': 'Signal has been saved to storage',
  'signal-loaded': 'Signal has been loaded from storage',
  'signal-deleted': 'Signal has been deleted from storage',
  'strategy-saved': 'Strategy has been saved to storage',
  'strategy-loaded': 'Strategy has been loaded from storage',
  'strategy-updated': 'Strategy has been updated in storage',
  'strategy-deleted': 'Strategy has been deleted from storage',
  'cleanup-completed': 'Storage cleanup has been completed',
  'backup-created': 'Backup has been created',
  'error-occurred': 'An error occurred during persistence operation'
} as const

/**
 * Performance optimization settings
 */
export const PERFORMANCE_SETTINGS = {
  caching: {
    enabled: true,
    maxSize: 100 * 1024 * 1024, // 100MB cache
    ttl: 3600, // 1 hour TTL
    strategy: 'lru' // Least Recently Used
  },
  indexing: {
    enabled: true,
    fields: ['timestamp', 'strategy', 'signal', 'confidence'],
    rebuildInterval: 24 * 7 // 1 week
  },
  connection: {
    poolSize: 10,
    timeout: 30000, // 30 seconds
    retryInterval: 5000, // 5 seconds
    maxRetries: 3
  },
  io: {
    bufferSize: 64 * 1024, // 64KB
    flushInterval: 5000, // 5 seconds
    syncWrites: false
  }
} as const

/**
 * Storage quotas and limits
 */
export const STORAGE_LIMITS = {
  maxSignalsPerStrategy: 1000000, // 1 million signals per strategy
  maxStrategies: 10000, // 10,000 strategies
  maxFileSize: 100 * 1024 * 1024, // 100MB per file
  maxTotalSize: 10 * 1024 * 1024 * 1024, // 10GB total
  maxQueryResults: 100000, // 100,000 results per query
  maxBatchSize: 10000, // 10,000 records per batch
  maxConcurrentOperations: 20,
  maxBackups: 50
} as const

/**
 * Data integrity settings
 */
export const INTEGRITY_SETTINGS = {
  checksums: {
    enabled: true,
    algorithm: 'sha256'
  },
  validation: {
    enabled: true,
    strictMode: false,
    schemaValidation: true
  },
  transactions: {
    enabled: true,
    isolationLevel: 'read-committed',
    timeout: 30000 // 30 seconds
  },
  replication: {
    enabled: false,
    replicas: 2,
    syncMode: 'async'
  }
} as const

/**
 * Monitoring and metrics settings
 */
export const MONITORING_SETTINGS = {
  metrics: {
    enabled: true,
    interval: 60000, // 1 minute
    retention: 30 * 24 * 60 * 60 * 1000 // 30 days
  },
  logging: {
    level: 'info',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 10,
    format: 'json'
  },
  alerts: {
    enabled: true,
    thresholds: {
      errorRate: 0.05, // 5% error rate
      responseTime: 5000, // 5 seconds
      storageUsage: 0.9 // 90% storage usage
    }
  }
} as const

/**
 * Default export for convenience
 */
export default {
  CONFIG: DEFAULT_PERSISTENCE_CONFIG,
  COMPRESSION: DEFAULT_COMPRESSION_CONFIG,
  RETENTION: DEFAULT_RETENTION_CONFIG,
  BATCH: DEFAULT_BATCH_CONFIG,
  VALIDATION: PERSISTENCE_VALIDATION_CONSTRAINTS,
  BACKENDS: STORAGE_BACKENDS,
  ALGORITHMS: COMPRESSION_ALGORITHMS,
  FORMATS: FILE_FORMATS,
  ERRORS: PERSISTENCE_ERROR_MESSAGES,
  EVENTS: PERSISTENCE_EVENTS,
  PERFORMANCE: PERFORMANCE_SETTINGS,
  LIMITS: STORAGE_LIMITS,
  INTEGRITY: INTEGRITY_SETTINGS,
  MONITORING: MONITORING_SETTINGS
}
