/**
 * WebSocket utility functions for error handling, validation, and logging
 */

import { logger } from '../../../shared/utils/logger'
import { EVENT_VALIDATION, WEBSOCKET_CONFIG } from '../constants/websocket'
import type {
  BrokerEventData,
  BalanceUpdateData,
  ConnectionStateChangeData,
  HeartbeatEventData,
  ErrorEventData,
  DisconnectEventData
} from '../types/websocket'

/**
 * Logger category for WebSocket events
 */
const LOG_CATEGORY = 'WebSocket' as const

/**
 * Validates event data size to prevent memory issues
 * @param data - Event data to validate
 * @returns True if data size is within limits
 */
export function validateEventDataSize(data: unknown): boolean {
  try {
    const serialized = JSON.stringify(data)
    const sizeInBytes = new Blob([serialized]).size

    if (sizeInBytes > EVENT_VALIDATION.MAX_EVENT_SIZE) {
      logger.warn(LOG_CATEGORY, `Event data size exceeds limit: ${sizeInBytes} bytes`)
      return false
    }

    return true
  } catch (error) {
    logger.error(LOG_CATEGORY, 'Failed to validate event data size', error)
    return false
  }
}

/**
 * Validates balance data
 * @param data - Balance data to validate
 * @returns True if balance data is valid
 */
export function validateBalanceData(data: unknown): data is BalanceUpdateData {
  if (typeof data !== 'object' || data === null) {
    logger.warn(LOG_CATEGORY, 'Invalid balance data: not an object', data)
    return false
  }

  const balanceData = data as BalanceUpdateData

  if (typeof balanceData.balance !== 'number' || !Number.isFinite(balanceData.balance)) {
    logger.warn(LOG_CATEGORY, 'Invalid balance data: balance is not a finite number', data)
    return false
  }

  if (
    balanceData.balance < EVENT_VALIDATION.MIN_BALANCE ||
    balanceData.balance > EVENT_VALIDATION.MAX_BALANCE
  ) {
    logger.warn(LOG_CATEGORY, `Balance out of range: ${balanceData.balance}`, data)
    return false
  }

  return true
}

/**
 * Validates connection state change data
 * @param data - Connection state data to validate
 * @returns True if connection state data is valid
 */
export function validateConnectionStateData(data: unknown): data is ConnectionStateChangeData {
  if (typeof data !== 'object' || data === null) {
    logger.warn(LOG_CATEGORY, 'Invalid connection state data: not an object', data)
    return false
  }

  const stateData = data as ConnectionStateChangeData

  if (typeof stateData.from !== 'string' || typeof stateData.to !== 'string') {
    logger.warn(LOG_CATEGORY, 'Invalid connection state data: from/to not strings', data)
    return false
  }

  if (typeof stateData.timestamp !== 'number' || !Number.isFinite(stateData.timestamp)) {
    logger.warn(LOG_CATEGORY, 'Invalid connection state data: invalid timestamp', data)
    return false
  }

  return true
}

/**
 * Validates heartbeat event data
 * @param data - Heartbeat data to validate
 * @returns True if heartbeat data is valid
 */
export function validateHeartbeatData(data: unknown): data is HeartbeatEventData {
  if (typeof data !== 'object' || data === null) {
    logger.warn(LOG_CATEGORY, 'Invalid heartbeat data: not an object', data)
    return false
  }

  const heartbeatData = data as HeartbeatEventData

  if (typeof heartbeatData.timestamp !== 'number' || !Number.isFinite(heartbeatData.timestamp)) {
    logger.warn(LOG_CATEGORY, 'Invalid heartbeat data: invalid timestamp', data)
    return false
  }

  if (
    typeof heartbeatData.missedBeats !== 'number' ||
    !Number.isInteger(heartbeatData.missedBeats)
  ) {
    logger.warn(LOG_CATEGORY, 'Invalid heartbeat data: invalid missedBeats', data)
    return false
  }

  if (heartbeatData.missedBeats < 0) {
    logger.warn(LOG_CATEGORY, 'Invalid heartbeat data: negative missedBeats', data)
    return false
  }

  return true
}

/**
 * Safely handles WebSocket event errors
 * @param error - Error that occurred
 * @param eventName - Name of the event that caused the error
 * @param data - Event data that caused the error
 */
export function handleWebSocketError(error: unknown, eventName: string, data?: unknown): void {
  const errorMessage = error instanceof Error ? error.message : String(error)
  const errorStack = error instanceof Error ? error.stack : undefined

  logger.error(LOG_CATEGORY, `Error handling event '${eventName}': ${errorMessage}`, {
    eventName,
    data,
    error: errorMessage,
    stack: errorStack
  })
}

/**
 * Logs WebSocket event reception for debugging
 * @param eventName - Name of the received event
 * @param data - Event data
 * @param isValid - Whether the event data is valid
 */
export function logEventReception(eventName: string, data: unknown, isValid: boolean): void {
  if (isValid) {
    logger.debug(LOG_CATEGORY, `Received valid event '${eventName}'`, { eventName, data })
  } else {
    logger.warn(LOG_CATEGORY, `Received invalid event '${eventName}'`, { eventName, data })
  }
}

/**
 * Creates a timeout wrapper for event handlers to prevent hanging
 * @param handler - Event handler function
 * @param timeout - Timeout in milliseconds
 * @returns Wrapped handler with timeout
 */
export function withTimeout<T>(
  handler: (data: T) => void,
  timeout: number = WEBSOCKET_CONFIG.DEFAULT_TIMEOUT
): (data: T) => void {
  return (data: T) => {
    const timeoutId = setTimeout(() => {
      logger.warn(LOG_CATEGORY, `Event handler timeout after ${timeout}ms`)
    }, timeout)

    try {
      handler(data)
    } catch (error) {
      logger.error(LOG_CATEGORY, 'Event handler error', error)
    } finally {
      clearTimeout(timeoutId)
    }
  }
}

/**
 * Creates a retry wrapper for event handlers
 * @param handler - Event handler function
 * @param maxRetries - Maximum number of retries
 * @returns Wrapped handler with retry logic
 */
export function withRetry<T>(
  handler: (data: T) => void,
  maxRetries: number = WEBSOCKET_CONFIG.MAX_RETRIES
): (data: T) => void {
  return (data: T) => {
    let attempts = 0

    const attemptHandler = (): void => {
      try {
        handler(data)
      } catch (error) {
        attempts++

        if (attempts <= maxRetries) {
          logger.warn(
            LOG_CATEGORY,
            `Event handler failed, retrying (${attempts}/${maxRetries})`,
            error
          )
          setTimeout(attemptHandler, 100 * attempts) // Exponential backoff
        } else {
          logger.error(LOG_CATEGORY, `Event handler failed after ${maxRetries} retries`, error)
        }
      }
    }

    attemptHandler()
  }
}

/**
 * Sanitizes event data for logging (removes sensitive information)
 * @param data - Event data to sanitize
 * @returns Sanitized data safe for logging
 */
export function sanitizeEventData(data: unknown): unknown {
  if (typeof data !== 'object' || data === null) {
    return data
  }

  const sanitized = { ...(data as Record<string, unknown>) }

  // Remove potentially sensitive fields
  const sensitiveFields = ['password', 'token', 'session', 'uid', 'auth']
  sensitiveFields.forEach((field) => {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]'
    }
  })

  return sanitized
}
