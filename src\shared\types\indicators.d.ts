/**
 * TypeScript type definitions for Technical Indicators
 * Contains all types and interfaces used by the indicator system
 */

/**
 * Base configuration for all indicators
 */
export interface BaseIndicatorConfig {
  /** Period/length for the indicator calculation */
  period: number
  /** Whether to enable real-time streaming updates */
  enableStreaming?: boolean
  /** Maximum number of values to keep in memory */
  maxHistorySize?: number
  /** Whether to validate input data */
  validateInputs?: boolean
}

/**
 * Input data point for indicator calculations
 */
export interface IndicatorDataPoint {
  /** Price value (typically close price) */
  value: number
  /** Timestamp of the data point */
  timestamp?: number
  /** Optional volume data */
  volume?: number
}

/**
 * Extended data point with OHLC values
 */
export interface OHLCDataPoint extends IndicatorDataPoint {
  /** Open price */
  open?: number
  /** High price */
  high?: number
  /** Low price */
  low?: number
  /** Close price (same as value) */
  close?: number
}

/**
 * Output data point from indicator calculations
 */
export interface IndicatorOutputPoint {
  /** Calculated indicator value */
  value: number
  /** Timestamp of the calculation */
  timestamp: number
  /** Index in the original data series */
  index: number
}

/**
 * Indicator calculation result
 */
export interface IndicatorResult {
  /** Array of calculated values */
  values: IndicatorOutputPoint[]
  /** Indicator metadata */
  metadata: IndicatorMetadata
  /** Whether the calculation was successful */
  success: boolean
  /** Error message if calculation failed */
  error?: string
}

/**
 * Metadata about the indicator calculation
 */
export interface IndicatorMetadata {
  /** Name of the indicator */
  name: string
  /** Configuration used for calculation */
  config: BaseIndicatorConfig
  /** Number of input data points */
  inputCount: number
  /** Number of output values */
  outputCount: number
  /** Calculation timestamp */
  calculatedAt: number
  /** Performance metrics */
  performance?: IndicatorPerformanceMetrics
}

/**
 * Performance metrics for indicator calculations
 */
export interface IndicatorPerformanceMetrics {
  /** Calculation time in milliseconds */
  calculationTime: number
  /** Memory usage in bytes */
  memoryUsage?: number
  /** Number of operations performed */
  operationCount?: number
}

/**
 * Validation result for indicator inputs
 */
export interface IndicatorValidationResult {
  /** Whether the validation passed */
  isValid: boolean
  /** Array of validation errors */
  errors: string[]
  /** Array of validation warnings */
  warnings: string[]
}

/**
 * Configuration specific to Simple Moving Average
 */
export interface SMAConfig extends BaseIndicatorConfig {
  /** Type of price to use for calculation */
  priceType?: 'close' | 'open' | 'high' | 'low' | 'typical' | 'weighted'
  /** Whether to use exponential smoothing */
  useSmoothing?: boolean
  /** Smoothing factor (0-1) */
  smoothingFactor?: number
}

/**
 * Configuration specific to Relative Strength Index (RSI)
 */
export interface RSIConfig extends BaseIndicatorConfig {
  /** Type of price to use for calculation */
  priceType?: 'close' | 'open' | 'high' | 'low' | 'typical' | 'weighted'
  /** Smoothing method for gain/loss averages */
  smoothingMethod?: 'sma' | 'ema'
  /** Overbought threshold (default: 70) */
  overboughtThreshold?: number
  /** Oversold threshold (default: 30) */
  oversoldThreshold?: number
}

/**
 * Configuration specific to Bollinger Bands
 */
export interface BollingerBandsConfig extends BaseIndicatorConfig {
  /** Type of price to use for calculation */
  priceType?: 'close' | 'open' | 'high' | 'low' | 'typical' | 'weighted'
  /** Standard deviation multiplier (default: 2) */
  standardDeviations?: number
  /** Whether to use exponential moving average instead of simple moving average */
  useEMA?: boolean
  /** Smoothing factor for EMA (only used if useEMA is true) */
  smoothingFactor?: number
}

/**
 * Bollinger Bands output point with upper, middle, and lower bands
 */
export interface BollingerBandsOutputPoint extends IndicatorOutputPoint {
  /** Upper band value */
  upperBand: number
  /** Middle band value (moving average) */
  middleBand: number
  /** Lower band value */
  lowerBand: number
  /** Standard deviation used for calculation */
  standardDeviation: number
}

/**
 * Real-time streaming update for indicators
 */
export interface IndicatorStreamUpdate {
  /** New data point */
  dataPoint: IndicatorDataPoint
  /** Updated indicator value */
  indicatorValue: IndicatorOutputPoint
  /** Whether this is a new value or update to existing */
  isNewValue: boolean
  /** Timestamp of the update */
  updateTimestamp: number
}

/**
 * Event types for indicator system
 */
export type IndicatorEventType =
  | 'data-added'
  | 'value-calculated'
  | 'error-occurred'
  | 'config-changed'
  | 'reset'
  | 'stream-started'
  | 'stream-stopped'

/**
 * Event data for indicator system
 */
export interface IndicatorEvent {
  /** Type of event */
  type: IndicatorEventType
  /** Indicator name that triggered the event */
  indicatorName: string
  /** Event payload */
  payload: unknown
  /** Event timestamp */
  timestamp: number
}

/**
 * Callback function for indicator events
 */
export type IndicatorEventCallback = (event: IndicatorEvent) => void

/**
 * Interface for indicator event emitter
 */
export interface IndicatorEventEmitter {
  /** Subscribe to indicator events */
  on(eventType: IndicatorEventType, callback: IndicatorEventCallback): void
  /** Unsubscribe from indicator events */
  off(eventType: IndicatorEventType, callback: IndicatorEventCallback): void
  /** Emit an indicator event */
  emit(event: IndicatorEvent): void
}

/**
 * Base interface that all indicators must implement
 */
export interface BaseIndicator extends IndicatorEventEmitter {
  /** Indicator name */
  readonly name: string
  /** Current configuration */
  readonly config: BaseIndicatorConfig
  /** Whether the indicator is ready for calculations */
  readonly isReady: boolean
  /** Current number of data points */
  readonly dataCount: number
  /** Latest calculated value */
  readonly latestValue: IndicatorOutputPoint | null

  /**
   * Add a single data point
   * @param dataPoint - The data point to add
   * @returns The calculated indicator value or null if not enough data
   */
  addData(dataPoint: IndicatorDataPoint): IndicatorOutputPoint | null

  /**
   * Add multiple data points
   * @param dataPoints - Array of data points to add
   * @returns Array of calculated indicator values
   */
  addDataBatch(dataPoints: IndicatorDataPoint[]): IndicatorOutputPoint[]

  /**
   * Calculate indicator for all current data
   * @returns Complete indicator result
   */
  calculate(): IndicatorResult

  /**
   * Reset the indicator to initial state
   */
  reset(): void

  /**
   * Update indicator configuration
   * @param newConfig - New configuration to apply
   */
  updateConfig(newConfig: Partial<BaseIndicatorConfig>): void

  /**
   * Get all calculated values
   * @returns Array of all indicator values
   */
  getValues(): IndicatorOutputPoint[]

  /**
   * Get the last N calculated values
   * @param count - Number of values to retrieve
   * @returns Array of the last N indicator values
   */
  getLastValues(count: number): IndicatorOutputPoint[]

  /**
   * Validate input data
   * @param dataPoints - Data points to validate
   * @returns Validation result
   */
  validateInput(dataPoints: IndicatorDataPoint[]): IndicatorValidationResult
}

/**
 * Interface specific to Simple Moving Average indicator
 */
export interface SMAIndicator extends BaseIndicator {
  /** SMA-specific configuration */
  readonly config: SMAConfig

  /**
   * Get the current sum of values in the window
   * @returns Current sum
   */
  getCurrentSum(): number

  /**
   * Get the current average without adding new data
   * @returns Current average value
   */
  getCurrentAverage(): number | null
}

/**
 * Interface specific to Relative Strength Index (RSI) indicator
 */
export interface RSIIndicator extends BaseIndicator {
  /** RSI-specific configuration */
  readonly config: RSIConfig

  /**
   * Get the current RSI value without adding new data
   * @returns Current RSI value (0-100)
   */
  getCurrentRSI(): number | null

  /**
   * Check if current RSI indicates overbought condition
   * @returns True if RSI is above overbought threshold
   */
  isOverbought(): boolean

  /**
   * Check if current RSI indicates oversold condition
   * @returns True if RSI is below oversold threshold
   */
  isOversold(): boolean

  /**
   * Get the current average gain
   * @returns Current average gain value
   */
  getCurrentAverageGain(): number | null

  /**
   * Get the current average loss
   * @returns Current average loss value
   */
  getCurrentAverageLoss(): number | null

  /**
   * Get the current Relative Strength (RS) value
   * @returns Current RS value (Average Gain / Average Loss)
   */
  getCurrentRS(): number | null
}

/**
 * Interface specific to Bollinger Bands indicator
 */
export interface BollingerBandsIndicator extends BaseIndicator {
  /** Bollinger Bands-specific configuration */
  readonly config: BollingerBandsConfig

  /**
   * Get the current Bollinger Bands values without adding new data
   * @returns Current Bollinger Bands values or null if not enough data
   */
  getCurrentBands(): BollingerBandsOutputPoint | null

  /**
   * Get the current middle band (moving average) value
   * @returns Current middle band value
   */
  getCurrentMiddleBand(): number | null

  /**
   * Get the current upper band value
   * @returns Current upper band value
   */
  getCurrentUpperBand(): number | null

  /**
   * Get the current lower band value
   * @returns Current lower band value
   */
  getCurrentLowerBand(): number | null

  /**
   * Get the current standard deviation
   * @returns Current standard deviation value
   */
  getCurrentStandardDeviation(): number | null

  /**
   * Check if current price is above upper band (potential sell signal)
   * @param currentPrice - Current price to check
   * @returns True if price is above upper band
   */
  isPriceAboveUpperBand(currentPrice: number): boolean

  /**
   * Check if current price is below lower band (potential buy signal)
   * @param currentPrice - Current price to check
   * @returns True if price is below lower band
   */
  isPriceBelowLowerBand(currentPrice: number): boolean

  /**
   * Get the band width (upper band - lower band)
   * @returns Current band width or null if not enough data
   */
  getBandWidth(): number | null

  /**
   * Get the %B indicator (position of price within bands)
   * @param currentPrice - Current price
   * @returns %B value (0-1, where 0.5 is middle band)
   */
  getPercentB(currentPrice: number): number | null
}

/**
 * Factory function type for creating indicators
 */
export type IndicatorFactory<T extends BaseIndicator = BaseIndicator> = (
  config: BaseIndicatorConfig
) => T

/**
 * Registry of available indicators
 */
export interface IndicatorRegistry {
  /** Register a new indicator factory */
  register<T extends BaseIndicator>(name: string, factory: IndicatorFactory<T>): void
  /** Create an indicator instance */
  create<T extends BaseIndicator>(name: string, config: BaseIndicatorConfig): T
  /** Get list of available indicator names */
  getAvailableIndicators(): string[]
  /** Check if an indicator is registered */
  isRegistered(name: string): boolean
}
