/**
 * Constants and configuration for Signal Engine and Strategy Factory
 * Contains default values, validation constraints, and performance settings
 */

import type {
  BaseStrategyConfig,
  StrategyFactoryConfig,
  SignalEngineConfig,
  IndicatorSignalRules,
  TradingSignal,
  StrategyCombinationLogic
} from '../types/signals'

/**
 * Trading signal constants
 */
export const TRADING_SIGNALS = {
  BUY: 'BUY' as const,
  SELL: 'SELL' as const,
  HOLD: 'HOLD' as const
} as const

/**
 * Strategy combination logic constants
 */
export const COMBINATION_LOGIC = {
  AND: 'AND' as const,
  OR: 'OR' as const,
  MAJORITY: 'MAJORITY' as const,
  WEIGHTED: 'WEIGHTED' as const
} as const

/**
 * Signal condition types
 */
export const SIGNAL_CONDITION_TYPES = {
  THRESHOLD: 'threshold' as const,
  CROSSOVER: 'crossover' as const,
  DIVERGENCE: 'divergence' as const,
  PATTERN: 'pattern' as const
} as const

/**
 * Comparison operators for signal conditions
 */
export const COMPARISON_OPERATORS = {
  GREATER_THAN: 'gt' as const,
  LESS_THAN: 'lt' as const,
  GREATER_THAN_EQUAL: 'gte' as const,
  LESS_THAN_EQUAL: 'lte' as const,
  EQUAL: 'eq' as const,
  NOT_EQUAL: 'ne' as const
} as const

/**
 * Crossover directions
 */
export const CROSSOVER_DIRECTIONS = {
  ABOVE: 'above' as const,
  BELOW: 'below' as const,
  CROSS_ABOVE: 'cross_above' as const,
  CROSS_BELOW: 'cross_below' as const
} as const

/**
 * Risk levels
 */
export const RISK_LEVELS = {
  LOW: 'low' as const,
  MEDIUM: 'medium' as const,
  HIGH: 'high' as const
} as const

/**
 * Default strategy configuration
 */
export const DEFAULT_STRATEGY_CONFIG: BaseStrategyConfig = {
  name: 'default_strategy',
  description: 'Default trading strategy',
  enableRealTime: true,
  maxSignalHistory: 1000,
  validateInputs: true,
  minConfidence: 0.6
} as const

/**
 * Default RSI signal rules
 */
export const DEFAULT_RSI_SIGNAL_RULES: IndicatorSignalRules = {
  buyConditions: [
    {
      type: 'threshold',
      params: {
        threshold: 30,
        operator: 'lte'
      }
    }
  ],
  sellConditions: [
    {
      type: 'threshold',
      params: {
        threshold: 70,
        operator: 'gte'
      }
    }
  ],
  holdConditions: [
    {
      type: 'threshold',
      params: {
        threshold: 30,
        operator: 'gt'
      }
    },
    {
      type: 'threshold',
      params: {
        threshold: 70,
        operator: 'lt'
      }
    }
  ]
} as const

/**
 * Default Bollinger Bands signal rules
 */
export const DEFAULT_BOLLINGER_BANDS_SIGNAL_RULES: IndicatorSignalRules = {
  buyConditions: [
    {
      type: 'crossover',
      params: {
        reference: 'lower_band',
        direction: 'cross_above'
      }
    }
  ],
  sellConditions: [
    {
      type: 'crossover',
      params: {
        reference: 'upper_band',
        direction: 'cross_above'
      }
    }
  ]
} as const

/**
 * Default SMA signal rules
 */
export const DEFAULT_SMA_SIGNAL_RULES: IndicatorSignalRules = {
  buyConditions: [
    {
      type: 'crossover',
      params: {
        reference: 'price',
        direction: 'cross_above'
      }
    }
  ],
  sellConditions: [
    {
      type: 'crossover',
      params: {
        reference: 'price',
        direction: 'cross_below'
      }
    }
  ]
} as const

/**
 * Default signal rules by indicator type
 */
export const DEFAULT_SIGNAL_RULES_BY_INDICATOR = {
  rsi: DEFAULT_RSI_SIGNAL_RULES,
  relativestrengthindex: DEFAULT_RSI_SIGNAL_RULES,
  sma: DEFAULT_SMA_SIGNAL_RULES,
  simplemovingaverage: DEFAULT_SMA_SIGNAL_RULES,
  bollingerbands: DEFAULT_BOLLINGER_BANDS_SIGNAL_RULES
} as const

/**
 * Default strategy factory configuration
 */
export const DEFAULT_STRATEGY_FACTORY_CONFIG: StrategyFactoryConfig = {
  defaultStrategyConfig: DEFAULT_STRATEGY_CONFIG,
  availableIndicators: ['rsi', 'sma', 'bollingerbands'],
  maxIndicatorsPerStrategy: 5,
  enablePerformanceTracking: true,
  enableValidation: true
} as const

/**
 * Default signal engine configuration
 */
export const DEFAULT_SIGNAL_ENGINE_CONFIG: SignalEngineConfig = {
  defaultSignalConfig: DEFAULT_STRATEGY_CONFIG,
  maxActiveStrategies: 10,
  signalHistoryRetention: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  enableRealTimeProcessing: true,
  performanceMonitoring: true
} as const

/**
 * Validation constraints
 */
export const VALIDATION_CONSTRAINTS = {
  /** Minimum confidence threshold */
  MIN_CONFIDENCE: 0.0,
  /** Maximum confidence threshold */
  MAX_CONFIDENCE: 1.0,
  /** Minimum strategy name length */
  MIN_STRATEGY_NAME_LENGTH: 3,
  /** Maximum strategy name length */
  MAX_STRATEGY_NAME_LENGTH: 50,
  /** Minimum number of indicators for multi-indicator strategy */
  MIN_INDICATORS_MULTI_STRATEGY: 2,
  /** Maximum number of indicators per strategy */
  MAX_INDICATORS_PER_STRATEGY: 10,
  /** Minimum signal history size */
  MIN_SIGNAL_HISTORY: 10,
  /** Maximum signal history size */
  MAX_SIGNAL_HISTORY: 10000,
  /** Minimum weight value for weighted strategies */
  MIN_WEIGHT: 0.0,
  /** Maximum weight value for weighted strategies */
  MAX_WEIGHT: 1.0,
  /** Minimum lookback period */
  MIN_LOOKBACK_PERIOD: 1,
  /** Maximum lookback period */
  MAX_LOOKBACK_PERIOD: 1000
} as const

/**
 * Performance monitoring constants
 */
export const PERFORMANCE_CONSTANTS = {
  /** Maximum processing time warning threshold (ms) */
  MAX_PROCESSING_TIME_WARNING: 100,
  /** Maximum processing time error threshold (ms) */
  MAX_PROCESSING_TIME_ERROR: 500,
  /** Memory usage warning threshold (MB) */
  MEMORY_WARNING_THRESHOLD: 50,
  /** Memory usage error threshold (MB) */
  MEMORY_ERROR_THRESHOLD: 100,
  /** Performance metrics update interval (ms) */
  METRICS_UPDATE_INTERVAL: 5000,
  /** Signal history cleanup interval (ms) */
  HISTORY_CLEANUP_INTERVAL: 60000
} as const

/**
 * Error messages for signal engine operations
 */
export const SIGNAL_ENGINE_ERROR_MESSAGES = {
  INVALID_INDICATOR: 'Invalid indicator name provided',
  INVALID_STRATEGY_CONFIG: 'Invalid strategy configuration',
  STRATEGY_NOT_FOUND: 'Strategy not found',
  STRATEGY_ALREADY_EXISTS: 'Strategy with this name already exists',
  MAX_STRATEGIES_REACHED: 'Maximum number of active strategies reached',
  INVALID_SIGNAL_RULES: 'Invalid signal generation rules',
  INVALID_COMBINATION_LOGIC: 'Invalid combination logic specified',
  INSUFFICIENT_DATA: 'Insufficient data for signal generation',
  VALIDATION_FAILED: 'Strategy validation failed',
  INDICATOR_CREATION_FAILED: 'Failed to create indicator instance',
  SIGNAL_GENERATION_FAILED: 'Failed to generate trading signal',
  INVALID_CONFIDENCE: 'Confidence value must be between 0 and 1',
  INVALID_WEIGHT: 'Weight value must be between 0 and 1',
  WEIGHTS_SUM_INVALID: 'Sum of weights must equal 1.0 for weighted strategies',
  INVALID_THRESHOLD: 'Invalid threshold value provided',
  INVALID_OPERATOR: 'Invalid comparison operator',
  INVALID_DIRECTION: 'Invalid crossover direction',
  INVALID_LOOKBACK: 'Invalid lookback period'
} as const

/**
 * Log categories for signal engine operations
 */
export const SIGNAL_ENGINE_LOG_CATEGORIES = {
  STRATEGY_CREATION: 'signal-engine:strategy-creation',
  SIGNAL_GENERATION: 'signal-engine:signal-generation',
  VALIDATION: 'signal-engine:validation',
  PERFORMANCE: 'signal-engine:performance',
  ERROR: 'signal-engine:error',
  DEBUG: 'signal-engine:debug'
} as const

/**
 * Event types for signal engine
 */
export const SIGNAL_ENGINE_EVENTS = {
  STRATEGY_CREATED: 'strategy-created',
  STRATEGY_REMOVED: 'strategy-removed',
  SIGNAL_GENERATED: 'signal-generated',
  VALIDATION_ERROR: 'validation-error',
  PERFORMANCE_WARNING: 'performance-warning',
  MEMORY_WARNING: 'memory-warning'
} as const

/**
 * Default confidence levels by signal type
 */
export const DEFAULT_CONFIDENCE_LEVELS = {
  [TRADING_SIGNALS.BUY]: 0.7,
  [TRADING_SIGNALS.SELL]: 0.7,
  [TRADING_SIGNALS.HOLD]: 0.5
} as const

/**
 * Signal priority levels
 */
export const SIGNAL_PRIORITY = {
  LOW: 1,
  MEDIUM: 2,
  HIGH: 3,
  CRITICAL: 4
} as const

/**
 * Strategy types
 */
export const STRATEGY_TYPES = {
  SINGLE_INDICATOR: 'single_indicator',
  MULTI_INDICATOR: 'multi_indicator',
  CUSTOM: 'custom'
} as const

/**
 * Market condition types
 */
export const MARKET_CONDITIONS = {
  TRENDING_UP: 'trending_up',
  TRENDING_DOWN: 'trending_down',
  SIDEWAYS: 'sideways',
  VOLATILE: 'volatile',
  STABLE: 'stable'
} as const
