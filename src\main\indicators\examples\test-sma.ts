/**
 * Simple test file to verify SMA implementation
 * Run this to ensure the indicator system is working correctly
 */

import type { IndicatorDataPoint } from '../../../shared/types/indicators'
import { createSMA, calculateSMA, IndicatorUtils } from '../index'
import { logger } from '../../../shared/utils/logger'

// Test constants
const FLOATING_POINT_PRECISION = 0.001
const PERFORMANCE_THRESHOLD_MS = 1
const ASYNC_TEST_TIMEOUT_MS = 100
const STREAM_UPDATE_DELAY_MS = 50

// Test data
const TEST_PRICES = [10, 20, 30, 40, 50] as const
const EXPECTED_SMA_3 = [20, 30, 40] as const
const LARGE_DATASET_SIZE = 1000
const PERFORMANCE_TEST_PERIOD = 20

/**
 * Utility function to check if arrays are approximately equal
 * @param actual - Actual values
 * @param expected - Expected values
 * @param precision - Floating point precision tolerance
 * @returns True if arrays are approximately equal
 */
function areArraysApproximatelyEqual(
  actual: number[],
  expected: readonly number[],
  precision: number = FLOATING_POINT_PRECISION
): boolean {
  return (
    actual.length === expected.length &&
    actual.every((val, i) => Math.abs(val - expected[i]) < precision)
  )
}

/**
 * Utility function to create test data points
 * @param prices - Array of price values
 * @param baseTimestamp - Base timestamp (defaults to current time)
 * @returns Array of indicator data points
 */
function createTestDataPoints(
  prices: readonly number[],
  baseTimestamp: number = Date.now()
): IndicatorDataPoint[] {
  return prices.map((price, index) => ({
    value: price,
    timestamp: baseTimestamp + index
  }))
}

/**
 * Test basic SMA calculation
 */
function testBasicSMA(): boolean {
  console.log('Testing basic SMA calculation...')

  try {
    const sma = createSMA({ period: 3 })
    const testDataPoints = createTestDataPoints(TEST_PRICES)

    testDataPoints.forEach((dataPoint) => {
      sma.addData(dataPoint)
    })

    const values = IndicatorUtils.extractValues(sma.getValues())

    console.log('Calculated values:', values)
    console.log('Expected values:', EXPECTED_SMA_3)

    const isCorrect = areArraysApproximatelyEqual(values, EXPECTED_SMA_3)

    if (isCorrect) {
      console.log('✅ Basic SMA test passed')
      return true
    } else {
      console.log('❌ Basic SMA test failed')
      return false
    }
  } catch (error) {
    console.error('❌ Basic SMA test error:', error)
    return false
  }
}

/**
 * Test legacy function compatibility
 */
function testLegacyFunction(): boolean {
  console.log('\nTesting legacy function compatibility...')

  try {
    const period = 3
    const values = calculateSMA([...TEST_PRICES], period)

    console.log('Legacy function values:', values)
    console.log('Expected values:', EXPECTED_SMA_3)

    const isCorrect = areArraysApproximatelyEqual(values, EXPECTED_SMA_3)

    if (isCorrect) {
      console.log('✅ Legacy function test passed')
      return true
    } else {
      console.log('❌ Legacy function test failed')
      return false
    }
  } catch (error) {
    console.error('❌ Legacy function test error:', error)
    return false
  }
}

/**
 * Test performance optimization
 */
function testPerformance(): boolean {
  console.log('\nTesting performance optimization...')

  try {
    const sma = createSMA({ period: PERFORMANCE_TEST_PERIOD })
    const largeDataset = Array.from({ length: LARGE_DATASET_SIZE }, (_, i) => i + 1)
    const testDataPoints = createTestDataPoints(largeDataset)

    const startTime = performance.now()

    // Add data points
    testDataPoints.forEach((dataPoint) => {
      sma.addData(dataPoint)
    })

    const endTime = performance.now()
    const duration = endTime - startTime
    const averageTimePerCalculation = duration / largeDataset.length

    console.log(`Processed ${largeDataset.length} data points in ${duration.toFixed(2)}ms`)
    console.log(`Average time per calculation: ${averageTimePerCalculation.toFixed(4)}ms`)

    // Performance should be reasonable (less than threshold per calculation on average)
    const isPerformant = averageTimePerCalculation < PERFORMANCE_THRESHOLD_MS

    if (isPerformant) {
      console.log('✅ Performance test passed')
      return true
    } else {
      console.log('⚠️  Performance test warning: calculations may be slower than expected')
      return true // Don't fail on performance, just warn
    }
  } catch (error) {
    console.error('❌ Performance test error:', error)
    return false
  }
}

/**
 * Test streaming functionality
 */
function testStreaming(): Promise<boolean> {
  console.log('\nTesting streaming functionality...')

  return new Promise((resolve) => {
    try {
      const sma = createSMA({ period: 3, enableStreaming: true })
      let streamUpdateCount = 0
      let eventCount = 0

      // Set up stream callback
      sma.onStreamUpdate((update) => {
        streamUpdateCount++
        console.log(`Stream update ${streamUpdateCount}: ${update.indicatorValue.value.toFixed(2)}`)
      })

      // Set up event listener
      sma.on('value-calculated', () => {
        eventCount++
      })

      // Add test data
      const testData = [10, 20, 30, 40] as const
      testData.forEach((value, index) => {
        setTimeout(() => {
          sma.addData({ value, timestamp: Date.now() + index })

          // Check results after last data point
          if (index === testData.length - 1) {
            setTimeout(() => {
              const success = streamUpdateCount > 0 && eventCount > 0
              const message = success
                ? `✅ Streaming test passed (${streamUpdateCount} stream updates, ${eventCount} events)`
                : '❌ Streaming test failed'

              console.log(message)
              resolve(success)
            }, ASYNC_TEST_TIMEOUT_MS)
          }
        }, index * STREAM_UPDATE_DELAY_MS)
      })
    } catch (error) {
      console.error('❌ Streaming test error:', error)
      resolve(false)
    }
  })
}

/**
 * Test error handling
 */
function testErrorHandling(): boolean {
  console.log('\nTesting error handling...')

  try {
    // Test invalid configuration
    const configurationErrorCaught = testInvalidConfiguration()
    if (!configurationErrorCaught) {
      console.log('❌ Invalid configuration should have thrown an error')
      return false
    }

    // Test input validation
    const inputValidationWorking = testInputValidation()
    if (!inputValidationWorking) {
      console.log('❌ Input validation failed to catch invalid data')
      return false
    }

    return true
  } catch (error) {
    console.error('❌ Error handling test error:', error)
    return false
  }
}

/**
 * Test invalid configuration handling
 * @returns True if error was caught correctly
 */
function testInvalidConfiguration(): boolean {
  try {
    createSMA({ period: -1 })
    return false // Should have thrown an error
  } catch {
    console.log('✅ Invalid configuration error caught correctly')
    return true
  }
}

/**
 * Test input validation
 * @returns True if validation works correctly
 */
function testInputValidation(): boolean {
  const sma = createSMA({ period: 3 })
  const invalidDataPoint: IndicatorDataPoint = { value: NaN, timestamp: Date.now() }
  const validation = sma.validateInput([invalidDataPoint])

  const isValid = !validation.isValid && validation.errors.length > 0
  if (isValid) {
    console.log('✅ Input validation working correctly')
  }

  return isValid
}

/**
 * Test suite configuration
 */
interface TestSuite {
  name: string
  testFunction: () => boolean | Promise<boolean>
}

/**
 * All test suites to run
 */
const TEST_SUITES: readonly TestSuite[] = [
  { name: 'Basic SMA Calculation', testFunction: testBasicSMA },
  { name: 'Legacy Function Compatibility', testFunction: testLegacyFunction },
  { name: 'Performance Optimization', testFunction: testPerformance },
  { name: 'Error Handling', testFunction: testErrorHandling },
  { name: 'Streaming Functionality', testFunction: testStreaming }
] as const

/**
 * Run all tests
 */
export async function runTests(): Promise<void> {
  console.log('🧪 Running SMA Implementation Tests...\n')

  const results: boolean[] = []

  // Run all test suites
  for (const testSuite of TEST_SUITES) {
    try {
      const result = await testSuite.testFunction()
      results.push(result)
    } catch (error) {
      console.error(`❌ Test suite "${testSuite.name}" failed with error:`, error)
      results.push(false)
    }
  }

  // Generate summary
  generateTestSummary(results)
}

/**
 * Generate and display test summary
 * @param results - Array of test results
 */
function generateTestSummary(results: readonly boolean[]): void {
  const passed = results.filter(Boolean).length
  const total = results.length

  console.log(`\n📊 Test Results: ${passed}/${total} tests passed`)

  if (passed === total) {
    console.log('🎉 All tests passed! SMA implementation is working correctly.')
    logger.info('SMA-Test', 'All tests passed successfully')
  } else {
    const failed = total - passed
    console.log('⚠️  Some tests failed. Please check the implementation.')
    logger.warn('SMA-Test', `${failed} tests failed`)
  }
}

/**
 * Quick verification function for manual testing
 */
export function quickTest(): void {
  console.log('🚀 Quick SMA Test...')

  const QUICK_TEST_PERIOD = 5
  const QUICK_TEST_PRICES = [100, 102, 98, 105, 103, 99, 101] as const

  const sma = createSMA({ period: QUICK_TEST_PERIOD })
  const testDataPoints = createTestDataPoints(QUICK_TEST_PRICES)

  console.log('Input prices:', QUICK_TEST_PRICES)

  testDataPoints.forEach((dataPoint) => {
    const result = sma.addData(dataPoint)
    if (result) {
      console.log(`SMA: ${result.value.toFixed(2)}`)
    }
  })

  console.log('✅ Quick test completed')
}

/**
 * Public API for the test module
 */
export const SMATestSuite = {
  // Main test functions
  runTests,
  quickTest,

  // Individual test functions (for selective testing)
  testBasicSMA,
  testLegacyFunction,
  testPerformance,
  testStreaming,
  testErrorHandling,

  // Utility functions
  areArraysApproximatelyEqual,
  createTestDataPoints,

  // Test constants (for external use)
  constants: {
    FLOATING_POINT_PRECISION,
    PERFORMANCE_THRESHOLD_MS,
    ASYNC_TEST_TIMEOUT_MS,
    STREAM_UPDATE_DELAY_MS,
    TEST_PRICES,
    EXPECTED_SMA_3,
    LARGE_DATASET_SIZE,
    PERFORMANCE_TEST_PERIOD
  }
} as const

// Default export for convenience
export default SMATestSuite

// Auto-run tests if this file is executed directly
if (require.main === module) {
  runTests().catch((error) => {
    console.error('Failed to run tests:', error)
    process.exit(1)
  })
}
