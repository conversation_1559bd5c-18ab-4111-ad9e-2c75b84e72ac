/**
 * Bollinger Bands Indicator Test Suite
 * Comprehensive testing for the Bollinger Bands implementation including
 * calculation accuracy, performance optimization, error handling, and streaming functionality
 */

import { BollingerBands, createBollingerBands, calculateBollingerBands } from '../bollingerBands'
import type {
  IndicatorDataPoint,
  BollingerBandsOutputPoint
} from '../../../shared/types/indicators'

/**
 * Test constants and configuration
 */
const FLOATING_POINT_PRECISION = 0.001
const PERFORMANCE_THRESHOLD_MS = 50
const ASYNC_TEST_TIMEOUT_MS = 5000
const STREAM_UPDATE_DELAY_MS = 100

/**
 * Test data - realistic price movements for Bollinger Bands testing
 */
const TEST_PRICES = [
  100, 102, 98, 105, 103, 99, 101, 104, 106, 102, 98, 95, 97, 100, 103, 105, 108, 110, 107, 104,
  102, 99, 96, 94, 97, 100, 103, 106, 109, 112
]

/**
 * Expected Bollinger Bands values for period 10, std dev 2
 * Calculated using standard financial formulas
 */
const EXPECTED_BB_PERIOD_10 = [
  { middle: 100.8, upper: 105.6, lower: 96.0 },
  { middle: 101.1, upper: 106.7, lower: 95.5 },
  { middle: 101.0, upper: 107.8, lower: 94.2 }
]

/**
 * Large dataset for performance testing
 */
const LARGE_DATASET_SIZE = 10000
const PERFORMANCE_TEST_PERIOD = 20

/**
 * Utility function to check if two numbers are approximately equal
 * @param a - First number
 * @param b - Second number
 * @param precision - Precision threshold
 * @returns True if numbers are approximately equal
 */
export const areNumbersApproximatelyEqual = (
  a: number,
  b: number,
  precision: number = FLOATING_POINT_PRECISION
): boolean => {
  return Math.abs(a - b) < precision
}

/**
 * Utility function to check if two arrays are approximately equal
 * @param a - First array
 * @param b - Second array
 * @param precision - Precision threshold
 * @returns True if arrays are approximately equal
 */
export const areArraysApproximatelyEqual = (
  a: number[],
  b: number[],
  precision: number = FLOATING_POINT_PRECISION
): boolean => {
  if (a.length !== b.length) return false
  return a.every((val, index) => areNumbersApproximatelyEqual(val, b[index], precision))
}

/**
 * Create test data points from price array
 * @param prices - Array of prices
 * @returns Array of indicator data points
 */
export const createTestDataPoints = (prices: number[]): IndicatorDataPoint[] => {
  return prices.map((price, index) => ({
    value: price,
    timestamp: Date.now() + index * 1000
  }))
}

/**
 * Test basic Bollinger Bands calculation
 */
export const testBasicBollingerBands = (): boolean => {
  console.log('🧮 Testing basic Bollinger Bands calculation...')

  try {
    const bb = createBollingerBands({ period: 10, standardDeviations: 2 })
    const dataPoints = createTestDataPoints(TEST_PRICES)

    // Add data points and collect results
    const results: BollingerBandsOutputPoint[] = []
    for (const point of dataPoints) {
      const result = bb.addData(point)
      if (result) {
        results.push(result)
      }
    }

    // Check that we have the expected number of results
    const expectedResults = TEST_PRICES.length - 10 + 1 // period - 1
    if (results.length !== expectedResults) {
      console.error(`❌ Expected ${expectedResults} results, got ${results.length}`)
      return false
    }

    // Verify calculation accuracy for a few key points
    const lastResult = results[results.length - 1]
    if (!lastResult.upperBand || !lastResult.lowerBand || !lastResult.middleBand) {
      console.error('❌ Missing band values in result')
      return false
    }

    // Check that upper band > middle band > lower band
    if (
      lastResult.upperBand <= lastResult.middleBand ||
      lastResult.middleBand <= lastResult.lowerBand
    ) {
      console.error('❌ Band ordering is incorrect')
      return false
    }

    // Check that standard deviation is positive
    if (lastResult.standardDeviation <= 0) {
      console.error('❌ Standard deviation should be positive')
      return false
    }

    console.log('✅ Basic Bollinger Bands calculation test passed')
    console.log(
      `   Last result: Upper=${lastResult.upperBand.toFixed(2)}, Middle=${lastResult.middleBand.toFixed(2)}, Lower=${lastResult.lowerBand.toFixed(2)}`
    )
    return true
  } catch (error) {
    console.error('❌ Basic Bollinger Bands test error:', error)
    return false
  }
}

/**
 * Test legacy function compatibility
 */
export const testLegacyFunction = (): boolean => {
  console.log('🔄 Testing legacy function compatibility...')

  try {
    const results = calculateBollingerBands(TEST_PRICES, 10, 2)

    if (results.length === 0) {
      console.error('❌ Legacy function returned no results')
      return false
    }

    // Check structure of results
    const lastResult = results[results.length - 1]
    if (!lastResult.upperBand || !lastResult.lowerBand || !lastResult.middleBand) {
      console.error('❌ Legacy function result missing band values')
      return false
    }

    console.log('✅ Legacy function compatibility test passed')
    return true
  } catch (error) {
    console.error('❌ Legacy function test error:', error)
    return false
  }
}

/**
 * Test performance with large dataset
 */
export const testPerformance = (): boolean => {
  console.log('⚡ Testing performance with large dataset...')

  try {
    // Generate large dataset
    const largePrices: number[] = []
    for (let i = 0; i < LARGE_DATASET_SIZE; i++) {
      largePrices.push(100 + Math.sin(i * 0.1) * 10 + Math.random() * 5)
    }

    const bb = createBollingerBands({ period: PERFORMANCE_TEST_PERIOD })
    const dataPoints = createTestDataPoints(largePrices)

    const startTime = performance.now()

    // Process all data points
    let resultCount = 0
    for (const point of dataPoints) {
      const result = bb.addData(point)
      if (result) {
        resultCount++
      }
    }

    const endTime = performance.now()
    const processingTime = endTime - startTime

    console.log(`   Processed ${LARGE_DATASET_SIZE} data points in ${processingTime.toFixed(2)}ms`)
    console.log(`   Generated ${resultCount} Bollinger Bands values`)
    console.log(`   Average time per calculation: ${(processingTime / resultCount).toFixed(4)}ms`)

    if (processingTime > (PERFORMANCE_THRESHOLD_MS * LARGE_DATASET_SIZE) / 1000) {
      console.warn(`⚠️  Performance test slower than expected: ${processingTime.toFixed(2)}ms`)
      return false
    }

    console.log('✅ Performance test passed')
    return true
  } catch (error) {
    console.error('❌ Performance test error:', error)
    return false
  }
}

/**
 * Test error handling scenarios
 */
export const testErrorHandling = (): boolean => {
  console.log('🛡️ Testing error handling...')

  try {
    // Test invalid configuration
    try {
      createBollingerBands({ period: 0 })
      console.error('❌ Should have thrown error for invalid period')
      return false
    } catch (error) {
      // Expected error
    }

    // Test invalid standard deviations
    try {
      createBollingerBands({ period: 20, standardDeviations: -1 })
      console.error('❌ Should have thrown error for negative standard deviations')
      return false
    } catch (error) {
      // Expected error
    }

    // Test invalid data
    const bb = createBollingerBands({ period: 5, validateInputs: true })
    try {
      bb.addData({ value: NaN, timestamp: Date.now() })
      console.error('❌ Should have thrown error for NaN value')
      return false
    } catch (error) {
      // Expected error
    }

    console.log('✅ Error handling test passed')
    return true
  } catch (error) {
    console.error('❌ Error handling test error:', error)
    return false
  }
}

/**
 * Test streaming functionality
 */
export const testStreaming = async (): Promise<boolean> => {
  console.log('📡 Testing streaming functionality...')

  try {
    const bb = createBollingerBands({ period: 5 })
    let streamUpdateCount = 0

    // Enable streaming with callback
    bb.enableStreaming((update) => {
      streamUpdateCount++
      console.log(
        `   Stream update ${streamUpdateCount}: ${update.indicatorValue.value.toFixed(2)}`
      )
    })

    // Add some initial data
    const initialData = createTestDataPoints(TEST_PRICES.slice(0, 10))
    for (const point of initialData) {
      bb.addData(point)
      await new Promise((resolve) => setTimeout(resolve, STREAM_UPDATE_DELAY_MS))
    }

    // Disable streaming
    bb.disableStreaming()

    if (streamUpdateCount === 0) {
      console.error('❌ No stream updates received')
      return false
    }

    console.log(`✅ Streaming test passed with ${streamUpdateCount} updates`)
    return true
  } catch (error) {
    console.error('❌ Streaming test error:', error)
    return false
  }
}

/**
 * Test band analysis methods
 */
export const testBandAnalysis = (): boolean => {
  console.log('📊 Testing band analysis methods...')

  try {
    const bb = createBollingerBands({ period: 10, standardDeviations: 2 })
    const dataPoints = createTestDataPoints(TEST_PRICES)

    // Add enough data to get results
    for (const point of dataPoints) {
      bb.addData(point)
    }

    // Test current band methods
    const currentBands = bb.getCurrentBands()
    if (!currentBands) {
      console.error('❌ No current bands available')
      return false
    }

    const upperBand = bb.getCurrentUpperBand()
    const middleBand = bb.getCurrentMiddleBand()
    const lowerBand = bb.getCurrentLowerBand()
    const stdDev = bb.getCurrentStandardDeviation()

    if (!upperBand || !middleBand || !lowerBand || !stdDev) {
      console.error('❌ Missing band values from getter methods')
      return false
    }

    // Test price position methods
    const testPrice = middleBand + 0.5 * (upperBand - middleBand)
    const isAboveUpper = bb.isPriceAboveUpperBand(upperBand + 1)
    const isBelowLower = bb.isPriceBelowLowerBand(lowerBand - 1)

    if (!isAboveUpper || !isBelowLower) {
      console.error('❌ Price position methods not working correctly')
      return false
    }

    // Test band width and %B
    const bandWidth = bb.getBandWidth()
    const percentB = bb.getPercentB(testPrice)

    if (!bandWidth || percentB === null) {
      console.error('❌ Band width or %B calculation failed')
      return false
    }

    console.log(`   Band width: ${bandWidth.toFixed(2)}`)
    console.log(`   %B for test price: ${(percentB * 100).toFixed(1)}%`)
    console.log('✅ Band analysis test passed')
    return true
  } catch (error) {
    console.error('❌ Band analysis test error:', error)
    return false
  }
}

/**
 * Test EMA vs SMA modes
 */
export const testEMAMode = (): boolean => {
  console.log('📈 Testing EMA vs SMA modes...')

  try {
    const bbSMA = createBollingerBands({ period: 10, useEMA: false })
    const bbEMA = createBollingerBands({ period: 10, useEMA: true, smoothingFactor: 0.2 })
    const dataPoints = createTestDataPoints(TEST_PRICES)

    // Add data to both indicators
    for (const point of dataPoints) {
      bbSMA.addData(point)
      bbEMA.addData(point)
    }

    const smaResult = bbSMA.getCurrentBands()
    const emaResult = bbEMA.getCurrentBands()

    if (!smaResult || !emaResult) {
      console.error('❌ Missing results from SMA or EMA mode')
      return false
    }

    // Results should be different
    if (areNumbersApproximatelyEqual(smaResult.middleBand, emaResult.middleBand, 0.01)) {
      console.warn('⚠️  SMA and EMA results are very similar')
    }

    console.log(`   SMA middle band: ${smaResult.middleBand.toFixed(2)}`)
    console.log(`   EMA middle band: ${emaResult.middleBand.toFixed(2)}`)
    console.log('✅ EMA mode test passed')
    return true
  } catch (error) {
    console.error('❌ EMA mode test error:', error)
    return false
  }
}

/**
 * Test suite configuration
 */
interface TestSuite {
  name: string
  testFunction: () => boolean | Promise<boolean>
}

/**
 * All test suites to run
 */
const TEST_SUITES: readonly TestSuite[] = [
  { name: 'Basic Bollinger Bands Calculation', testFunction: testBasicBollingerBands },
  { name: 'Legacy Function Compatibility', testFunction: testLegacyFunction },
  { name: 'Performance Optimization', testFunction: testPerformance },
  { name: 'Error Handling', testFunction: testErrorHandling },
  { name: 'Streaming Functionality', testFunction: testStreaming },
  { name: 'Band Analysis Methods', testFunction: testBandAnalysis },
  { name: 'EMA vs SMA Modes', testFunction: testEMAMode }
] as const

/**
 * Run all Bollinger Bands tests
 */
export async function runTests(): Promise<boolean> {
  console.log('🚀 Starting Bollinger Bands Indicator Test Suite...\n')

  let passedTests = 0
  const totalTests = TEST_SUITES.length

  for (const testSuite of TEST_SUITES) {
    try {
      console.log(`\n📋 Running: ${testSuite.name}`)
      console.log('─'.repeat(50))

      const result = await testSuite.testFunction()

      if (result) {
        passedTests++
        console.log(`✅ ${testSuite.name} - PASSED`)
      } else {
        console.log(`❌ ${testSuite.name} - FAILED`)
      }
    } catch (error) {
      console.error(`💥 ${testSuite.name} - ERROR:`, error)
    }
  }

  // Generate summary
  console.log('\n' + '='.repeat(60))
  console.log('📊 BOLLINGER BANDS TEST SUMMARY')
  console.log('='.repeat(60))
  console.log(`Total Tests: ${totalTests}`)
  console.log(`Passed: ${passedTests}`)
  console.log(`Failed: ${totalTests - passedTests}`)
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`)

  const allPassed = passedTests === totalTests
  if (allPassed) {
    console.log('🎉 All tests passed! Bollinger Bands implementation is working correctly.')
  } else {
    console.log('⚠️  Some tests failed. Please review the implementation.')
  }

  return allPassed
}

/**
 * Quick test function for development
 */
export const quickTest = (): boolean => {
  console.log('⚡ Running quick Bollinger Bands test...')

  try {
    const bb = createBollingerBands({ period: 5 })
    const testData = [100, 102, 98, 105, 103, 99, 101]

    for (const price of testData) {
      bb.addData({ value: price, timestamp: Date.now() })
    }

    const result = bb.getCurrentBands()
    if (!result) {
      console.error('❌ Quick test failed - no result')
      return false
    }

    console.log(
      `✅ Quick test passed - Upper: ${result.upperBand.toFixed(2)}, Middle: ${result.middleBand.toFixed(2)}, Lower: ${result.lowerBand.toFixed(2)}`
    )
    return true
  } catch (error) {
    console.error('❌ Quick test error:', error)
    return false
  }
}

/**
 * Public API for the test module
 */
export const BollingerBandsTestSuite = {
  // Main test functions
  runTests,
  quickTest,

  // Individual test functions (for selective testing)
  testBasicBollingerBands,
  testLegacyFunction,
  testPerformance,
  testStreaming,
  testErrorHandling,
  testBandAnalysis,
  testEMAMode,

  // Utility functions
  areNumbersApproximatelyEqual,
  areArraysApproximatelyEqual,
  createTestDataPoints,

  // Test constants (for external use)
  constants: {
    FLOATING_POINT_PRECISION,
    PERFORMANCE_THRESHOLD_MS,
    ASYNC_TEST_TIMEOUT_MS,
    STREAM_UPDATE_DELAY_MS,
    TEST_PRICES,
    EXPECTED_BB_PERIOD_10,
    LARGE_DATASET_SIZE,
    PERFORMANCE_TEST_PERIOD
  }
} as const

// Export default for convenience
export default BollingerBandsTestSuite
