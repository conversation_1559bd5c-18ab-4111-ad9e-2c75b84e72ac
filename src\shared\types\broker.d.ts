type StreamData = [symbol: string, timestamp: number, price: number | null]
type Candle = [time: number, open: number, high: number, low: number, close: number]
type History = [time: number, price: number]

/**
 * TypeScript type definitions for PocketOption broker
 * Contains all custom types used by the PocketOption class
 */

/**
 * Interface for authentication data extracted from session ID
 */
interface AuthData {
  session: string
  uid: number
}

interface Balance {
  isDemo: number
  balance: number
}

/**
 * Interface for socket authentication payload
 */
interface SocketAuthPayload {
  isDemo: number
  isFastHistory: boolean
  platform: number
  session: string
  uid: number
}

/**
 * Interface for heartbeat configuration
 */
interface HeartbeatConfig {
  interval: number
  timeout: number
  maxMissedBeats: number
}

/**
 * Type for heartbeat statistics returned by getHeartbeatStats method
 */
interface HeartbeatStats {
  health: string // Using string instead of enum to avoid circular dependency
  missedBeats: number
  lastSent: number
  lastReceived: number
  isActive: boolean
}

/**
 * Type for connection state change event data
 */
interface ConnectionStateChangeData {
  from: string
  to: string
  timestamp: number
}

/**
 * Type for heartbeat health change event data
 */
interface HeartbeatHealthChangeData {
  from: string
  to: string
  missedBeats: number
}

/**
 * Type for heartbeat sent event data
 */
interface HeartbeatSentData {
  timestamp: number
  missedBeats: number
}

/**
 * Type for heartbeat received event data
 */
interface HeartbeatReceivedData {
  timestamp: number
  responseTime: number
  missedBeats: number
}

/**
 * Type for heartbeat failed event data
 */
interface HeartbeatFailedData {
  missedBeats: number
  maxMissedBeats: number
}

/**
 * Type for disconnect event data
 */
interface DisconnectEventData {
  reason: string
}

/**
 * Type for error event data
 */
interface ErrorEventData {
  error: string
}

/**
 * Type for ChartData
 */
interface ChartData {
  chart_id: string
  settings: string
}

/**
 * Type for ChartData.settings
 */
interface ChartSettings {
  chartId: string
  chartType: number
  chartPeriod: number
  candlesTimer: boolean
  symbol: string
  demoDealAmount: number
  liveDealAmount: number
  enabledTradeMonitor: boolean
  enabledRatingWidget: boolean
  isVisible: boolean
  fastTimeframe: number
  enabledAutoscroll: boolean
  enabledGridSnap: boolean
  minimizedTradePanel: boolean
  fastCloseAt: number
  enableQuickAutoOffset: boolean
  quickAutoOffsetValue: number
  showArea: boolean
  percentAmount: number
}

/**
 * Type for `loadHistoryPeriodFast` method response
 */
interface HistoryPeriodData {
  asset: string
  index: number
  data: PeriodData[]
  period: number
}

interface PeriodData {
  symbol_id: number
  time: number
  open: number
  close: number
  high: number
  low: number
}

interface HistoryNewFastData {
  asset: string
  period: number
  history: History[]
  candles: Candle[]
}
