# Signal Engine and Strategy Factory

Comprehensive trading signal generation system with flexible strategy creation and real-time signal processing.

## Overview

The Signal Engine and Strategy Factory provide a powerful, extensible framework for creating and executing trading strategies using technical indicators. The system supports both single indicator strategies and complex multi-indicator combinations with various signal combination logics.

### Key Features

- **Flexible Strategy Creation**: Support for single and multi-indicator strategies
- **Real-time Signal Generation**: Process market data and generate BUY/SELL/HOLD signals
- **Multiple Combination Logics**: AND, OR, MAJORITY, and WEIGHTED signal combinations
- **Performance Monitoring**: Comprehensive metrics and performance tracking
- **Singleton Pattern**: Consistent state management across the application
- **Comprehensive Validation**: Input validation and error handling
- **Memory Management**: Automatic cleanup and resource management
- **Event-driven Architecture**: Real-time signal broadcasting and monitoring

## Architecture

```
SignalEngine (Singleton)
├── StrategyFactory (Singleton)
│   ├── StrategyImpl (Strategy instances)
│   │   ├── BaseIndicator instances (RSI, SMA, etc.)
│   │   └── Signal generation logic
│   └── Validation and error handling
└── Performance monitoring and metrics
```

## Quick Start

### Basic Usage

```typescript
import { SignalEngine } from './core/SignalEngine'

// Get singleton instance
const signalEngine = SignalEngine.getInstance()

// Create a simple RSI strategy
const rsiStrategy = signalEngine.createStrategy('rsi', {
  name: 'rsi_oversold_strategy',
  period: 14,
  overbought: 70,
  oversold: 30
})

// Process market data
const marketData = { value: 45.67, timestamp: Date.now() }
const signals = signalEngine.processMarketData(marketData)

console.log('Generated signals:', signals)
// Output: [{ signal: 'BUY', confidence: 0.75, strategy: 'rsi_oversold_strategy', ... }]
```

### Advanced Multi-Indicator Strategy

```typescript
// Create a combined RSI + SMA strategy
const combinedStrategy = signalEngine.createStrategy(['rsi', 'sma'], {
  name: 'rsi_sma_combined',
  description: 'RSI oversold with SMA trend confirmation',
  rsi: { 
    period: 14,
    overbought: 70,
    oversold: 30
  },
  sma: { 
    period: 20
  },
  combinationLogic: 'AND', // Both indicators must agree
  weights: [
    { indicator: 'rsi', weight: 0.6 },
    { indicator: 'sma', weight: 0.4 }
  ]
})
```

## Strategy Configuration

### Single Indicator Strategy

```typescript
interface SingleIndicatorConfig {
  name?: string                    // Strategy name (auto-generated if not provided)
  description?: string             // Strategy description
  period?: number                  // Indicator period (default: 14)
  overbought?: number             // RSI overbought threshold (default: 70)
  oversold?: number               // RSI oversold threshold (default: 30)
  signalRules?: IndicatorSignalRules  // Custom signal generation rules
  enableRealTime?: boolean        // Enable real-time processing
  maxSignalHistory?: number       // Maximum signals to keep in history
  validateInputs?: boolean        // Enable input validation
  minConfidence?: number          // Minimum confidence threshold (0-1)
}
```

### Multi-Indicator Strategy

```typescript
interface MultiIndicatorConfig {
  name?: string                    // Strategy name
  description?: string             // Strategy description
  combinationLogic?: 'AND' | 'OR' | 'MAJORITY' | 'WEIGHTED'
  weights?: IndicatorWeight[]      // Weights for WEIGHTED logic
  [indicatorName: string]: {       // Per-indicator configuration
    period?: number
    // ... indicator-specific options
  }
}
```

## Signal Combination Logic

### AND Logic
All indicators must generate the same signal type for the strategy to produce that signal.

```typescript
const strategy = signalEngine.createStrategy(['rsi', 'sma'], {
  combinationLogic: 'AND'
})
// Result: BUY only if both RSI and SMA generate BUY signals
```

### OR Logic
Any indicator generating a BUY or SELL signal will cause the strategy to produce that signal.

```typescript
const strategy = signalEngine.createStrategy(['rsi', 'sma'], {
  combinationLogic: 'OR'
})
// Result: BUY if either RSI or SMA generates a BUY signal
```

### MAJORITY Logic
The signal type with the most indicators wins.

```typescript
const strategy = signalEngine.createStrategy(['rsi', 'sma', 'bollinger'], {
  combinationLogic: 'MAJORITY'
})
// Result: BUY if 2 out of 3 indicators generate BUY signals
```

### WEIGHTED Logic
Signals are combined using weighted averages based on indicator importance.

```typescript
const strategy = signalEngine.createStrategy(['rsi', 'sma'], {
  combinationLogic: 'WEIGHTED',
  weights: [
    { indicator: 'rsi', weight: 0.7 },
    { indicator: 'sma', weight: 0.3 }
  ]
})
// Result: RSI has 70% influence, SMA has 30% influence
```

## Available Indicators

### RSI (Relative Strength Index)
```typescript
const rsiStrategy = signalEngine.createStrategy('rsi', {
  period: 14,           // Calculation period
  overbought: 70,       // Overbought threshold
  oversold: 30          // Oversold threshold
})
```

**Default Signal Rules:**
- BUY: RSI ≤ 30 (oversold)
- SELL: RSI ≥ 70 (overbought)
- HOLD: 30 < RSI < 70

### SMA (Simple Moving Average)
```typescript
const smaStrategy = signalEngine.createStrategy('sma', {
  period: 20,           // Moving average period
  priceType: 'close'    // Price type to use
})
```

**Default Signal Rules:**
- BUY: Price crosses above SMA
- SELL: Price crosses below SMA
- HOLD: Price near SMA

### Bollinger Bands
```typescript
const bbStrategy = signalEngine.createStrategy('bollingerbands', {
  period: 20,           // Moving average period
  stdDev: 2            // Standard deviation multiplier
})
```

**Default Signal Rules:**
- BUY: Price crosses above lower band
- SELL: Price crosses above upper band
- HOLD: Price within bands

## Performance Monitoring

### Get Performance Metrics

```typescript
const metrics = signalEngine.getPerformanceMetrics()
console.log('Performance metrics:', {
  totalStrategies: metrics.totalStrategies,
  activeStrategies: metrics.activeStrategies,
  totalSignals: metrics.totalSignals,
  averageProcessingTime: metrics.averageProcessingTime,
  memoryUsage: metrics.memoryUsage
})
```

### Strategy-Specific Metrics

```typescript
const strategy = signalEngine.getActiveStrategies()[0]
const strategyMetrics = strategy.getPerformanceMetrics()
console.log('Strategy performance:', {
  totalSignals: strategyMetrics.totalSignals,
  buySignals: strategyMetrics.buySignals,
  sellSignals: strategyMetrics.sellSignals,
  averageConfidence: strategyMetrics.averageConfidence
})
```

## Signal History Management

### Get Signal History

```typescript
// Get all signals
const allSignals = signalEngine.getSignalHistory()

// Get signals for specific strategy
const strategySignals = signalEngine.getSignalHistory('my_strategy')

// Get latest signal from strategy
const strategy = signalEngine.getActiveStrategies()[0]
const latestSignal = strategy.getLatestSignal()
```

### Clear Signal History

```typescript
// Clear all signal history
signalEngine.clearSignalHistory()

// Clear history for specific strategy
signalEngine.clearSignalHistory('my_strategy')
```

## Error Handling

The system provides comprehensive error handling with specific error types:

```typescript
import { SignalValidationError } from '../../shared/utils/signalValidation'

try {
  const strategy = signalEngine.createStrategy('invalid_indicator', {})
} catch (error) {
  if (error instanceof SignalValidationError) {
    console.error('Validation error:', error.message)
    console.error('Field:', error.field)
    console.error('Code:', error.code)
  }
}
```

## Best Practices

### 1. Strategy Naming
Use descriptive names for strategies to make debugging and monitoring easier:

```typescript
const strategy = signalEngine.createStrategy('rsi', {
  name: 'rsi_14_oversold_strategy',
  description: 'RSI(14) strategy focusing on oversold conditions'
})
```

### 2. Configuration Management
Store strategy configurations in constants for reusability:

```typescript
const RSI_OVERSOLD_CONFIG = {
  name: 'rsi_oversold',
  period: 14,
  overbought: 70,
  oversold: 30,
  minConfidence: 0.6
}

const strategy = signalEngine.createStrategy('rsi', RSI_OVERSOLD_CONFIG)
```

### 3. Performance Monitoring
Regularly monitor performance metrics to ensure optimal operation:

```typescript
setInterval(() => {
  const metrics = signalEngine.getPerformanceMetrics()
  if (metrics.averageProcessingTime > 100) {
    console.warn('High processing time detected:', metrics.averageProcessingTime)
  }
}, 30000) // Check every 30 seconds
```

### 4. Memory Management
Clean up unused strategies and old signals:

```typescript
// Remove unused strategies
signalEngine.removeStrategy('old_strategy')

// Clear old signal history periodically
setInterval(() => {
  signalEngine.clearSignalHistory()
}, 24 * 60 * 60 * 1000) // Daily cleanup
```

## Testing

### Unit Testing Example

```typescript
import { SignalEngine } from './core/SignalEngine'

describe('SignalEngine', () => {
  let signalEngine: SignalEngine

  beforeEach(() => {
    SignalEngine.resetInstance()
    signalEngine = SignalEngine.getInstance()
  })

  test('should create RSI strategy', () => {
    const strategy = signalEngine.createStrategy('rsi', {
      name: 'test_rsi',
      period: 14
    })

    expect(strategy.name).toBe('test_rsi')
    expect(strategy.isReady).toBe(true)
  })

  test('should generate signals', () => {
    const strategy = signalEngine.createStrategy('rsi', { period: 14 })
    
    const signals = signalEngine.processMarketData({
      value: 25, // Oversold condition
      timestamp: Date.now()
    })

    expect(signals).toHaveLength(1)
    expect(signals[0].signal).toBe('BUY')
  })
})
```

## Troubleshooting

### Common Issues

1. **Strategy Not Ready**: Ensure indicators have enough data points
2. **No Signals Generated**: Check if market data meets signal conditions
3. **High Memory Usage**: Clear signal history or reduce retention period
4. **Slow Processing**: Reduce number of active strategies or optimize indicator calculations

### Debug Logging

Enable debug logging to troubleshoot issues:

```typescript
import { logger } from '../../shared/utils/logger'

// Set log level to debug
logger.level = 'debug'

// Monitor signal generation
const signals = signalEngine.processMarketData(marketData)
// Check console for detailed debug information
```
