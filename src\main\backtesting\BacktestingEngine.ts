/**
 * Backtesting Engine
 * Comprehensive backtesting framework for validating signal profitability
 * and strategy performance using historical market data
 */

import { logger } from '../../shared/utils/logger'
import { SignalGenerationService } from '../services/SignalGenerationService'
import { AdvancedMultiIndicatorStrategy } from '../strategies/MultiIndicatorStrategy'
import {
  transformHistoryData,
  transformPeriodData,
  sortDataByTimestamp
} from '../../shared/utils/marketDataTransformers'
import type {
  GeneratedSignal,
  MarketDataPoint,
  OHLCMarketData,
  TradingSignal
} from '../../shared/types/signals'

/**
 * Backtesting configuration
 */
export interface BacktestConfig {
  /** Initial capital for backtesting */
  initialCapital: number
  /** Position size as percentage of capital (0-1) */
  positionSizePercent: number
  /** Maximum risk per trade as percentage (0-1) */
  maxRiskPerTrade: number
  /** Transaction costs (spread, commission) as percentage */
  transactionCosts: number
  /** Minimum confidence threshold for signal execution */
  minConfidenceThreshold: number
  /** Maximum number of concurrent positions */
  maxConcurrentPositions: number
  /** Stop loss percentage (0-1) */
  stopLossPercent: number
  /** Take profit percentage (0-1) */
  takeProfitPercent: number
  /** Signal expiry time in milliseconds */
  signalExpiryMs: number
}

/**
 * Trade execution result
 */
export interface TradeResult {
  id: string
  symbol: string
  signal: TradingSignal
  entryPrice: number
  exitPrice: number
  entryTime: number
  exitTime: number
  positionSize: number
  pnl: number
  pnlPercent: number
  confidence: number
  strategy: string
  exitReason: 'PROFIT' | 'LOSS' | 'EXPIRY' | 'SIGNAL_REVERSAL'
  duration: number
}

/**
 * Backtesting performance metrics
 */
export interface BacktestResults {
  // Basic metrics
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number

  // Financial metrics
  initialCapital: number
  finalCapital: number
  totalReturn: number
  totalReturnPercent: number
  maxDrawdown: number
  maxDrawdownPercent: number

  // Risk metrics
  sharpeRatio: number
  sortinoRatio: number
  calmarRatio: number
  maxConsecutiveLosses: number
  maxConsecutiveWins: number

  // Trade metrics
  averageWin: number
  averageLoss: number
  averageWinPercent: number
  averageLossPercent: number
  profitFactor: number
  expectancy: number

  // Time metrics
  averageTradeDuration: number
  totalBacktestDuration: number

  // Signal quality metrics
  averageConfidence: number
  confidenceVsPerformance: Array<{ confidence: number; winRate: number; avgReturn: number }>

  // Detailed trade history
  trades: TradeResult[]

  // Equity curve
  equityCurve: Array<{ timestamp: number; equity: number; drawdown: number }>
}

/**
 * Active position tracking
 */
interface ActivePosition {
  id: string
  symbol: string
  signal: TradingSignal
  entryPrice: number
  entryTime: number
  positionSize: number
  confidence: number
  strategy: string
  stopLoss: number
  takeProfit: number
  expiryTime: number
}

/**
 * Default backtesting configuration
 */
const DEFAULT_BACKTEST_CONFIG: BacktestConfig = {
  initialCapital: 10000,
  positionSizePercent: 0.02, // 2% per trade
  maxRiskPerTrade: 0.01, // 1% max risk
  transactionCosts: 0.001, // 0.1% transaction costs
  minConfidenceThreshold: 0.65,
  maxConcurrentPositions: 5,
  stopLossPercent: 0.02, // 2% stop loss
  takeProfitPercent: 0.04, // 4% take profit (1:2 risk-reward)
  signalExpiryMs: 5 * 60 * 1000 // 5 minutes
}

/**
 * Backtesting Engine
 */
export class BacktestingEngine {
  private config: BacktestConfig
  private signalService: SignalGenerationService | null = null
  private activePositions: Map<string, ActivePosition> = new Map()
  private completedTrades: TradeResult[] = []
  private equityCurve: Array<{ timestamp: number; equity: number; drawdown: number }> = []
  private currentCapital: number = 0
  private peakCapital: number = 0
  private tradeIdCounter: number = 0

  constructor(config: Partial<BacktestConfig> = {}) {
    this.config = { ...DEFAULT_BACKTEST_CONFIG, ...config }
    this.currentCapital = this.config.initialCapital
    this.peakCapital = this.config.initialCapital
  }

  /**
   * Run backtest on historical data
   */
  public async runBacktest(
    historicalData: (MarketDataPoint | OHLCMarketData)[],
    strategies?: string[]
  ): Promise<BacktestResults> {
    try {
      logger.info(
        'BacktestingEngine',
        `Starting backtest with ${historicalData.length} data points`
      )

      // Initialize signal generation service
      this.signalService = SignalGenerationService.getInstance()
      if (!this.signalService.getStatus().isInitialized) {
        await this.signalService.initialize({
          minConfidenceThreshold: this.config.minConfidenceThreshold,
          enableAdvancedStrategy: true,
          enableSignalLogging: false, // Disable logging during backtest
          maxSignalsPerSymbolPerHour: 100, // Allow more signals for backtesting
          watchedSymbols: []
        })
      }

      // Reset state
      this.resetBacktestState()

      // Sort data by timestamp
      const sortedData = sortDataByTimestamp(historicalData)

      // Process each data point
      for (let i = 0; i < sortedData.length; i++) {
        const dataPoint = sortedData[i]
        await this.processDataPoint(dataPoint, i, sortedData.length)
      }

      // Close any remaining positions
      this.closeAllPositions(sortedData[sortedData.length - 1])

      // Calculate and return results
      const results = this.calculateBacktestResults(
        sortedData[0].timestamp,
        sortedData[sortedData.length - 1].timestamp
      )

      logger.success(
        'BacktestingEngine',
        `Backtest completed. Win Rate: ${(results.winRate * 100).toFixed(1)}%, Total Return: ${(results.totalReturnPercent * 100).toFixed(1)}%`
      )

      return results
    } catch (error) {
      logger.error('BacktestingEngine', 'Error running backtest:', error)
      throw error
    }
  }

  /**
   * Process a single data point
   */
  private async processDataPoint(
    dataPoint: MarketDataPoint | OHLCMarketData,
    index: number,
    totalPoints: number
  ): Promise<void> {
    const currentPrice = 'price' in dataPoint ? dataPoint.price : dataPoint.close

    // Update existing positions
    this.updateActivePositions(dataPoint, currentPrice)

    // Generate new signals
    if (this.signalService) {
      const signals = await this.signalService.processMarketData(dataPoint)

      for (const signal of signals) {
        if (this.shouldExecuteSignal(signal, dataPoint)) {
          this.executeSignal(signal, dataPoint, currentPrice)
        }
      }
    }

    // Update equity curve every 100 data points or at the end
    if (index % 100 === 0 || index === totalPoints - 1) {
      this.updateEquityCurve(dataPoint.timestamp)
    }
  }

  /**
   * Check if signal should be executed
   */
  private shouldExecuteSignal(
    signal: GeneratedSignal,
    dataPoint: MarketDataPoint | OHLCMarketData
  ): boolean {
    // Check confidence threshold
    if (signal.confidence < this.config.minConfidenceThreshold) {
      return false
    }

    // Check if we have too many concurrent positions
    if (this.activePositions.size >= this.config.maxConcurrentPositions) {
      return false
    }

    // Check if we already have a position for this symbol
    const existingPosition = Array.from(this.activePositions.values()).find(
      (pos) => pos.symbol === dataPoint.symbol
    )

    if (existingPosition) {
      // Close existing position if signal is opposite
      if (existingPosition.signal !== signal.signal) {
        this.closePosition(existingPosition.id, dataPoint, 'SIGNAL_REVERSAL')
        return true
      }
      return false
    }

    return true
  }

  /**
   * Execute a trading signal
   */
  private executeSignal(
    signal: GeneratedSignal,
    dataPoint: MarketDataPoint | OHLCMarketData,
    currentPrice: number
  ): void {
    const positionSize = this.calculatePositionSize(currentPrice)
    const positionId = `trade_${++this.tradeIdCounter}`

    // Calculate stop loss and take profit levels
    const stopLoss =
      signal.signal === 'BUY'
        ? currentPrice * (1 - this.config.stopLossPercent)
        : currentPrice * (1 + this.config.stopLossPercent)

    const takeProfit =
      signal.signal === 'BUY'
        ? currentPrice * (1 + this.config.takeProfitPercent)
        : currentPrice * (1 - this.config.takeProfitPercent)

    const position: ActivePosition = {
      id: positionId,
      symbol: dataPoint.symbol,
      signal: signal.signal,
      entryPrice: currentPrice,
      entryTime: dataPoint.timestamp,
      positionSize,
      confidence: signal.confidence,
      strategy: signal.strategy,
      stopLoss,
      takeProfit,
      expiryTime: dataPoint.timestamp + this.config.signalExpiryMs
    }

    this.activePositions.set(positionId, position)

    // Deduct transaction costs
    this.currentCapital -= positionSize * this.config.transactionCosts

    logger.debug(
      'BacktestingEngine',
      `Opened ${signal.signal} position for ${dataPoint.symbol} at ${currentPrice.toFixed(4)}`
    )
  }

  /**
   * Update active positions based on current market data
   */
  private updateActivePositions(
    dataPoint: MarketDataPoint | OHLCMarketData,
    currentPrice: number
  ): void {
    const positionsToClose: string[] = []

    for (const [positionId, position] of this.activePositions) {
      if (position.symbol !== dataPoint.symbol) {
        continue
      }

      // Check for stop loss
      if (
        (position.signal === 'BUY' && currentPrice <= position.stopLoss) ||
        (position.signal === 'SELL' && currentPrice >= position.stopLoss)
      ) {
        positionsToClose.push(positionId)
        this.closePosition(positionId, dataPoint, 'LOSS')
        continue
      }

      // Check for take profit
      if (
        (position.signal === 'BUY' && currentPrice >= position.takeProfit) ||
        (position.signal === 'SELL' && currentPrice <= position.takeProfit)
      ) {
        positionsToClose.push(positionId)
        this.closePosition(positionId, dataPoint, 'PROFIT')
        continue
      }

      // Check for expiry
      if (dataPoint.timestamp >= position.expiryTime) {
        positionsToClose.push(positionId)
        this.closePosition(positionId, dataPoint, 'EXPIRY')
        continue
      }
    }

    // Remove closed positions
    positionsToClose.forEach((id) => this.activePositions.delete(id))
  }

  /**
   * Close a specific position
   */
  private closePosition(
    positionId: string,
    dataPoint: MarketDataPoint | OHLCMarketData,
    exitReason: TradeResult['exitReason']
  ): void {
    const position = this.activePositions.get(positionId)
    if (!position) {
      return
    }

    const exitPrice = 'price' in dataPoint ? dataPoint.price : dataPoint.close
    const pnl = this.calculatePnL(position, exitPrice)
    const pnlPercent = pnl / position.positionSize

    const trade: TradeResult = {
      id: position.id,
      symbol: position.symbol,
      signal: position.signal,
      entryPrice: position.entryPrice,
      exitPrice,
      entryTime: position.entryTime,
      exitTime: dataPoint.timestamp,
      positionSize: position.positionSize,
      pnl,
      pnlPercent,
      confidence: position.confidence,
      strategy: position.strategy,
      exitReason,
      duration: dataPoint.timestamp - position.entryTime
    }

    this.completedTrades.push(trade)
    this.currentCapital +=
      position.positionSize + pnl - position.positionSize * this.config.transactionCosts

    // Update peak capital for drawdown calculation
    if (this.currentCapital > this.peakCapital) {
      this.peakCapital = this.currentCapital
    }

    logger.debug(
      'BacktestingEngine',
      `Closed ${position.signal} position for ${position.symbol}: P&L ${pnl.toFixed(2)} (${(pnlPercent * 100).toFixed(1)}%)`
    )
  }

  /**
   * Close all active positions
   */
  private closeAllPositions(lastDataPoint: MarketDataPoint | OHLCMarketData): void {
    const positionIds = Array.from(this.activePositions.keys())
    positionIds.forEach((id) => {
      this.closePosition(id, lastDataPoint, 'EXPIRY')
      this.activePositions.delete(id)
    })
  }

  /**
   * Calculate P&L for a position
   */
  private calculatePnL(position: ActivePosition, exitPrice: number): number {
    if (position.signal === 'BUY') {
      return position.positionSize * ((exitPrice - position.entryPrice) / position.entryPrice)
    } else {
      return position.positionSize * ((position.entryPrice - exitPrice) / position.entryPrice)
    }
  }

  /**
   * Calculate position size based on risk management
   */
  private calculatePositionSize(currentPrice: number): number {
    const maxPositionByPercent = this.currentCapital * this.config.positionSizePercent
    const maxPositionByRisk =
      (this.currentCapital * this.config.maxRiskPerTrade) / this.config.stopLossPercent

    return Math.min(maxPositionByPercent, maxPositionByRisk)
  }

  /**
   * Update equity curve
   */
  private updateEquityCurve(timestamp: number): void {
    const currentDrawdown = (this.peakCapital - this.currentCapital) / this.peakCapital

    this.equityCurve.push({
      timestamp,
      equity: this.currentCapital,
      drawdown: currentDrawdown
    })
  }

  /**
   * Reset backtest state
   */
  private resetBacktestState(): void {
    this.activePositions.clear()
    this.completedTrades = []
    this.equityCurve = []
    this.currentCapital = this.config.initialCapital
    this.peakCapital = this.config.initialCapital
    this.tradeIdCounter = 0
  }

  /**
   * Calculate comprehensive backtest results
   */
  private calculateBacktestResults(startTime: number, endTime: number): BacktestResults {
    const trades = this.completedTrades
    const winningTrades = trades.filter((t) => t.pnl > 0)
    const losingTrades = trades.filter((t) => t.pnl < 0)

    // Basic metrics
    const totalTrades = trades.length
    const winRate = totalTrades > 0 ? winningTrades.length / totalTrades : 0

    // Financial metrics
    const totalReturn = this.currentCapital - this.config.initialCapital
    const totalReturnPercent = totalReturn / this.config.initialCapital
    const maxDrawdown = Math.max(...this.equityCurve.map((e) => e.drawdown * this.peakCapital))
    const maxDrawdownPercent = maxDrawdown / this.peakCapital

    // Trade metrics
    const averageWin =
      winningTrades.length > 0
        ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) / winningTrades.length
        : 0
    const averageLoss =
      losingTrades.length > 0
        ? losingTrades.reduce((sum, t) => sum + Math.abs(t.pnl), 0) / losingTrades.length
        : 0
    const averageWinPercent =
      winningTrades.length > 0
        ? winningTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / winningTrades.length
        : 0
    const averageLossPercent =
      losingTrades.length > 0
        ? losingTrades.reduce((sum, t) => sum + Math.abs(t.pnlPercent), 0) / losingTrades.length
        : 0

    const grossProfit = winningTrades.reduce((sum, t) => sum + t.pnl, 0)
    const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0))
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0

    const expectancy = totalTrades > 0 ? trades.reduce((sum, t) => sum + t.pnl, 0) / totalTrades : 0

    // Risk metrics
    const returns = this.equityCurve
      .map((e, i) =>
        i > 0 ? (e.equity - this.equityCurve[i - 1].equity) / this.equityCurve[i - 1].equity : 0
      )
      .slice(1)
    const avgReturn =
      returns.length > 0 ? returns.reduce((sum, r) => sum + r, 0) / returns.length : 0
    const returnStdDev =
      returns.length > 0
        ? Math.sqrt(
            returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length
          )
        : 0
    const sharpeRatio = returnStdDev > 0 ? avgReturn / returnStdDev : 0

    const negativeReturns = returns.filter((r) => r < 0)
    const downwardStdDev =
      negativeReturns.length > 0
        ? Math.sqrt(
            negativeReturns.reduce((sum, r) => sum + Math.pow(r, 2), 0) / negativeReturns.length
          )
        : 0
    const sortinoRatio = downwardStdDev > 0 ? avgReturn / downwardStdDev : 0

    const calmarRatio = maxDrawdownPercent > 0 ? totalReturnPercent / maxDrawdownPercent : 0

    // Consecutive wins/losses
    let maxConsecutiveWins = 0
    let maxConsecutiveLosses = 0
    let currentWinStreak = 0
    let currentLossStreak = 0

    trades.forEach((trade) => {
      if (trade.pnl > 0) {
        currentWinStreak++
        currentLossStreak = 0
        maxConsecutiveWins = Math.max(maxConsecutiveWins, currentWinStreak)
      } else {
        currentLossStreak++
        currentWinStreak = 0
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLossStreak)
      }
    })

    // Confidence analysis
    const confidenceVsPerformance = this.analyzeConfidenceVsPerformance(trades)
    const averageConfidence =
      trades.length > 0 ? trades.reduce((sum, t) => sum + t.confidence, 0) / trades.length : 0

    return {
      totalTrades,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate,

      initialCapital: this.config.initialCapital,
      finalCapital: this.currentCapital,
      totalReturn,
      totalReturnPercent,
      maxDrawdown,
      maxDrawdownPercent,

      sharpeRatio,
      sortinoRatio,
      calmarRatio,
      maxConsecutiveLosses,
      maxConsecutiveWins,

      averageWin,
      averageLoss,
      averageWinPercent,
      averageLossPercent,
      profitFactor,
      expectancy,

      averageTradeDuration:
        trades.length > 0 ? trades.reduce((sum, t) => sum + t.duration, 0) / trades.length : 0,
      totalBacktestDuration: endTime - startTime,

      averageConfidence,
      confidenceVsPerformance,

      trades: [...trades],
      equityCurve: [...this.equityCurve]
    }
  }

  /**
   * Analyze confidence vs performance correlation
   */
  private analyzeConfidenceVsPerformance(
    trades: TradeResult[]
  ): Array<{ confidence: number; winRate: number; avgReturn: number }> {
    const confidenceBuckets = [0.6, 0.7, 0.8, 0.9, 1.0]
    const analysis: Array<{ confidence: number; winRate: number; avgReturn: number }> = []

    for (let i = 0; i < confidenceBuckets.length; i++) {
      const minConfidence = i === 0 ? 0 : confidenceBuckets[i - 1]
      const maxConfidence = confidenceBuckets[i]

      const bucketTrades = trades.filter(
        (t) => t.confidence > minConfidence && t.confidence <= maxConfidence
      )

      if (bucketTrades.length > 0) {
        const winningTrades = bucketTrades.filter((t) => t.pnl > 0)
        const winRate = winningTrades.length / bucketTrades.length
        const avgReturn =
          bucketTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / bucketTrades.length

        analysis.push({
          confidence: maxConfidence,
          winRate,
          avgReturn
        })
      }
    }

    return analysis
  }
}
