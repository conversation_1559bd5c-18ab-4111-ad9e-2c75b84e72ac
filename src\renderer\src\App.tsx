import { useCallback, useEffect, useState } from 'react'
import TradingBotUI from './components/TradingBotUI'
import HeaderBalance from './components/HeaderBalance'
import { TradingContextProvider } from './contexts/TradingContext'
import type { TradingBotConfig } from '../../shared/types/trading'
import { brokerService } from './services/brokerService'

function App(): React.JSX.Element {
  const [brokerStatus, setBrokerStatus] = useState<{
    isConnected: boolean
    isBotActive: boolean
    error?: string
  }>({
    isConnected: false,
    isBotActive: false
  })

  /**
   * Handles trading bot start
   */
  const handleTradingBotStart = useCallback(async (config: TradingBotConfig) => {
    try {
      console.log('Starting trading bot with config:', config)

      // Ensure broker is connected first
      const statusResponse = await brokerService.getBrokerStatus()
      if (!statusResponse.success || !statusResponse.data?.isConnected) {
        console.log('Broker not connected, attempting to connect...')
        const connectResponse = await brokerService.connectBroker()
        if (!connectResponse.success) {
          throw new Error(connectResponse.error || 'Failed to connect broker')
        }
      }

      // Start the trading bot
      const response = await brokerService.startTradingBot(config)
      if (!response.success) {
        throw new Error(response.error || 'Failed to start trading bot')
      }

      console.log('Trading bot started successfully:', response.message)
      setBrokerStatus((prev) => ({ ...prev, isBotActive: true, error: undefined }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('Failed to start trading bot:', errorMessage)
      setBrokerStatus((prev) => ({ ...prev, error: errorMessage }))

      // Show error to user (you might want to use a toast notification here)
      alert(`Failed to start trading bot: ${errorMessage}`)
    }
  }, [])

  /**
   * Handles trading bot stop
   */
  const handleTradingBotStop = useCallback(async () => {
    try {
      console.log('Stopping trading bot...')

      const response = await brokerService.stopTradingBot()
      if (!response.success) {
        throw new Error(response.error || 'Failed to stop trading bot')
      }

      console.log('Trading bot stopped successfully:', response.message)
      setBrokerStatus((prev) => ({ ...prev, isBotActive: false, error: undefined }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('Failed to stop trading bot:', errorMessage)
      setBrokerStatus((prev) => ({ ...prev, error: errorMessage }))

      // Show error to user (you might want to use a toast notification here)
      alert(`Failed to stop trading bot: ${errorMessage}`)
    }
  }, [])

  /**
   * Handles trading bot configuration changes
   */
  const handleConfigChange = useCallback((config: Partial<TradingBotConfig>) => {
    console.log('Trading bot config changed:', config)
  }, [])

  /**
   * Updates broker status periodically
   */
  useEffect(() => {
    const updateBrokerStatus = async (): Promise<void> => {
      try {
        const response = await brokerService.getBrokerStatus()
        if (response.success && response.data) {
          setBrokerStatus({
            isConnected: response.data.isConnected,
            isBotActive: response.data.isBotActive,
            error: undefined
          })
        }
      } catch (error) {
        console.error('Failed to get broker status:', error)
      }
    }

    // Update status immediately
    updateBrokerStatus()

    // Set up periodic status updates
    const interval = setInterval(updateBrokerStatus, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  /**
   * Set up broker event listeners
   */
  useEffect(() => {
    const cleanup = brokerService.setupEventListeners({
      onConnected: () => {
        console.log('Broker connected')
        setBrokerStatus((prev) => ({ ...prev, isConnected: true, error: undefined }))
      },
      onDisconnected: () => {
        console.log('Broker disconnected')
        setBrokerStatus((prev) => ({ ...prev, isConnected: false, isBotActive: false }))
      },
      onError: (error: string) => {
        console.error('Broker error:', error)
        setBrokerStatus((prev) => ({ ...prev, error }))
      },
      onBotStarted: (config: unknown) => {
        console.log('Bot started event received:', config)
        setBrokerStatus((prev) => ({ ...prev, isBotActive: true }))
      },
      onBotStopped: () => {
        console.log('Bot stopped event received')
        setBrokerStatus((prev) => ({ ...prev, isBotActive: false }))
      }
    })

    return cleanup
  }, [])

  return (
    <TradingContextProvider>
      <div className="min-h-screen bg-gray-900 text-white">
        {/* Header */}
        <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
          <div className="max-w-6xl mx-auto flex items-center justify-between">
            {/* Left side - Title and Description */}
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-white">Kwartani Trading Bot</h1>
              <p className="text-gray-400 text-sm mt-1">
                Automated trading interface for PocketOption
              </p>
            </div>

            {/* Right side - Status and Balance Display */}
            <div className="flex items-center space-x-4">
              {/* Broker Status Indicator */}
              <div className="flex items-center space-x-2">
                <div
                  className={`w-3 h-3 rounded-full ${
                    brokerStatus.isConnected ? 'bg-green-500' : 'bg-red-500'
                  }`}
                  title={brokerStatus.isConnected ? 'Broker Connected' : 'Broker Disconnected'}
                />
                <span className="text-sm text-gray-400">
                  {brokerStatus.isConnected ? 'Connected' : 'Disconnected'}
                </span>
                {brokerStatus.isBotActive && (
                  <span className="text-xs bg-green-600 text-white px-2 py-1 rounded">
                    Bot Active
                  </span>
                )}
                {brokerStatus.error && (
                  <span
                    className="text-xs bg-red-600 text-white px-2 py-1 rounded"
                    title={brokerStatus.error}
                  >
                    Error
                  </span>
                )}
              </div>

              {/* Balance Display */}
              <HeaderBalance currencySymbol="$" ariaLabel="Current account balance display" />
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 py-8">
          <div className="max-w-6xl mx-auto px-6">
            <TradingBotUI
              onStart={handleTradingBotStart}
              onStop={handleTradingBotStop}
              onConfigChange={handleConfigChange}
            />
          </div>
        </main>

        {/* Footer */}
        <footer className="bg-gray-800 border-t border-gray-700 px-6 py-4 mt-8">
          <div className="max-w-6xl mx-auto text-center text-gray-400 text-sm">
            <p>Kwartani v7 - Trading Bot Interface</p>
            <p className="mt-1">Built with Electron, React, and TypeScript</p>
          </div>
        </footer>
      </div>
    </TradingContextProvider>
  )
}

export default App
