/**
 * Trading Context Hooks
 * Custom hooks for accessing trading context and formatted data
 */

import { useContext } from 'react'
import TradingContext from '../contexts/TradingContext'

/**
 * Trading context value interface
 */
interface TradingContextValue {
  /** Current trading statistics */
  tradingStats: import('../../../shared/types/trading').TradingStatistics
  /** Function to update trading statistics */
  setTradingStats: React.Dispatch<
    React.SetStateAction<import('../../../shared/types/trading').TradingStatistics>
  >
  /** Function to reset trading statistics */
  resetTradingStats: (initialCapital?: number) => void
  /** Function to update balance */
  updateBalance: (newBalance: number) => void
}

/**
 * Hook to use trading context
 */
export const useTradingContext = (): TradingContextValue => {
  const context = useContext(TradingContext)
  if (context === undefined) {
    throw new Error('useTradingContext must be used within a TradingContextProvider')
  }
  return context
}

/**
 * Hook to get current balance with formatting
 */
export const useFormattedBalance = (currencySymbol: string = '$'): string => {
  const { tradingStats } = useTradingContext()

  const formatCurrency = (value: number): string => {
    if (!Number.isFinite(value)) {
      return `${currencySymbol}0.00`
    }

    const isNegative = value < 0
    const absValue = Math.abs(value)
    const formatted = absValue.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
    return `${isNegative ? '-' : ''}${currencySymbol}${formatted}`
  }

  return formatCurrency(tradingStats.balance)
}
