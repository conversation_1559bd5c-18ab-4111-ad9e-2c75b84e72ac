import { app, shell, <PERSON><PERSON>erWindow, ipc<PERSON>ain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import { PocketOption } from './broker/PocketOption'
import { logger } from '../shared/utils/logger'
import { SignalEngineEventEmitter } from './events/SignalEngineEventEmitter'
import type { TradingBotConfig } from '../shared/types/trading'

// Global broker instance
let brokerInstance: PocketOption | null = null

/**
 * Sets up IPC handlers for trading bot operations
 */
function setupTradingBotIPC(): void {
  // Handle start bot request
  ipcMain.handle('trading-bot:start', async (_, config: TradingBotConfig) => {
    try {
      if (!brokerInstance) {
        throw new Error('Broker not initialized')
      }

      if (!brokerInstance.isConnected()) {
        throw new Error('Broker not connected')
      }

      await brokerInstance.startBot()

      return {
        success: true,
        message: 'Trading bot started successfully',
        config
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logger.error('IPC', `Failed to start trading bot: ${errorMessage}`)

      return {
        success: false,
        error: errorMessage
      }
    }
  })

  // Handle stop bot request
  ipcMain.handle('trading-bot:stop', async () => {
    try {
      if (!brokerInstance) {
        throw new Error('Broker not initialized')
      }

      brokerInstance.stopBot()

      return {
        success: true,
        message: 'Trading bot stopped successfully'
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logger.error('IPC', `Failed to stop trading bot: ${errorMessage}`)

      return {
        success: false,
        error: errorMessage
      }
    }
  })

  // Handle get bot status request
  ipcMain.handle('trading-bot:status', async () => {
    try {
      if (!brokerInstance) {
        return {
          success: false,
          error: 'Broker not initialized'
        }
      }

      return {
        success: true,
        data: {
          isConnected: brokerInstance.isConnected(),
          connectionState: brokerInstance.getConnectionState(),
          heartbeatHealth: brokerInstance.getHeartbeatHealth(),
          isBotActive: brokerInstance.isBotActive(),
          heartbeatStats: brokerInstance.getHeartbeatStats()
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logger.error('IPC', `Failed to get bot status: ${errorMessage}`)

      return {
        success: false,
        error: errorMessage
      }
    }
  })

  // Handle broker connection request
  ipcMain.handle('broker:connect', async () => {
    try {
      if (!brokerInstance) {
        // Try to reinitialize broker
        brokerInstance = await initializeBroker()
        if (!brokerInstance) {
          throw new Error('Failed to initialize broker')
        }
      }

      if (!brokerInstance.isConnected()) {
        await brokerInstance.connect()
      }

      return {
        success: true,
        message: 'Broker connected successfully'
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logger.error('IPC', `Failed to connect broker: ${errorMessage}`)

      return {
        success: false,
        error: errorMessage
      }
    }
  })

  // Handle broker disconnect request
  ipcMain.handle('broker:disconnect', async () => {
    try {
      if (brokerInstance) {
        brokerInstance.disconnect()
      }

      return {
        success: true,
        message: 'Broker disconnected successfully'
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logger.error('IPC', `Failed to disconnect broker: ${errorMessage}`)

      return {
        success: false,
        error: errorMessage
      }
    }
  })
}

async function initializeBroker(): Promise<PocketOption | null> {
  const session = import.meta.env.MAIN_VITE_SESSION_KEY
  const isDemo = import.meta.env.MAIN_VITE_DEMO === 'true'

  const broker = PocketOption.getInstance(session, isDemo)
  try {
    await broker.connect()
    brokerInstance = broker
    return broker
  } catch (error) {
    logger.error(
      `Broker`,
      `Failed to initialize broker: ${error instanceof Error ? error.message : error}`
    )
    brokerInstance = null
    return null
  }
}

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    show: false,
    alwaysOnTop: true,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  // Register window with Signal Engine event emitter
  const eventEmitter = SignalEngineEventEmitter.getInstance()
  eventEmitter.registerWindow(mainWindow)

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  await initializeBroker()

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // IPC test
  ipcMain.on('ping', () => console.log('pong'))

  // Trading bot IPC handlers
  setupTradingBotIPC()

  createWindow()

  // Set up Signal Engine event system
  setupSignalEngineEvents()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

/**
 * Set up Signal Engine event system
 */
function setupSignalEngineEvents(): void {
  const eventEmitter = SignalEngineEventEmitter.getInstance()

  // Set up periodic heartbeat and performance updates
  setInterval(() => {
    try {
      // Get Signal Engine instance if available
      const { SignalEngine } = require('./core/SignalEngine')
      const signalEngine = SignalEngine.getInstance()

      if (signalEngine) {
        // Emit performance update
        signalEngine.emitPerformanceUpdate()

        // Emit heartbeat with metrics
        const metrics = signalEngine.getPerformanceMetrics()
        eventEmitter.broadcastHeartbeat({
          signalCount: metrics.totalSignalsGenerated,
          activeStrategies: signalEngine.getActiveStrategies().length,
          processingTime: metrics.averageProcessingTime
        })
      }
    } catch (error) {
      logger.error('SignalEngineEvents', 'Failed to emit periodic updates:', error)
    }
  }, 30000) // Every 30 seconds

  // Set up connection status monitoring
  eventEmitter.emitSignalEngineEvent('connection-status', {
    status: 'connected',
    timestamp: Date.now(),
    details: 'Signal Engine event system initialized'
  })

  logger.info('SignalEngineEvents', 'Signal Engine event system initialized')
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
