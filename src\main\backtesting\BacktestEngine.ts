/**
 * Backtesting Engine Implementation
 * Comprehensive backtesting system for strategy evaluation with realistic market simulation
 */

import { EventEmitter } from 'events'
import { logger } from '../../shared/utils/logger'
import {
  DEFAULT_BACKTEST_CONFIG,
  BACKTEST_VALIDATION_CONSTRAINTS,
  PERFORMANCE_CONSTANTS,
  BACKTEST_ERROR_MESSAGES,
  BACKTEST_EVENTS
} from '../../shared/constants/backtesting'
import type {
  BacktestEngine,
  BacktestConfig,
  BacktestResult,
  TradeRecord,
  PortfolioSnapshot,
  BacktestPerformanceMetrics,
  BacktestEvent,
  BacktestEventCallback,
  PositionSizer,
  RiskManager,
  MarketSimulator
} from '../../shared/types/backtesting'
import type { Strategy, GeneratedSignal, IndicatorDataPoint } from '../../shared/types/signals'
import { SignalEngine } from '../core/SignalEngine'

/**
 * Main backtesting engine class
 */
export class BacktestingEngine extends EventEmitter implements BacktestEngine {
  private _trades: TradeRecord[] = []
  private _portfolioHistory: PortfolioSnapshot[] = []
  private _currentPortfolio: PortfolioSnapshot
  private _openTrades: Map<string, TradeRecord> = new Map()
  private _customMetrics: Map<
    string,
    (trades: TradeRecord[], portfolio: PortfolioSnapshot[]) => number
  > = new Map()
  private _positionSizer: PositionSizer
  private _riskManager: RiskManager
  private _marketSimulator: MarketSimulator
  private _isRunning: boolean = false
  private _startTime: number = 0

  /**
   * Create a new backtesting engine
   */
  constructor(
    positionSizer?: PositionSizer,
    riskManager?: RiskManager,
    marketSimulator?: MarketSimulator
  ) {
    super()

    // Initialize with default implementations if not provided
    this._positionSizer = positionSizer || new DefaultPositionSizer()
    this._riskManager = riskManager || new DefaultRiskManager()
    this._marketSimulator = marketSimulator || new DefaultMarketSimulator()

    // Initialize portfolio
    this._currentPortfolio = this._createInitialPortfolio(DEFAULT_BACKTEST_CONFIG.initialCapital)

    logger.debug('BacktestingEngine', 'Backtesting engine initialized')
  }

  /**
   * Run backtest with given strategy and data
   */
  public async runBacktest(
    strategy: Strategy,
    historicalData: IndicatorDataPoint[],
    config: BacktestConfig = DEFAULT_BACKTEST_CONFIG,
    benchmark?: IndicatorDataPoint[]
  ): Promise<BacktestResult> {
    this._startTime = performance.now()
    this._isRunning = true

    try {
      // Validate inputs
      this._validateBacktestInputs(strategy, historicalData, config)

      // Reset engine state
      this.reset()

      // Initialize portfolio with config
      this._currentPortfolio = this._createInitialPortfolio(config.initialCapital)

      // Emit start event
      this._emitEvent('backtest-started', {
        strategy: strategy.name,
        dataPoints: historicalData.length,
        config
      })

      logger.info('BacktestingEngine', `Starting backtest for strategy: ${strategy.name}`)

      // Process each data point
      for (let i = 0; i < historicalData.length; i++) {
        const dataPoint = historicalData[i]

        // Add data to strategy and get signal
        const signal = strategy.addData(dataPoint)

        // Process signal if generated
        if (signal) {
          await this._processSignal(signal, dataPoint.value, config)
        }

        // Check open trades for exit conditions
        await this._checkOpenTrades(dataPoint.value, config)

        // Update portfolio snapshot
        this._updatePortfolio(dataPoint.timestamp, dataPoint.value)

        // Add small delay to simulate real-time processing
        if (config.execution.executionDelay > 0) {
          await this._delay(config.execution.executionDelay)
        }
      }

      // Close any remaining open trades
      await this._closeAllOpenTrades(historicalData[historicalData.length - 1].value, config)

      // Calculate final metrics
      const metrics = this._calculatePerformanceMetrics(config, benchmark)

      // Create result object
      const result: BacktestResult = {
        config,
        strategy,
        metrics,
        trades: [...this._trades],
        portfolioHistory: [...this._portfolioHistory],
        equityCurve: this._generateEquityCurve(),
        drawdownCurve: this._generateDrawdownCurve(),
        monthlyReturns: this._calculateMonthlyReturns(),
        executionTime: performance.now() - this._startTime,
        startDate: historicalData[0].timestamp,
        endDate: historicalData[historicalData.length - 1].timestamp,
        benchmark: benchmark ? this._calculateBenchmarkMetrics(benchmark) : undefined
      }

      // Emit completion event
      this._emitEvent('backtest-completed', {
        strategy: strategy.name,
        totalTrades: this._trades.length,
        totalReturn: metrics.totalReturn,
        sharpeRatio: metrics.sharpeRatio,
        maxDrawdown: metrics.maxDrawdown
      })

      logger.info(
        'BacktestingEngine',
        `Backtest completed for ${strategy.name}: ${metrics.totalReturn.toFixed(2)}% return`
      )

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('BacktestingEngine', `Backtest failed: ${errorMessage}`)

      this._emitEvent('error-occurred', {
        error: errorMessage,
        strategy: strategy.name
      })

      throw error
    } finally {
      this._isRunning = false
    }
  }

  /**
   * Get current portfolio state
   */
  public getPortfolioState(): PortfolioSnapshot {
    return { ...this._currentPortfolio }
  }

  /**
   * Get all trade records
   */
  public getTrades(): TradeRecord[] {
    return [...this._trades]
  }

  /**
   * Get performance metrics
   */
  public getMetrics(): BacktestPerformanceMetrics {
    return this._calculatePerformanceMetrics(DEFAULT_BACKTEST_CONFIG)
  }

  /**
   * Reset engine state
   */
  public reset(): void {
    this._trades = []
    this._portfolioHistory = []
    this._openTrades.clear()
    this._currentPortfolio = this._createInitialPortfolio(DEFAULT_BACKTEST_CONFIG.initialCapital)
    this._isRunning = false

    logger.debug('BacktestingEngine', 'Engine state reset')
  }

  /**
   * Add custom metric calculator
   */
  public addCustomMetric(
    name: string,
    calculator: (trades: TradeRecord[], portfolio: PortfolioSnapshot[]) => number
  ): void {
    this._customMetrics.set(name, calculator)
    logger.debug('BacktestingEngine', `Custom metric added: ${name}`)
  }

  /**
   * Process a trading signal
   */
  private async _processSignal(
    signal: GeneratedSignal,
    currentPrice: number,
    config: BacktestConfig
  ): Promise<void> {
    try {
      // Check if trade should be executed based on risk management
      if (
        !this._riskManager.shouldExecuteTrade(signal, currentPrice, this._currentPortfolio, config)
      ) {
        logger.debug('BacktestingEngine', `Trade rejected by risk manager: ${signal.signal}`)
        return
      }

      // Calculate position size
      const positionSize = this._positionSizer.calculateSize(
        signal,
        currentPrice,
        this._currentPortfolio.totalValue,
        config
      )

      if (positionSize <= 0) {
        logger.debug('BacktestingEngine', `Invalid position size calculated: ${positionSize}`)
        return
      }

      // Simulate order execution
      const execution = this._marketSimulator.executeOrder(
        signal,
        currentPrice,
        positionSize,
        config.execution
      )

      // Create trade record
      const trade: TradeRecord = {
        id: this._generateTradeId(),
        entrySignal: signal,
        entryTime: signal.timestamp,
        entryPrice: execution.executedPrice,
        direction: signal.signal === 'BUY' ? 'LONG' : 'SHORT',
        size: execution.executedSize,
        status: 'OPEN',
        commission: execution.commission,
        slippage: execution.slippage,
        metadata: {
          confidence: signal.confidence,
          strategy: signal.strategy
        }
      }

      // Add to open trades
      this._openTrades.set(trade.id, trade)

      // Update portfolio
      this._updatePortfolioForTrade(trade, 'OPEN')

      // Emit trade opened event
      this._emitEvent('trade-opened', {
        tradeId: trade.id,
        signal: signal.signal,
        price: execution.executedPrice,
        size: execution.executedSize
      })

      logger.debug(
        'BacktestingEngine',
        `Trade opened: ${trade.id} ${trade.direction} ${trade.size} @ ${trade.entryPrice}`
      )
    } catch (error) {
      logger.error('BacktestingEngine', `Error processing signal: ${error}`)
    }
  }

  /**
   * Check open trades for exit conditions
   */
  private async _checkOpenTrades(currentPrice: number, config: BacktestConfig): Promise<void> {
    for (const [tradeId, trade] of this._openTrades) {
      // Check risk management exit conditions
      if (this._riskManager.shouldClosePosition(trade, currentPrice, config.riskManagement)) {
        await this._closeTrade(trade, currentPrice, config, 'RISK_MANAGEMENT')
      }
    }
  }

  /**
   * Close a specific trade
   */
  private async _closeTrade(
    trade: TradeRecord,
    exitPrice: number,
    config: BacktestConfig,
    exitReason: string
  ): Promise<void> {
    try {
      // Calculate P&L
      const pnl = this._calculateTradePnL(trade, exitPrice)
      const pnlPercentage = pnl / (trade.entryPrice * trade.size)

      // Update trade record
      trade.exitPrice = exitPrice
      trade.exitTime = Date.now()
      trade.status = 'CLOSED'
      trade.pnl = pnl
      trade.pnlPercentage = pnlPercentage
      trade.duration = trade.exitTime - trade.entryTime
      trade.metadata = { ...trade.metadata, exitReason }

      // Remove from open trades and add to completed trades
      this._openTrades.delete(trade.id)
      this._trades.push(trade)

      // Update portfolio
      this._updatePortfolioForTrade(trade, 'CLOSE')

      // Emit trade closed event
      this._emitEvent('trade-closed', {
        tradeId: trade.id,
        pnl: pnl,
        pnlPercentage: pnlPercentage,
        duration: trade.duration,
        exitReason
      })

      logger.debug(
        'BacktestingEngine',
        `Trade closed: ${trade.id} P&L: ${pnl.toFixed(2)} (${(pnlPercentage * 100).toFixed(2)}%)`
      )
    } catch (error) {
      logger.error('BacktestingEngine', `Error closing trade: ${error}`)
    }
  }

  /**
   * Close all open trades at the end of backtest
   */
  private async _closeAllOpenTrades(finalPrice: number, config: BacktestConfig): Promise<void> {
    const openTrades = Array.from(this._openTrades.values())
    for (const trade of openTrades) {
      await this._closeTrade(trade, finalPrice, config, 'END_OF_BACKTEST')
    }
  }

  /**
   * Calculate trade P&L
   */
  private _calculateTradePnL(trade: TradeRecord, exitPrice: number): number {
    const entryValue = trade.entryPrice * trade.size
    const exitValue = exitPrice * trade.size

    let pnl: number
    if (trade.direction === 'LONG') {
      pnl = exitValue - entryValue
    } else {
      pnl = entryValue - exitValue
    }

    // Subtract commission and slippage
    pnl -= trade.commission
    pnl -= trade.slippage

    return pnl
  }

  /**
   * Update portfolio for trade events
   */
  private _updatePortfolioForTrade(trade: TradeRecord, action: 'OPEN' | 'CLOSE'): void {
    if (action === 'OPEN') {
      // Reduce cash by trade value plus costs
      const tradeValue = trade.entryPrice * trade.size
      this._currentPortfolio.cash -= tradeValue + trade.commission + trade.slippage
      this._currentPortfolio.positionValue += tradeValue
      this._currentPortfolio.openPositions += 1
    } else {
      // Add P&L to cash
      this._currentPortfolio.cash += trade.pnl || 0
      this._currentPortfolio.positionValue -= trade.entryPrice * trade.size
      this._currentPortfolio.openPositions -= 1
    }

    // Update total value
    this._currentPortfolio.totalValue =
      this._currentPortfolio.cash + this._currentPortfolio.positionValue
  }

  /**
   * Update portfolio snapshot
   */
  private _updatePortfolio(timestamp: number, currentPrice: number): void {
    // Calculate current position value based on current price
    let currentPositionValue = 0
    for (const trade of this._openTrades.values()) {
      const currentValue = currentPrice * trade.size
      if (trade.direction === 'LONG') {
        currentPositionValue += currentValue
      } else {
        currentPositionValue +=
          trade.entryPrice * trade.size - (currentValue - trade.entryPrice * trade.size)
      }
    }

    const totalValue = this._currentPortfolio.cash + currentPositionValue
    const initialValue =
      this._portfolioHistory.length > 0 ? this._portfolioHistory[0].totalValue : totalValue
    const totalReturn = (totalValue - initialValue) / initialValue

    // Calculate drawdown
    const peakValue = Math.max(...this._portfolioHistory.map((p) => p.totalValue), totalValue)
    const drawdown = (peakValue - totalValue) / peakValue
    const maxDrawdown = Math.max(this._currentPortfolio.maxDrawdown, drawdown)

    const snapshot: PortfolioSnapshot = {
      timestamp,
      totalValue,
      cash: this._currentPortfolio.cash,
      positionValue: currentPositionValue,
      openPositions: this._openTrades.size,
      drawdown,
      maxDrawdown,
      totalReturn
    }

    this._currentPortfolio = snapshot
    this._portfolioHistory.push(snapshot)

    // Emit portfolio update event
    this._emitEvent('portfolio-updated', {
      totalValue,
      totalReturn,
      drawdown,
      openPositions: this._openTrades.size
    })
  }

  /**
   * Create initial portfolio snapshot
   */
  private _createInitialPortfolio(initialCapital: number): PortfolioSnapshot {
    return {
      timestamp: Date.now(),
      totalValue: initialCapital,
      cash: initialCapital,
      positionValue: 0,
      openPositions: 0,
      drawdown: 0,
      maxDrawdown: 0,
      totalReturn: 0
    }
  }

  /**
   * Generate unique trade ID
   */
  private _generateTradeId(): string {
    return `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Validate backtest inputs
   */
  private _validateBacktestInputs(
    strategy: Strategy,
    historicalData: IndicatorDataPoint[],
    config: BacktestConfig
  ): void {
    if (!strategy || !strategy.isReady) {
      throw new Error(BACKTEST_ERROR_MESSAGES.INVALID_STRATEGY)
    }

    if (!historicalData || historicalData.length < 10) {
      throw new Error(BACKTEST_ERROR_MESSAGES.INSUFFICIENT_DATA)
    }

    // Validate config parameters
    const constraints = BACKTEST_VALIDATION_CONSTRAINTS

    if (
      config.initialCapital < constraints.initialCapital.min ||
      config.initialCapital > constraints.initialCapital.max
    ) {
      throw new Error(BACKTEST_ERROR_MESSAGES.INVALID_INITIAL_CAPITAL)
    }

    if (
      config.commission < constraints.commission.min ||
      config.commission > constraints.commission.max
    ) {
      throw new Error(BACKTEST_ERROR_MESSAGES.INVALID_COMMISSION)
    }

    if (config.slippage < constraints.slippage.min || config.slippage > constraints.slippage.max) {
      throw new Error(BACKTEST_ERROR_MESSAGES.INVALID_SLIPPAGE)
    }
  }

  /**
   * Calculate comprehensive performance metrics
   */
  private _calculatePerformanceMetrics(
    config: BacktestConfig,
    benchmark?: IndicatorDataPoint[]
  ): BacktestPerformanceMetrics {
    if (this._trades.length < PERFORMANCE_CONSTANTS.MIN_TRADES_FOR_STATISTICS) {
      logger.warn(
        'BacktestingEngine',
        `Insufficient trades for reliable statistics: ${this._trades.length}`
      )
    }

    const returns = this._calculateReturns()
    const winningTrades = this._trades.filter((t) => (t.pnl || 0) > 0)
    const losingTrades = this._trades.filter((t) => (t.pnl || 0) < 0)

    const totalReturn = this._currentPortfolio.totalReturn
    const annualizedReturn = this._calculateAnnualizedReturn(returns)
    const volatility = this._calculateVolatility(returns)
    const sharpeRatio = this._calculateSharpeRatio(annualizedReturn, volatility)
    const maxDrawdown = this._currentPortfolio.maxDrawdown

    return {
      totalReturn: totalReturn * 100, // Convert to percentage
      annualizedReturn: annualizedReturn * 100,
      maxDrawdown: maxDrawdown * 100,
      sharpeRatio,
      sortinoRatio: this._calculateSortinoRatio(returns),
      calmarRatio: annualizedReturn / maxDrawdown,
      winRate: (winningTrades.length / this._trades.length) * 100,
      profitFactor: this._calculateProfitFactor(winningTrades, losingTrades),
      averageWin:
        winningTrades.length > 0
          ? (winningTrades.reduce((sum, t) => sum + (t.pnlPercentage || 0), 0) /
              winningTrades.length) *
            100
          : 0,
      averageLoss:
        losingTrades.length > 0
          ? (losingTrades.reduce((sum, t) => sum + Math.abs(t.pnlPercentage || 0), 0) /
              losingTrades.length) *
            100
          : 0,
      largestWin:
        winningTrades.length > 0
          ? Math.max(...winningTrades.map((t) => t.pnlPercentage || 0)) * 100
          : 0,
      largestLoss:
        losingTrades.length > 0
          ? Math.min(...losingTrades.map((t) => t.pnlPercentage || 0)) * 100
          : 0,
      totalTrades: this._trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      averageTradeDuration: this._calculateAverageTradeDuration(),
      volatility: volatility * 100,
      valueAtRisk: this._calculateVaR(returns) * 100,
      expectedShortfall: this._calculateExpectedShortfall(returns) * 100,
      recoveryFactor: totalReturn / maxDrawdown,
      ulcerIndex: this._calculateUlcerIndex()
    }
  }

  /**
   * Calculate returns array from portfolio history
   */
  private _calculateReturns(): number[] {
    const returns: number[] = []
    for (let i = 1; i < this._portfolioHistory.length; i++) {
      const prevValue = this._portfolioHistory[i - 1].totalValue
      const currentValue = this._portfolioHistory[i].totalValue
      returns.push((currentValue - prevValue) / prevValue)
    }
    return returns
  }

  /**
   * Calculate annualized return
   */
  private _calculateAnnualizedReturn(returns: number[]): number {
    if (returns.length === 0) return 0

    const totalReturn = returns.reduce((acc, r) => acc * (1 + r), 1) - 1
    const periods = returns.length
    const periodsPerYear = PERFORMANCE_CONSTANTS.TRADING_DAYS_PER_YEAR

    return Math.pow(1 + totalReturn, periodsPerYear / periods) - 1
  }

  /**
   * Calculate volatility (standard deviation of returns)
   */
  private _calculateVolatility(returns: number[]): number {
    if (returns.length < 2) return 0

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length
    const variance =
      returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / (returns.length - 1)

    return Math.sqrt(variance * PERFORMANCE_CONSTANTS.TRADING_DAYS_PER_YEAR)
  }

  /**
   * Calculate Sharpe ratio
   */
  private _calculateSharpeRatio(annualizedReturn: number, volatility: number): number {
    if (volatility === 0) return 0
    return (annualizedReturn - PERFORMANCE_CONSTANTS.RISK_FREE_RATE) / volatility
  }

  /**
   * Calculate Sortino ratio
   */
  private _calculateSortinoRatio(returns: number[]): number {
    if (returns.length === 0) return 0

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length
    const downside = returns.filter((r) => r < 0)

    if (downside.length === 0) return Infinity

    const downsideVariance = downside.reduce((sum, r) => sum + Math.pow(r, 2), 0) / downside.length
    const downsideDeviation = Math.sqrt(
      downsideVariance * PERFORMANCE_CONSTANTS.TRADING_DAYS_PER_YEAR
    )

    return (
      (mean * PERFORMANCE_CONSTANTS.TRADING_DAYS_PER_YEAR - PERFORMANCE_CONSTANTS.RISK_FREE_RATE) /
      downsideDeviation
    )
  }

  /**
   * Calculate profit factor
   */
  private _calculateProfitFactor(
    winningTrades: TradeRecord[],
    losingTrades: TradeRecord[]
  ): number {
    const grossProfit = winningTrades.reduce((sum, t) => sum + (t.pnl || 0), 0)
    const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + (t.pnl || 0), 0))

    return grossLoss === 0 ? Infinity : grossProfit / grossLoss
  }

  /**
   * Calculate average trade duration in hours
   */
  private _calculateAverageTradeDuration(): number {
    if (this._trades.length === 0) return 0

    const totalDuration = this._trades.reduce((sum, t) => sum + (t.duration || 0), 0)
    return totalDuration / this._trades.length / (1000 * 60 * 60) // Convert to hours
  }

  /**
   * Calculate Value at Risk (VaR)
   */
  private _calculateVaR(returns: number[]): number {
    if (returns.length === 0) return 0

    const sortedReturns = [...returns].sort((a, b) => a - b)
    const index = Math.floor(
      (1 - PERFORMANCE_CONSTANTS.VAR_CONFIDENCE_LEVEL) * sortedReturns.length
    )

    return sortedReturns[index] || 0
  }

  /**
   * Calculate Expected Shortfall (Conditional VaR)
   */
  private _calculateExpectedShortfall(returns: number[]): number {
    if (returns.length === 0) return 0

    const var95 = this._calculateVaR(returns)
    const tailReturns = returns.filter((r) => r <= var95)

    return tailReturns.length > 0
      ? tailReturns.reduce((sum, r) => sum + r, 0) / tailReturns.length
      : 0
  }

  /**
   * Calculate Ulcer Index
   */
  private _calculateUlcerIndex(): number {
    if (this._portfolioHistory.length === 0) return 0

    const drawdowns = this._portfolioHistory.map((p) => p.drawdown)
    const squaredDrawdowns = drawdowns.map((d) => d * d)
    const meanSquaredDrawdown =
      squaredDrawdowns.reduce((sum, d) => sum + d, 0) / squaredDrawdowns.length

    return Math.sqrt(meanSquaredDrawdown)
  }

  /**
   * Generate equity curve data
   */
  private _generateEquityCurve(): { timestamp: number; value: number }[] {
    return this._portfolioHistory.map((p) => ({
      timestamp: p.timestamp,
      value: p.totalValue
    }))
  }

  /**
   * Generate drawdown curve data
   */
  private _generateDrawdownCurve(): { timestamp: number; drawdown: number }[] {
    return this._portfolioHistory.map((p) => ({
      timestamp: p.timestamp,
      drawdown: p.drawdown
    }))
  }

  /**
   * Calculate monthly returns
   */
  private _calculateMonthlyReturns(): { month: string; return: number }[] {
    const monthlyReturns: { month: string; return: number }[] = []
    const monthlyData = new Map<string, { start: number; end: number }>()

    // Group portfolio snapshots by month
    for (const snapshot of this._portfolioHistory) {
      const date = new Date(snapshot.timestamp)
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`

      if (!monthlyData.has(monthKey)) {
        monthlyData.set(monthKey, { start: snapshot.totalValue, end: snapshot.totalValue })
      } else {
        monthlyData.get(monthKey)!.end = snapshot.totalValue
      }
    }

    // Calculate returns for each month
    for (const [month, data] of monthlyData) {
      const monthReturn = (data.end - data.start) / data.start
      monthlyReturns.push({ month, return: monthReturn * 100 })
    }

    return monthlyReturns
  }

  /**
   * Calculate benchmark metrics
   */
  private _calculateBenchmarkMetrics(benchmark: IndicatorDataPoint[]): {
    totalReturn: number
    volatility: number
    sharpeRatio: number
  } {
    if (benchmark.length < 2) {
      return { totalReturn: 0, volatility: 0, sharpeRatio: 0 }
    }

    const benchmarkReturns: number[] = []
    for (let i = 1; i < benchmark.length; i++) {
      const prevValue = benchmark[i - 1].value
      const currentValue = benchmark[i].value
      benchmarkReturns.push((currentValue - prevValue) / prevValue)
    }

    const totalReturn = benchmarkReturns.reduce((acc, r) => acc * (1 + r), 1) - 1
    const volatility = this._calculateVolatility(benchmarkReturns)
    const annualizedReturn = this._calculateAnnualizedReturn(benchmarkReturns)
    const sharpeRatio = this._calculateSharpeRatio(annualizedReturn, volatility)

    return {
      totalReturn: totalReturn * 100,
      volatility: volatility * 100,
      sharpeRatio
    }
  }

  /**
   * Emit backtest event
   */
  private _emitEvent(type: keyof typeof BACKTEST_EVENTS, data: Record<string, unknown>): void {
    const event: BacktestEvent = {
      type: type as any,
      timestamp: Date.now(),
      data
    }

    this.emit(type, event)
    logger.debug('BacktestingEngine', `Event emitted: ${type}`)
  }

  /**
   * Add delay for simulation
   */
  private async _delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * Run backtest using Signal Engine strategies
   * Enhanced method for testing Signal Engine strategies with comprehensive analysis
   */
  public async runSignalEngineBacktest(
    strategyNames: string | string[],
    strategyConfig: any,
    historicalData: IndicatorDataPoint[],
    config: BacktestConfig = DEFAULT_BACKTEST_CONFIG
  ): Promise<BacktestResult & { signalEngineMetrics: any }> {
    const signalEngine = SignalEngine.getInstance()

    try {
      // Create strategy using Signal Engine
      const strategy = signalEngine.createStrategy(strategyNames, strategyConfig)
      if (!strategy) {
        throw new Error(
          `Failed to create Signal Engine strategy: ${Array.isArray(strategyNames) ? strategyNames.join(', ') : strategyNames}`
        )
      }

      // Run standard backtest
      const backtestResult = await this.runBacktest(strategy, historicalData, config)

      // Get Signal Engine performance metrics
      const signalEngineMetrics = signalEngine.getPerformanceMetrics()

      // Enhanced analysis for Signal Engine strategies
      const enhancedMetrics = {
        ...signalEngineMetrics,
        strategyType: Array.isArray(strategyNames) ? 'multi-indicator' : 'single-indicator',
        indicatorsUsed: Array.isArray(strategyNames) ? strategyNames : [strategyNames],
        signalQuality: this._analyzeSignalQuality(backtestResult.trades),
        strategyEfficiency: this._calculateStrategyEfficiency(
          backtestResult.trades,
          signalEngineMetrics
        )
      }

      logger.info(
        'BacktestingEngine',
        `Signal Engine backtest completed for ${Array.isArray(strategyNames) ? strategyNames.join('+') : strategyNames}`
      )

      return {
        ...backtestResult,
        signalEngineMetrics: enhancedMetrics
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('BacktestingEngine', `Signal Engine backtest failed: ${errorMessage}`)
      throw error
    }
  }

  /**
   * Analyze signal quality from trade results
   */
  private _analyzeSignalQuality(trades: TradeRecord[]): {
    averageConfidence: number
    confidenceDistribution: { low: number; medium: number; high: number }
    signalAccuracy: number
    profitableSignalRatio: number
  } {
    if (trades.length === 0) {
      return {
        averageConfidence: 0,
        confidenceDistribution: { low: 0, medium: 0, high: 0 },
        signalAccuracy: 0,
        profitableSignalRatio: 0
      }
    }

    const confidences = trades.map((t) => t.metadata?.confidence || 0)
    const averageConfidence = confidences.reduce((sum, c) => sum + c, 0) / confidences.length

    const confidenceDistribution = {
      low: confidences.filter((c) => c < 0.4).length / confidences.length,
      medium: confidences.filter((c) => c >= 0.4 && c < 0.7).length / confidences.length,
      high: confidences.filter((c) => c >= 0.7).length / confidences.length
    }

    const profitableTrades = trades.filter((t) => (t.pnl || 0) > 0)
    const signalAccuracy = profitableTrades.length / trades.length
    const profitableSignalRatio =
      profitableTrades.reduce((sum, t) => sum + (t.metadata?.confidence || 0), 0) /
      trades.reduce((sum, t) => sum + (t.metadata?.confidence || 0), 0)

    return {
      averageConfidence,
      confidenceDistribution,
      signalAccuracy,
      profitableSignalRatio
    }
  }

  /**
   * Calculate strategy efficiency metrics
   */
  private _calculateStrategyEfficiency(
    trades: TradeRecord[],
    signalEngineMetrics: any
  ): {
    signalToTradeRatio: number
    averageProcessingTime: number
    signalGenerationRate: number
    strategyUtilization: number
  } {
    const totalSignals = signalEngineMetrics?.totalSignalsGenerated || 0
    const totalTrades = trades.length

    return {
      signalToTradeRatio: totalSignals > 0 ? totalTrades / totalSignals : 0,
      averageProcessingTime: signalEngineMetrics?.averageProcessingTime || 0,
      signalGenerationRate: signalEngineMetrics?.signalGenerationRate || 0,
      strategyUtilization: totalSignals > 0 ? (totalTrades / totalSignals) * 100 : 0
    }
  }

  /**
   * Compare multiple Signal Engine strategies
   */
  public async compareSignalEngineStrategies(
    strategies: Array<{
      name: string
      indicators: string | string[]
      config: any
    }>,
    historicalData: IndicatorDataPoint[],
    backtestConfig: BacktestConfig = DEFAULT_BACKTEST_CONFIG
  ): Promise<Array<BacktestResult & { strategyName: string; signalEngineMetrics: any }>> {
    const results: Array<BacktestResult & { strategyName: string; signalEngineMetrics: any }> = []

    for (const strategy of strategies) {
      try {
        logger.info(
          'BacktestingEngine',
          `Running comparison backtest for strategy: ${strategy.name}`
        )

        const result = await this.runSignalEngineBacktest(
          strategy.indicators,
          strategy.config,
          historicalData,
          backtestConfig
        )

        results.push({
          ...result,
          strategyName: strategy.name
        })

        // Reset engine state between strategies
        this.reset()
      } catch (error) {
        logger.error(
          'BacktestingEngine',
          `Strategy comparison failed for ${strategy.name}: ${error}`
        )
        // Continue with other strategies
      }
    }

    // Log comparison summary
    if (results.length > 1) {
      this._logStrategyComparison(results)
    }

    return results
  }

  /**
   * Log strategy comparison summary
   */
  private _logStrategyComparison(results: Array<BacktestResult & { strategyName: string }>): void {
    console.log('\n📊 Strategy Comparison Summary')
    console.log('='.repeat(60))

    results.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.strategyName}`)
      console.log(`   Total Return: ${result.metrics.totalReturn.toFixed(2)}%`)
      console.log(`   Sharpe Ratio: ${result.metrics.sharpeRatio.toFixed(2)}`)
      console.log(`   Max Drawdown: ${result.metrics.maxDrawdown.toFixed(2)}%`)
      console.log(`   Total Trades: ${result.trades.length}`)
      console.log(`   Win Rate: ${result.metrics.winRate.toFixed(1)}%`)
    })

    // Find best performing strategy
    const bestStrategy = results.reduce((best, current) =>
      current.metrics.sharpeRatio > best.metrics.sharpeRatio ? current : best
    )

    console.log(`\n🏆 Best Performing Strategy: ${bestStrategy.strategyName}`)
    console.log(`   Sharpe Ratio: ${bestStrategy.metrics.sharpeRatio.toFixed(2)}`)
    console.log(`   Total Return: ${bestStrategy.metrics.totalReturn.toFixed(2)}%`)
  }
}

/**
 * Default position sizer implementation
 */
export class DefaultPositionSizer implements PositionSizer {
  /**
   * Calculate position size based on configuration
   */
  public calculateSize(
    signal: GeneratedSignal,
    currentPrice: number,
    portfolioValue: number,
    config: BacktestConfig
  ): number {
    const method = config.riskManagement.positionSizing

    switch (method) {
      case 'fixed':
        return (config.maxPositionSize * portfolioValue) / currentPrice

      case 'percentage':
        return (config.maxPositionSize * portfolioValue) / currentPrice

      case 'kelly':
        // Simplified Kelly criterion (would need historical win rate and avg win/loss)
        const kellyFraction = Math.min(0.25, config.riskPerTrade * 2) // Conservative approach
        return (kellyFraction * portfolioValue) / currentPrice

      case 'volatility':
        // Volatility-based sizing (simplified)
        const volatilityAdjustment = 1 / (1 + signal.confidence) // Higher confidence = larger position
        return (config.maxPositionSize * volatilityAdjustment * portfolioValue) / currentPrice

      default:
        return (config.maxPositionSize * portfolioValue) / currentPrice
    }
  }
}

/**
 * Default risk manager implementation
 */
export class DefaultRiskManager implements RiskManager {
  /**
   * Check if trade should be executed based on risk rules
   */
  public shouldExecuteTrade(
    signal: GeneratedSignal,
    currentPrice: number,
    portfolio: PortfolioSnapshot,
    config: BacktestConfig
  ): boolean {
    // Check maximum drawdown
    if (portfolio.drawdown > config.riskManagement.maxDrawdown!) {
      return false
    }

    // Check available cash
    const positionValue = config.maxPositionSize * portfolio.totalValue
    if (positionValue > portfolio.cash) {
      return false
    }

    // Check signal confidence threshold (optional)
    if (signal.confidence < 0.5) {
      return false
    }

    return true
  }

  /**
   * Calculate stop loss and take profit levels
   */
  public calculateExitLevels(
    entryPrice: number,
    direction: 'LONG' | 'SHORT',
    config: RiskManagementConfig
  ): { stopLoss?: number; takeProfit?: number } {
    const result: { stopLoss?: number; takeProfit?: number } = {}

    if (config.stopLoss) {
      if (direction === 'LONG') {
        result.stopLoss = entryPrice * (1 - config.stopLoss)
      } else {
        result.stopLoss = entryPrice * (1 + config.stopLoss)
      }
    }

    if (config.takeProfit) {
      if (direction === 'LONG') {
        result.takeProfit = entryPrice * (1 + config.takeProfit)
      } else {
        result.takeProfit = entryPrice * (1 - config.takeProfit)
      }
    }

    return result
  }

  /**
   * Check if position should be closed due to risk management
   */
  public shouldClosePosition(
    trade: TradeRecord,
    currentPrice: number,
    config: RiskManagementConfig
  ): boolean {
    const exitLevels = this.calculateExitLevels(trade.entryPrice, trade.direction, config)

    // Check stop loss
    if (exitLevels.stopLoss) {
      if (trade.direction === 'LONG' && currentPrice <= exitLevels.stopLoss) {
        return true
      }
      if (trade.direction === 'SHORT' && currentPrice >= exitLevels.stopLoss) {
        return true
      }
    }

    // Check take profit
    if (exitLevels.takeProfit) {
      if (trade.direction === 'LONG' && currentPrice >= exitLevels.takeProfit) {
        return true
      }
      if (trade.direction === 'SHORT' && currentPrice <= exitLevels.takeProfit) {
        return true
      }
    }

    return false
  }
}

/**
 * Default market simulator implementation
 */
export class DefaultMarketSimulator implements MarketSimulator {
  /**
   * Simulate order execution with realistic costs
   */
  public executeOrder(
    signal: GeneratedSignal,
    currentPrice: number,
    size: number,
    config: ExecutionConfig
  ): {
    executedPrice: number
    executedSize: number
    commission: number
    slippage: number
    executionTime: number
  } {
    // Simulate fill probability
    const fillRandom = Math.random()
    const executedSize =
      fillRandom <= config.fillProbability ? size : config.allowPartialFills ? size * fillRandom : 0

    // Calculate slippage
    const slippageAmount = this._calculateSlippage(currentPrice, size, config)
    const executedPrice =
      signal.signal === 'BUY' ? currentPrice + slippageAmount : currentPrice - slippageAmount

    // Calculate commission
    const tradeValue = executedPrice * executedSize
    const commission = tradeValue * 0.001 // 0.1% commission

    return {
      executedPrice,
      executedSize,
      commission,
      slippage: slippageAmount * executedSize,
      executionTime: Date.now() + config.executionDelay
    }
  }

  /**
   * Calculate market impact based on order size
   */
  public calculateMarketImpact(
    orderSize: number,
    marketVolume: number,
    config: ExecutionConfig
  ): number {
    const volumeRatio = orderSize / marketVolume

    switch (config.marketImpact) {
      case 'none':
        return 0

      case 'linear':
        return volumeRatio * 0.001 // 0.1% impact per 100% of volume

      case 'square_root':
        return Math.sqrt(volumeRatio) * 0.0005 // Square root impact

      default:
        return 0
    }
  }

  /**
   * Calculate slippage for order execution
   */
  private _calculateSlippage(currentPrice: number, size: number, config: ExecutionConfig): number {
    // Base slippage
    let slippage = currentPrice * 0.0005 // 0.05% base slippage

    // Add random component
    slippage += currentPrice * (Math.random() - 0.5) * 0.001 // ±0.05% random

    // Size-based slippage
    const sizeMultiplier = Math.log(1 + size / 1000) // Logarithmic size impact
    slippage *= sizeMultiplier

    return Math.abs(slippage)
  }
}

/**
 * Factory function to create a backtesting engine
 */
export const createBacktestEngine = (
  positionSizer?: PositionSizer,
  riskManager?: RiskManager,
  marketSimulator?: MarketSimulator
): BacktestingEngine => {
  return new BacktestingEngine(positionSizer, riskManager, marketSimulator)
}
